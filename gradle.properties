org.gradle.jvmargs=-Xmx5120m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.daemon=true
GROUP=com.meituan.android.hades

VERSION_NAME=0.1.1782.2
ADAPTER_VERSION_NAME=12.36.400

POM_DESCRIPTION=hades
# ä¸è¾¹çé½å¯ä»¥é»è®¤ä¸ºç©ºï¼ä½æ¯å¿é¡»æè¿äºåé
POM_URL=
POM_SCM_URL=
POM_SCM_CONNECTION=
POM_SCM_DEV_CONNECTION=
POM_LICENCE_NAME=
POM_LICENCE_URL=
POM_LICENCE_DIST=
POM_DEVELOPER_ID=
POM_DEVELOPER_NAME=
POM_PACKAGING=aar
PIXEL_RELEASE_REPOSITORY_URL=http://pixel.sankuai.com/repository/releases/
PIXEL_SNAPSHOT_REPOSITORY_URL=http://pixel.sankuai.com/repository/snapshots/
USER=deployment
PASSWORD=deployment123
# æ¯å¦ä½¿ç¨pin-baseåºæ¬å°ä¾èµ
IS_USE_PIN_BASE_LOCAL=false
# pin-baseåºæ¬å°ä¾èµï¼å¯ä»¥æ ¹æ®æ¯ä¸ªäººçéç½®æ¹å¨
PIN_BASE_LOCATION=../mt-pin-base
#Libraryççæ¬å·
LIBRARY_VERSION=1.0.508-prod@aar
#qqDynLoaderççæ¬å·
QQDYNLOADER_VERSION=1.0.498-prod@aar
HPX_JDK_VERSION=8
android.injected.testOnly=false

# Soda sourcecode dependency for local development
SODA_VERSION=0.0.21-archiveprod@aar

BOSS_WIFI_VERSION=1.0.1.177-mt@aar