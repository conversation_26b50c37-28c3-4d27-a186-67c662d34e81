apply plugin: 'com.android.library'
apply plugin: 'maven-publish'
apply from: file('../dependencies_base_component.gradle')


android {
    compileSdkVersion 30
    buildToolsVersion '30.0.3'

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 30
        versionCode 1
        versionName "1.0"
        buildConfigField "String", "pinVersion", "\"$VERSION_NAME\""
        buildConfigField("String", "ADAPTER_VERSION_NAME", "\"${ADAPTER_VERSION_NAME}\"")

        consumerProguardFiles "consumer-rules.pro"
        vectorDrawables.useSupportLibrary = true

    }


    compileOptions {
        targetCompatibility JavaVersion.VERSION_1_8
        sourceCompatibility JavaVersion.VERSION_1_8
    }


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    flavorDimensions "default"
    productFlavors {
        dev {
            dimension 'default'
            buildConfigField "boolean", "LOG_ENABLE", "true"
            // 开发环境支持 mock AdapterVersion，外层包 StringBuilder 可以避免字符串常量在编译时被inline
            buildConfigField("String", "ADAPTER_VERSION_NAME", "new StringBuilder(\"${ADAPTER_VERSION_NAME}\").toString()")

            manifestPlaceholders = [
                //wifi碰撞协议相关
                hades_sport_version: 1
            ]
        }
        prod {
            dimension 'default'
            buildConfigField "boolean", "LOG_ENABLE", "false"
            buildTypes {
                release {
                    minifyEnabled true
                    proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
                }
            }
            manifestPlaceholders = [
                //wifi碰撞协议相关
                hades_sport_version: 1
            ]
        }
        minify {
            dimension 'default'
            buildConfigField "boolean", "LOG_ENABLE", "false"
            if (project.hasProperty("minify")) {
                buildTypes {
                    release {
                        minifyEnabled true
                        proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
                    }
                }
            }
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }


    libraryVariants.configureEach { variant ->
        def variantName = variant.name.replace("minify", "").capitalize() // 处理变体名称
        def taskName = "generateAssetImages${variantName}"
        def outputDir = file("$buildDir/generated/source/assetImages/${variant.name}")

        // 使用 tasks.register 创建任务
        tasks.register(taskName) {
            doLast {
                // 定位 assets/custom_assert 目录
                def assetsDir = variant.sourceSets.find { it.name == 'main' }.assets.srcDirs.first()
                def customAssetsDir = new File(assetsDir, "custom_assets") // 定义 customAssetsDir

                // 收集图片文件名
                def imageNames = []
                if (customAssetsDir.exists()) {
                    // 遍历 custom_assert 下的所有子目录
                    customAssetsDir.eachDir { subDir ->
                        subDir.eachFile { file ->
                            if (file.isFile()) {
                                imageNames.add(file.name)
                            }
                        }
                    }
                }
                println "basd-------------"
                // 生成Java类
                def packageName = "com.meituan.hades.image"
                def className = "AssetImages"
                def javaFile = new File(outputDir, "${packageName.replace('.', '/')}/${className}.java")
                javaFile.parentFile.mkdirs()
                javaFile.text = """
package ${packageName};

import java.util.Arrays;
import java.util.List;
public class ${className} {
    public static final List<String> NAMES = Arrays.asList(
        ${imageNames.collect { "\"$it\"" }.join(",\n        ")}
    );
}
"""
            }
        }

        // 注册生成任务到变体
       variant.registerJavaGeneratingTask(tasks[taskName], outputDir)
    }
}

configurations {
    // babel, crashreporter, snare, metrics, sniffer融合
    all*.exclude group: 'com.meituan.android.common', module: 'babel'
    all*.exclude group: 'com.meituan.android.snare', module: 'snare'
    all*.exclude group: 'com.meituan.android.crashreporter', module: 'library'
    all*.exclude group: 'com.meituan.metrics', module: 'metrics'
    all*.exclude group: 'com.meituan.android.sniffer'
    all*.exclude group: 'com.meituan.android.dynamiclayout', module: 'adapters'
}

dependencies {

    //容器版本对齐  团app版本12.11.400
    // KNB--------------------------------
    // 小礼包组件
    api("com.meituan.android.knb:titans:21.0.1") {
        force = true
    }
    // 新框架接口层
    api("com.meituan.android.knb:titans-protocol:21.0.1") {
        force = true
    }
    // 新框架核心实现层
    api("com.meituan.android.knb:titans-base:21.0.1") {
        force = true
    }
    // 新框架的配置层
    api("com.meituan.android.knb:titans-config:21.0.1") {
        force = true
    }
    // 新框架的桥（主要是新框架的UI桥）
    api("com.meituan.android.knb:titans-bridges-base:21.0.1") {
        force = true
    }
    // 接入方通用适配逻辑
    api("com.meituan.android.knb:titans-adapter-base:21.0.1") {
        force = true
    }

    api('com.meituan.android.knb:titans-knbweb-delegate:21.0.3') {
        force = true
    }

    api('com.meituan.android.knb:knb-web:13.0.3') {
        force = true
    }

    api('com.meituan.android.knb:titans-adapter-mtapp:20.12.18.201') {
        force = true
        exclude group: 'com.meituan.android.knb', module: 'offline-titans-adapter'
//        exclude group: 'com.meituan.android.mtwebkit' , module: 'library'
    }
    api('com.meituan.android.knb:offline-debug-adapter:1.0.1') {
        force = true
    }
    api('com.meituan.android.knb:offline-titans-adapter:3.0.3@aar') {
        force = true
    }

    api('com.sankuai.waimai:knb-mt:8.20.1-mt')

    //KNB-----------------------------------------

    //MRN-----------------------------------------
    api('com.meituan.android.mrn:mrn:3.1216.402') {
        force = true
    }
    implementation ("com.meituan.android.common.metricx:metricx:12.27.208") {
        force = true
    }
    //以下是metricx依赖的日志上报组件和工具类，请尽量保持版本号一致，如果无法保持一致，请跟客服确认版本
    implementation ("com.meituan.android.common.metricx:babel:4.17.10"){
        force = true
    }
    implementation ("com.meituan.android.common.metricx:utils:12.27.208"){
        force = true
    }
    implementation ("com.meituan.android.common.metricx:base:12.27.208"){
        force = true
    }
    implementation("com.meituan.android.common.metricx:nativetools:12.27.208") {
        force = true
    }
    //MRN-----------------------------------------

    //MSC-----------------------------------------
    api('com.meituan.android.msc:library:1.38.2') {
        force = true
    }
    //MSC-----------------------------------------

    //DSP-----------------------------------------
    api('com.sankuai.meituan.mbc:core:0.3.111') {
        force = true
        transitive = false
    }
    api('com.sankuai.meituan.mbc:business:0.3.111') {
        force = true
        transitive = false
    }
    api('com.sankuai.meituan.mbc:dsp-core:0.1.138') {
        force = true
        transitive = false
    }
    api('com.sankuai.meituan.mbc:dsp-business:0.1.138') {
        force = true
        transitive = false
    }
    //DSP-----------------------------------------

    implementation fileTree(dir: "libs", include: ["*.jar"])

    implementation project(':interface')
    implementation project(':dydex')

    /** 配置下发 **/
    implementation 'com.meituan.android.common:horn:0.4.0.36-mt'
    // 短视频bugfix
    implementation( 'com.sankuai.meituan.base-homepage:library:0.0.41.85501056.2') {
        force = true
    }
    implementation('com.sankuai.meituan.mtlive:gamevideo-business:12.35.200.4') {
        force = true
        transitive = false
    }
    implementation 'com.qq.e.union:union:4.611.1481'


    /** 统一线程 **/
    implementation('com.sankuai.android.jarvis:library:0.1.38')
    api('com.sankuai.meituan.pylon:netsingleton:3.2.3') {
        exclude group: 'com.android.support', module: 'metrics'
    }

    implementation('com.meituan.passport:basemodule:5.103.21') {
        exclude group: 'com.android.support', module: 'appcompat-v7'
    }

    implementation('com.meituan.android.aurora:core:1.3.83') {
        force = true
    }
    implementation('com.google.code.gson:gson:2.8.2')
    implementation('com.sankuai.meituan.pylon:snackbar:0.0.28')
    implementation('com.meituan.android.common.analyse:library:4.97.2')
    implementation('com.squareup.picasso:picasso:10000.0.0.152.14')
    implementation('com.google.android:flexbox:1.0.0')
    implementation('com.sankuai.meituan.pylon:ui:3.0.2')


    implementation('com.sankuai.meituan.retrofit2:callfactory-okhttp3:1.10.2')

    implementation('com.meituan.android.loader:dynloader-interface:1.1.6')
    implementation('com.meituan.android.loader:dynloader:1.1.6')

    implementation("com.meituan.android.common.metricx:metricx:12.27.208")
    implementation 'com.meituan.metrics:apache-tracker:4.8.6'
    implementation 'com.meituan.metrics:okhttp3-tracker:4.8.6'
    implementation 'com.meituan.metrics:shark-tracker:4.8.6'
    implementation "com.meituan.android.common.metricx:babel:4.17.10"
    implementation("com.meituan.android.common.metricx:utils:12.27.208")
    implementation("com.meituan.android.common.metricx:base:12.27.208")

    implementation('com.meituan.android.abtest:abtestv2:0.0.0.31') {
        force = true
    }

    implementation 'com.sankuai.meituan.serviceloader:serviceloader:2.2.33'
    implementation 'com.sankuai.meituan.serviceloader:annotation:2.2.30'
    annotationProcessor 'com.sankuai.meituan.serviceloader:processor:2.1.0.12'

    implementation('com.sankuai.meituan.pylon:base:2.0.14')
    implementation('com.sankuai.meituan.pylon:basemodule:3.0.22')

    implementation('com.tencent.mm.opensdk:wechat-sdk-android-without-mta:6.8.0')

    implementation('com.meituan.android.privacy:interface:0.7.6')
    implementation 'com.meituan.android.privacy:impl:0.7.6'

    implementation('com.sankuai.meituan:buildconfig:8.6.0.0')

    implementation('com.meituan.met.mercury:load:1.3.13')
    implementation('com.meituan.uuid:library:8.9.3')
    implementation('com.meituan.android.downloadmanager:library:2.1.19') {
        force = true
    }
    implementation('com.meituan.android.mtguard:mtguard:6.5.0')
    implementation('com.oppo.hapjs:channel-sdk:1.4.10.7')

    implementation('com.meituan.android.common:utils:3.0.35') {
        force = true
    }
    implementation('com.sankuai.meituan.router:router:3.0.0.76') {
        force = true
    }
    compileOnly('com.meituan.android.uptodate:library:2.1.33')
    if (IS_USE_PIN_BASE_LOCAL.equalsIgnoreCase('true')) {
        implementation project(':qqdynloader')
        implementation project(':library')
    } else {
        implementation("com.meituan.android.qbase:library:$project.LIBRARY_VERSION")
        implementation("com.meituan.android.qbase:qqdynloader:$project.QQDYNLOADER_VERSION") {
            force = true
        }
    }

    implementation project(':eat')

    implementation('com.dianping.android.sdk:nvnetwork:7.0.14-mt') {
        force = true
    }
    implementation('com.meituan.android.mtpush:library:11.14.1') {
        force = true
    }
    implementation('com.meituan.android.terminus:library:10.0.13') {
        force = true
        exclude group: 'com.meituan.android.common', module: 'UUIDPermission'
        exclude group: 'com.meituan.android.common.candy', module: 'candyandroid'
        exclude group: 'com.github.handmark.pulltorefresh', module: 'pulltorefresh-library'
    }
    implementation('com.meituan.android.msi:msi-lx:12.13.200') {
        force = true
    }
    implementation('com.meituan.android.cipstorage:library:1.0.2-embed') {
        force = true
    }
    implementation 'com.sankuai.waimai:manipulator-annotation:0.3.28'
    implementation('com.meituan.android.aurora:startup:0.0.17') {
        force = true
    }
    implementation('com.meituan.android.common:unionid:2.0.19-c') {
        force = true
    }

    //启动任务
    compileOnly('com.meituan.android.launcher:library:12.27.207') {
        force = true
        transitive = false
    }
    implementation('com.meituan.android.dynamiclayout:library:12.25.400.2') {
        force = true
    }
    implementation ('com.meituan.android.lithodynamic:library:12.25.400.2-litho') {
        force = true
    }

    //秒开初始化
    implementation('com.meituan.android.common.metricx:fsp:3.21.8') {
        force = true
    }

    implementation("com.meituan.android.pin:mt-pin-bosswifi:$project.BOSS_WIFI_VERSION")
    implementation("com.meituan.android.pin:pin-bosswifi-biz:$project.BOSS_WIFI_VERSION")
    //特团卡片
    compileOnly('com.dianping.android.sdk:mrnmodule:6.0.92') {
        force = true
        transitive = false
    }
    //S3存储
    compileOnly('com.meituan.android.mss:mss-android:2.0.10-mt') {
        force = true
    }
    implementation ('com.meituan.android.floatlayer:library:12.27.200.1') {
        force = true
    }

    compileOnly "com.meituan.android.hades.soda:soda:0.0.21-archiveprod"
}

//    task sourceJar(type: Jar) {
//        from android.sourceSets.main.java.srcDirs
//        classifier "sources"
//    }
//
//    afterEvaluate {
//        publishing {
//            publications {
//                impl(MavenPublication) {
//                    groupId = "com.meituan.android.hades"
//                    artifactId = "impl"
//                    version = "$VERSION_NAME"
//                    artifact(sourceJar)
//                    artifact("$buildDir/outputs/aar/impl-dev-debug.aar")
//                }
//
//
//            }
//        }
//    }
