package com.meituan.android.hades.impl;

import static org.junit.Assert.assertEquals;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;

import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

public class GsonSerializeTest {

    public static final class GsonData {
        @SerializedName("name")
        public String name = "x";

        @SerializedName("age")
        public int age = 10;

        @SerializedName("extra")
        public HashMap<String, Object> extra = new HashMap<>();

        @Override
        public String toString() {
            return "GsonData{" +
                    "name='" + name + '\'' +
                    ", age=" + age +
                    ", extra=" + extra +
                    '}';
        }
    }

    @Test
    public void gsonSerialize() {
        Gson gson = new Gson();

        deserialization(gson);

        GsonData data = new GsonData();
        data.name = "aaa";
        data.age = 100;
        data.extra.put("v2", "abc");
        data.extra.put("v3", false);
        // data.extra.put("v1", 22233);
        data.extra.put("v1", 22233.0);
        data.extra.put("v4", 27.5);
        data.extra.put("v5", 27.8);

        String jsonString = gson.toJson(data);
        System.out.println(jsonString);

        GsonData jsonData = gson.fromJson(jsonString, GsonData.class);
        System.out.println(jsonData);

        assertEquals(data.toString(), jsonData.toString());
    }

    private void deserialization(Gson gson) {
        // json 中的 int 也会变成 java.lang.Double 类型.
        System.out.println("---------------");
        String json = "{\"name\":\"wei\",\"age\":30,\"extra\":{\"v1\":100,\"v2\":\"txt\",\"v3\":false,\"v4\":27.5,\"v5\":27.5,\"ex2\":{\"ex21\":210,\"ex22\":\"apk\"}}}";
        GsonData jsonData = gson.fromJson(json, GsonData.class);
        System.out.println(jsonData);

        for (Map.Entry<String, Object> entry : jsonData.extra.entrySet()) {
            System.out.println("key=" + entry.getKey() + ", value=" + entry.getValue() + ", type=" + entry.getValue().getClass());
        }
        System.out.println("---------------");
    }
}
