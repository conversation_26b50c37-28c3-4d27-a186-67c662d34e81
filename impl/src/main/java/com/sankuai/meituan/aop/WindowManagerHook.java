package com.sankuai.meituan.aop;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import com.meituan.android.hades.impl.guard.AbilityGuardManager;
import com.meituan.android.hades.impl.guard.FloatWindowGuard;
import com.meituan.android.hades.impl.window.FloatWindowController;
import com.sankuai.waimai.manipulator.annotation.CallSiteReplacement;

/**
 * 悬浮窗管控 hook
 * 根据包名判断是否拦截悬浮窗显示
 */
public class WindowManagerHook {

    private static final String TAG = "WindowManagerHook";

    @CallSiteReplacement(targetClass = "android.view.WindowManager", methodName = "addView", exclude = {"com.meituan.android.hades.impl.guard.*", "com.meituan.android.pin.bosswifi.biz.*"})
    public static void hookAddView(WindowManager target, View view, ViewGroup.LayoutParams layoutParams) {

        StackTraceElement[] stackTraceElements = Thread.currentThread().getStackTrace();
        boolean afterCurrentFlag = false;
        StackTraceElement invoker = null;
        for (StackTraceElement element : stackTraceElements) {
            if (TextUtils.equals(element.getClassName(), WindowManagerHook.class.getName())) {
                afterCurrentFlag = true;
            } else {
                if (afterCurrentFlag) {
                    invoker = element;
                    break;
                }
            }
        }
        String invokerClassName = "";
        if (invoker != null) {
            invokerClassName = invoker.getClassName();
        }

        if (AbilityGuardManager.enableAbilityGuard()) { // 走的新的管控逻辑
            FloatWindowGuard.addViewForHook(target, view, layoutParams, invokerClassName);
        } else {    // 根据调用者包名判定是否需要管控
            internalAddView(target, view, layoutParams, invokerClassName);
        }
    }

    public static void internalAddView(WindowManager target, View view, ViewGroup.LayoutParams layoutParams, String invokerClassName) {
        int windowType = WindowManager.LayoutParams.TYPE_PHONE;
        if (layoutParams instanceof WindowManager.LayoutParams) {
            windowType = ((WindowManager.LayoutParams) layoutParams).type;
        }
        // 判断是否需要拦截此次悬浮窗显示
        boolean intercept = FloatWindowController.needIntercept(invokerClassName, windowType);
        if (!intercept) {
            target.addView(view, layoutParams);
        }
        FloatWindowController.reportControlInfo(intercept, invokerClassName, windowType);
    }

}