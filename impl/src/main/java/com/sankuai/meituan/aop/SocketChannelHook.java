package com.sankuai.meituan.aop;


import com.meituan.android.hades.metrics.interceptor.HadesSocketInterceptor;
import com.sankuai.waimai.manipulator.annotation.CallSiteReplacement;
import com.sankuai.waimai.manipulator.annotation.ClassLevelScope;

import java.io.IOException;
import java.net.SocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.SocketChannel;

/**
 * 耗电管控时，拦截SocketChannel相关网络操作，目前主要针对的是pike
 */
@ClassLevelScope(exclude = {"com.sankuai.meituan.aop.*",
        "com.dianping.nvtunnelkit.nio.NIOSelectorHelper",
        "com.dianping.nvnetwork.tunnel2.NIOSelectorHelper"
})
public final class SocketChannelHook {

    // write
    @CallSiteReplacement(
            targetClass = "java.nio.channels.SocketChannel",
            methodName = "write")
    public static int replaceSocketChannelWrite(final SocketChannel channel,
                                                final ByteBuffer buffer)
            throws IOException {

        HadesSocketInterceptor.throwHadesForbiddenExceptionIfNeeded(channel,
                "write(buffer)");

        return channel.write(buffer);
    }

    @CallSiteReplacement(
            targetClass = "java.nio.channels.SocketChannel",
            methodName = "write")
    public static long replaceSocketChannelWrite(final SocketChannel channel,
                                                 final ByteBuffer[] buffers,
                                                 final int offset,
                                                 final int length)
            throws IOException {

        HadesSocketInterceptor.throwHadesForbiddenExceptionIfNeeded(channel,
                "write([],i,i])");

        return channel.write(buffers , offset, length);
    }

    @CallSiteReplacement(
            targetClass = "java.nio.channels.SocketChannel",
            methodName = "write")
    public static long replaceSocketChannelWrite(final SocketChannel channel,
                                                 final ByteBuffer[] buffers)
            throws IOException {

        HadesSocketInterceptor.throwHadesForbiddenExceptionIfNeeded(channel,
                "write([])");

        return channel.write(buffers);
    }

    // read
    @CallSiteReplacement(
            targetClass = "java.nio.channels.SocketChannel",
            methodName = "read")
    public static int replaceSocketChannelRead(final SocketChannel channel,
                                                final ByteBuffer buffer)
            throws IOException {

        HadesSocketInterceptor.throwHadesForbiddenExceptionIfNeeded(channel,
                "read(buffer)");

        return channel.read(buffer);
    }

    @CallSiteReplacement(
            targetClass = "java.nio.channels.SocketChannel",
            methodName = "read")
    public static long replaceSocketChannelRead(final SocketChannel channel,
                                                 final ByteBuffer[] buffers,
                                                 final int offset,
                                                 final int length)
            throws IOException {

        HadesSocketInterceptor.throwHadesForbiddenExceptionIfNeeded(channel,
                "read([],i,i])");

        return channel.read(buffers , offset, length);
    }

    @CallSiteReplacement(
            targetClass = "java.nio.channels.SocketChannel",
            methodName = "read")
    public static long replaceSocketChannelRead(final SocketChannel channel,
                                                 final ByteBuffer[] buffers)
            throws IOException {

        HadesSocketInterceptor.throwHadesForbiddenExceptionIfNeeded(channel,
                "read([])");

        return channel.read(buffers);
    }

}
