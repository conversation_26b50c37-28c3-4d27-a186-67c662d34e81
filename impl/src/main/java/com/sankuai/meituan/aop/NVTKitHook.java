package com.sankuai.meituan.aop;

import com.dianping.nvnetwork.tunnel2.BaseTunnelConnection;
import com.dianping.nvtunnelkit.nio.NIOSelectorHelper;
import com.meituan.android.hades.metrics.interceptor.HadesSocketInterceptor;
import com.sankuai.waimai.manipulator.annotation.CallSiteReplacement;

import java.io.IOException;
import java.net.SocketAddress;
import java.nio.channels.SocketChannel;

public class NVTKitHook {

    @CallSiteReplacement(
            targetClass = "com.dianping.nvtunnelkit.nio.NIOSelectorHelper",
            methodName = "connect")
    public static SocketChannel replaceNIOSelectorHelperConnect(final NIOSelectorHelper helper,
                                                      final SocketAddress address) throws IOException {
        SocketChannel channel = helper.connect(address);
        HadesSocketInterceptor.throwHadesForbiddenExceptionIfNeeded(channel, address, "connect(address)");
        HadesSocketInterceptor.onConnect(channel);
        return channel;
    }

    @CallSiteReplacement(
            targetClass = "com.dianping.nvnetwork.tunnel2.NIOSelectorHelper",
            methodName = "connect")
    public static SocketChannel replaceNIOSelectorHelperConnect2(final com.dianping.nvnetwork.tunnel2.NIOSelectorHelper helper,
                                                            final BaseTunnelConnection connection) throws IOException {
        SocketChannel channel = helper.connect(connection);
        HadesSocketInterceptor.throwHadesForbiddenExceptionIfNeeded(channel, null, "connect(tunnelCon)");
        HadesSocketInterceptor.onConnect(channel);
        return channel;
    }
}
