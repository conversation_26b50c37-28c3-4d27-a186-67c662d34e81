package com.sankuai.meituan.aop;

import static com.meituan.android.cipstorage.CIPStorageCenter.MODE_MULTI_PROCESS;

import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProvider;
import android.appwidget.AppWidgetProviderInfo;
import android.content.ComponentName;
import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import com.meituan.android.cipstorage.CIPStorageCenter;
import com.meituan.android.common.babel.Babel;
import com.meituan.android.hades.eat.Utils;
import com.meituan.android.hades.impl.config.PinCommonServiceConfig;
import com.meituan.android.hades.impl.model.PinCommonConfigData;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.HadesWidgetUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.sankuai.waimai.manipulator.annotation.HookMethodEntry;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对AppWidgetProvider及所有子类进行插桩，监控Widget的行为
 */
public class AppWidgetProviderHook {
    private static final String TAG = "AppWidgetProviderHook";

    //CIP存储的key，存储每一种widget的上一次上报onEnabled事件的时间
    private static final String KEY_WIDGET_REPORT_ENABLE_TIME = "key_widget_rpt_enable_tm_";
    //存储每一种widget的上一次上报onUpdate事件的时间
    private static final String KEY_WIDGET_REPORT_UPDATE_TIME = "key_widget_rpt_update_tm_";
    //存储每一种widget的上一次上报onDeleted事件的时间
    private static final String KEY_WIDGET_REPORT_DELETE_TIME = "key_widget_rpt_delete_tm_";
    //存储每一种widget的上一次上报onDisabled事件的时间
    private static final String KEY_WIDGET_REPORT_DISABLE_TIME = "key_widget_rpt_disable_tm_";
    //存储每一种widget在桌面安装的数量
    private static final String KEY_WIDGET_COUNT = "key_widget_count_";
    //存储上一次读取桌面Widget列表的时间
    private static final String KEY_REFRESH_WIDGET_TIME = "key_refresh_widget_tm";

    //Babel上报的tag
    public final static String KEY_WIDGET_MONITOR = "key_widget_monitor";

    //Widget行为的事件类型
    public static final String EVENT_TYPE_ENABLE = "onEnabled";
    public static final String EVENT_TYPE_UPDATE = "onUpdate";
    public static final String EVENT_TYPE_DELETE = "onDeleted";
    public static final String EVENT_TYPE_DISABLE = "onDisabled";
    //通过数量变化判断出安装了widget
    public static final String EVENT_TYPE_COUNT_INSTALL = "count_install";
    //通过数量变化判断出卸载了widget
    public static final String EVENT_TYPE_COUNT_DELETE = "count_delete";

    //给上报onUpdate事件的时间加个缓存，避免频繁调用CIP造成ANR
    private static final Map<String, Long> updateTimeCache = new HashMap<>();

    /**
     * 是否进行widget行为监控
     * 由后端配置控制
     */
    private static boolean isHookEnabled() {
        PinCommonConfigData pinCommonConfigData = PinCommonServiceConfig.getInstance().getConfigCacheForMemoryOrDisk(HadesUtils.getContext());
        if (pinCommonConfigData != null && !pinCommonConfigData.isAppWidgetProviderHookEnable()) {
            //后端下发0则关闭监控
            return false;
        }
        //默认开启
        return true;
    }

    /**
     * 插桩AppWidgetProvider及所有子类的onEnabled方法，在方法执行前增加监控代码
     * 同一个AppWidgetProvider，在安装第一个组件时会回调onEnabled，后面安装多个时不再回调
     */
    @HookMethodEntry(className = "android.appwidget.AppWidgetProvider+",
            methodName = "onEnabled",
            methodDesc = "(android.content.Context)")
    public static void hookOnEnabled(AppWidgetProvider appWidgetProvider, Context context) {
        if (isHookEnabled()) {
            String widgetClassName = appWidgetProvider.getClass().getName();
            Logger.d(TAG, "hookOnEnabled: " + widgetClassName);
            CIPStorageCenter cip = getCIP(HadesUtils.getContext());
            long lastReportTime = cip.getLong(KEY_WIDGET_REPORT_ENABLE_TIME + widgetClassName, 0);
            if (Math.abs(System.currentTimeMillis() - lastReportTime) > 1000) { //会瞬间回调多次，加个防抖
                try {
                    reportWidgetBabel(widgetClassName, EVENT_TYPE_ENABLE);
                    //更新上报时间
                    cip.setLong(KEY_WIDGET_REPORT_ENABLE_TIME + widgetClassName, System.currentTimeMillis());
                } catch (Exception e) {
                }
            }
        }
    }

    /**
     * 插桩AppWidgetProvider及所有子类的onUpdate方法，在方法执行前增加监控代码
     * 安装Widget成功后会立即执行onUpdate回调
     */
    @HookMethodEntry(className = "android.appwidget.AppWidgetProvider+",
            methodName = "onUpdate",
            methodDesc = "(android.content.Context,android.appwidget.AppWidgetManager,int[])")
    public static void hookOnUpdate(AppWidgetProvider appWidgetProvider, Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        if (isHookEnabled()) {
            String widgetClassName = appWidgetProvider.getClass().getName();
            Logger.d(TAG, "hookOnUpdate: " + widgetClassName);
            if (!isReportedUpdateToday(widgetClassName)) {
                try {
                    //当天未上报过，进行上报
                    reportWidgetBabel(widgetClassName, EVENT_TYPE_UPDATE);
                    //更新上报时间
                    long currentTime = System.currentTimeMillis();
                    getCIP(HadesUtils.getContext()).setLong(KEY_WIDGET_REPORT_UPDATE_TIME + widgetClassName, currentTime);
                    updateTimeCache.put(widgetClassName, currentTime);
                } catch (Exception e) {
                }
            }
            //检查一下widget数量变化，上报安装事件
            refreshWidgetCount();
        }
    }

    /**
     * 插桩AppWidgetProvider及所有子类的onDeleted方法，在方法执行前增加监控代码
     * 同一个AppWidgetProvider有多个组件时，删除每一个组件都会回调
     */
    @HookMethodEntry(className = "android.appwidget.AppWidgetProvider+",
            methodName = "onDeleted",
            methodDesc = "(android.content.Context,int[])")
    public static void hookOnDeleted(AppWidgetProvider appWidgetProvider, Context context, int[] appWidgetIds) {
        if (isHookEnabled()) {
            String widgetClassName = appWidgetProvider.getClass().getName();
            Logger.d(TAG, "hookOnDeleted: " + widgetClassName);
            CIPStorageCenter cip = getCIP(HadesUtils.getContext());
            long lastReportTime = cip.getLong(KEY_WIDGET_REPORT_DELETE_TIME + widgetClassName, 0);
            if (Math.abs(System.currentTimeMillis() - lastReportTime) > 1000) { //会瞬间回调多次，加个防抖
                try {
                    reportWidgetBabel(widgetClassName, EVENT_TYPE_DELETE);
                    //更新上报时间
                    cip.setLong(KEY_WIDGET_REPORT_DELETE_TIME + widgetClassName, System.currentTimeMillis());
                    //删除后也清理一下update事件的上报时间，之后允许再报update
                    cip.setLong(KEY_WIDGET_REPORT_UPDATE_TIME + widgetClassName, 0);
                    updateTimeCache.remove(widgetClassName);
                } catch (Exception e) {
                }
            }
            //检查一下widget数量变化，上报安装事件
            refreshWidgetCount();
        }
    }

    /**
     * 插桩AppWidgetProvider及所有子类的onDisabled方法，在方法执行前增加监控代码
     * 同一个AppWidgetProvider有多个组件时，删除最后一个组件时才会回调
     */
    @HookMethodEntry(className = "android.appwidget.AppWidgetProvider+",
            methodName = "onDisabled",
            methodDesc = "(android.content.Context)")
    public static void hookOnDisabled(AppWidgetProvider appWidgetProvider, Context context) {
        if (isHookEnabled()) {
            String widgetClassName = appWidgetProvider.getClass().getName();
            Logger.d(TAG, "hookOnDisabled: " + widgetClassName);
            CIPStorageCenter cip = getCIP(HadesUtils.getContext());
            long lastReportTime = cip.getLong(KEY_WIDGET_REPORT_DISABLE_TIME + widgetClassName, 0);
            if (Math.abs(System.currentTimeMillis() - lastReportTime) > 1000) { //会瞬间回调多次，加个防抖
                try {
                    reportWidgetBabel(widgetClassName, EVENT_TYPE_DISABLE);
                    //更新上报时间
                    cip.setLong(KEY_WIDGET_REPORT_DISABLE_TIME + widgetClassName, System.currentTimeMillis());
                } catch (Exception e) {
                }
            }
        }
    }

    private static CIPStorageCenter getCIP(Context context) {
        return CIPStorageCenter.instance(context, "hades", MODE_MULTI_PROCESS);
    }

    /**
     * 判断当天是否上报过onUpdate事件，true表示上报过
     * 每种Widget的onUpdate和onDeleted事件一天只报一次即可
     */
    private static boolean isReportedUpdateToday(String widgetClassName) {
        try {
            //上一次的上报时间
            long lastReportTime;
            if (updateTimeCache.containsKey(widgetClassName)) {
                lastReportTime = updateTimeCache.get(widgetClassName);
            } else {
                lastReportTime = getCIP(HadesUtils.getContext()).getLong(KEY_WIDGET_REPORT_UPDATE_TIME + widgetClassName, 0);
                updateTimeCache.put(widgetClassName, lastReportTime);
            }
            if (lastReportTime == 0) {//从未上报过
                return false;
            } else return Utils.isToday(lastReportTime);
        } catch (Exception e) {
            Logger.d(TAG, "isReportedUpdateToday exception=" + e);
            //出现异常就返回true，避免频繁上报
            return true;
        }

    }

    /**
     * 将监控到的Widget事件上报到Babel日志
     */
    private static void reportWidgetBabel(String widgetClassName, String eventType, int desktopCount) {
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("widgetClassName", widgetClassName);
        paramsMap.put("eventType", eventType);
        paramsMap.put("deviceBrand", Build.BRAND);
        if (desktopCount >= 0) {
            paramsMap.put("desktopCount", desktopCount);
        }

        try {
            com.meituan.android.common.kitefly.Log.Builder logBuilder =
                    new com.meituan.android.common.kitefly.Log.Builder("");
            logBuilder.tag(KEY_WIDGET_MONITOR).optional(paramsMap).generalChannelStatus(true);
            Babel.log(logBuilder.build());
        } catch (Exception e) {
            Logger.d(TAG, "reportWidgetBabel exception=" + e +
                    ", map=" + paramsMap);
        }
    }

    //重载方法
    private static void reportWidgetBabel(String widgetClassName, String eventType) {
        reportWidgetBabel(widgetClassName, eventType, -1);
    }

    public static void refreshWidgetCount() {
        try {
            //防抖，避免瞬间调用多次，频繁读取系统Widget列表
            Context context = HadesUtils.getContext();
            CIPStorageCenter cip = getCIP(HadesUtils.getContext());
            long lastReportTime = cip.getLong(KEY_REFRESH_WIDGET_TIME, 0);
            if (Math.abs(System.currentTimeMillis() - lastReportTime) < 1000 * 5) { //5秒内最多刷新一次
                return;
            }
            cip.setLong(KEY_REFRESH_WIDGET_TIME, System.currentTimeMillis());

            //读取系统组件列表耗时，放在子线程中
            HadesUtils.runOnWorkThread(
                    () -> {
                        AppWidgetManager appWidgetManager = AppWidgetManager.getInstance(context);
                        // 获取所有已声明的 AppWidgetProvider 信息
                        List<AppWidgetProviderInfo> appWidgetProviderInfoList = appWidgetManager.getInstalledProviders();

                        for (AppWidgetProviderInfo providerInfo : appWidgetProviderInfoList) {
                            ComponentName provider = providerInfo.provider;
                            String providerPackageName = provider.getPackageName();
                            String providerClassName = provider.getClassName();
                            if (TextUtils.equals(providerPackageName, context.getPackageName())) {//只看本应用的Widget
                                int newCount = HadesWidgetUtils.getWidgetCount(context, providerClassName);
                                int oldCount = cip.getInteger(KEY_WIDGET_COUNT + providerClassName, 0);
                                if (newCount > oldCount) {
                                    reportWidgetBabel(providerClassName, EVENT_TYPE_COUNT_INSTALL, newCount);
                                    cip.setInteger(KEY_WIDGET_COUNT + providerClassName, newCount);
                                } else if (newCount < oldCount) {
                                    reportWidgetBabel(providerClassName, EVENT_TYPE_COUNT_DELETE, newCount);
                                    cip.setInteger(KEY_WIDGET_COUNT + providerClassName, newCount);
                                }
                            }
                        }
                    }
            );
        } catch (Exception e) {
            Logger.d(TAG, "refreshWidgetCount exception=" + e);
        }
    }
}
