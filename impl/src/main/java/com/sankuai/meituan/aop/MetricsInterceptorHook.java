package com.sankuai.meituan.aop;

import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.impl.utils.NetUrlUtil;
import com.meituan.android.hades.metrics.interceptor.ApacheHttpInterceptorHolder;
import com.meituan.android.hades.metrics.interceptor.HttpURLInterceptorHolder;
import com.meituan.android.hades.metrics.interceptor.OkHttp2InterceptorHolder;
import com.meituan.android.hades.metrics.interceptor.OkHttp3InterceptorHolder;
import com.meituan.android.hades.metrics.interceptor.SharkInterceptorHolder;
import com.sankuai.waimai.manipulator.annotation.HookMethodEntry;
import com.sankuai.waimai.manipulator.runtime.MethodEntryHook;

import java.net.URLConnection;

import okhttp3.Interceptor;
import okhttp3.Response;
import okhttp3.internal.connection.ConnectInterceptor;

public class MetricsInterceptorHook {

    @HookMethodEntry(className = "okhttp3.internal.connection.ConnectInterceptor",
            methodName = "intercept",
            methodDesc = "(okhttp3.Interceptor$Chain)")
    public static MethodEntryHook<Response> beforeOk3ConnectInterceptor(final ConnectInterceptor interceptor, final Interceptor.Chain chain) {
        final Response forbidResponse = OkHttp3InterceptorHolder.buildForbiddenResponseIfNeeded(chain.request());
        if(null != forbidResponse) {
            return MethodEntryHook.intercept(forbidResponse);
        }
        return MethodEntryHook.pass();
    }

    @HookMethodEntry(className = "com.squareup.okhttp.Call$ApplicationInterceptorChain",
            methodName = "proceed",
            methodDesc = "(com.squareup.okhttp.Request)")
    public static MethodEntryHook<com.squareup.okhttp.Response> beforeOk2Proceed(final Object applicationInterceptorChain, final com.squareup.okhttp.Request request) {
        final com.squareup.okhttp.Response forbidResponse = OkHttp2InterceptorHolder.buildForbiddenResponseIfNeeded(request);
        if (null != forbidResponse) {
            return MethodEntryHook.intercept(forbidResponse);
        }
        return MethodEntryHook.pass();
    }

    @HookMethodEntry(className = "com.meituan.metrics.traffic.reflection.SharkWrapper",
            methodName = "addInterceptorToBuilder",
            methodDesc = "(java.lang.Object)")
    public static void beforeSharkAddInterceptorToBuilder(final Object builder) {
        try {
            SharkInterceptorHolder.addInterceptorIfNeeded(builder);
        } catch (Throwable ignored) {
        }
    }

    @HookMethodEntry(className = "com.meituan.metrics.traffic.reflection.ApacheHttpWrapper",
            methodName = "addRequestAndResponseInterceptor",
            methodDesc = "(java.lang.Object)")
    public static void beforeApacheAddRequestInterceptor(final Object obj) {
        try {
            ApacheHttpInterceptorHolder.getInstance().addInterceptorIfNeeded(obj);
        } catch (Throwable ignored) {
        }
    }

    @HookMethodEntry(className = "com.meituan.metrics.traffic.hurl.HttpURLWrapper",
            methodName = "wrapURLConnection",
            methodDesc = "(java.net.URLConnection)")
    public static MethodEntryHook<URLConnection> beforeHttpWrapURLConnection(
            final URLConnection wrapped) {
        URLConnection newConnection = null;
        try{
            newConnection = HttpURLInterceptorHolder.wrapURLConnection(wrapped);
        }catch (Throwable ignored) {
            Logger.d("MetricsInterceptorHook", "httpWrapURLConnection connect error:" + ignored);
        }
        if (null != newConnection) {
            return MethodEntryHook.intercept(newConnection);
        }
        return MethodEntryHook.pass();
    }


}
