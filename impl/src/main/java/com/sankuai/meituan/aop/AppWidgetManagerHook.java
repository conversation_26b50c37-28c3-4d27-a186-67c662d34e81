package com.sankuai.meituan.aop;

import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.content.ComponentName;
import android.os.Build;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.common.babel.Babel;
import com.meituan.android.hades.impl.guard.AbilityGuardManager;
import com.meituan.android.hades.impl.guard.PinWidgetGuard;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.impl.widget.hook.AppWidgetManagerHookConfig;
import com.meituan.android.hades.impl.widget.hook.WidgetControlBrandData;
import com.sankuai.waimai.manipulator.annotation.CallSiteReplacement;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * AppWidgetManager的hook，主要hook requestPinAppWidget方法
 * 增加widget安装管控，只有在白名单中的widget才允许安装，widget类名作为白名单
 */
public final class AppWidgetManagerHook {
    private static final String TAG = "AppWidgetManagerHook";
    public static final String KEY_EXTRAS_TOKEN = "token";

    /**
     * 支持的widget列表本地白名单，widget类名采用|号分割
     * 支持包名和全类名配置白名单
     */
    @Deprecated
    public static final String WIDGET_LIST_WHITELIST = "com.meituan.android.hades.impl.widget" +
            "|com.meituan.android.walmai.widget";

    /**
     * 管控的品牌、系统版本(以正则表达式来匹配)
     * 即商务列表
     * JSON格式
     */
    public static final List<WidgetControlBrandData> CONTROL_BRAND_LIST_NEW;

    static {
        //给CONTROL_BRAND_LIST赋一个默认值
        List<WidgetControlBrandData> tempList = new ArrayList<>();
        tempList.add(new WidgetControlBrandData("vivo", ".*"));
        CONTROL_BRAND_LIST_NEW = tempList;
    }

    /**
     * 普通token列表，采用|号分隔
     * 使用此类token请求安装widget，弹出仿系统权限弹窗，确认后调用系统接口安装
     */
    public static final String NORMAL_REQUEST_TOKEN_LIST = "";

    //特殊Token给QQ内部使用，服务端可配置
    public static final String QQ_SPECIAL_REQUEST_TOKEN = "8fYB3Zp2eF4t9KZkQF2L6Q==";
    /**
     * 特殊token列表，采用|号分隔
     * 使用此类token请求安装widget，直接静默安装。
     * QQ使用此类
     */
    public static final String SPECIAL_REQUEST_TOKEN_LIST = "";

    private static final int TOKEN_TYPE_INVALID = 0;
    private static final int TOKEN_TYPE_NORMAL = 1;
    private static final int TOKEN_TYPE_SPECIAL = 2;

    //Babel上报的tag
    public static final String KEY_WIDGET_INTERCEPT = "key_widget_intercept";

    private static AppWidgetManagerHookConfig appWidgetManagerHookConfig;

    @CallSiteReplacement(
            targetClass = "android.appwidget.AppWidgetManager",
            methodName = "requestPinAppWidget",
            exclude = {"com.meituan.android.hades.impl.guard.*"}
    )
    public static boolean hookRequestPinAppWidget(
            AppWidgetManager appWidgetManager,
            ComponentName provider,
            Bundle extras,
            PendingIntent successCallback) {

        if (AbilityGuardManager.enableAbilityGuard()) {
            return PinWidgetGuard.requestPinAppWidgetForHook(appWidgetManager, provider, extras,
                    successCallback);
        }

        if (appWidgetManagerHookConfig == null) {
            appWidgetManagerHookConfig = HadesUtils.getAppWidgetManagerHookConfig();
        }
        Logger.d(TAG, "appWidgetManagerHookConfig:" + appWidgetManagerHookConfig);
        String widgetClassName = provider.getClassName();
        if (appWidgetManagerHookConfig == null
                || !appWidgetManagerHookConfig.enable
                || !isControlBrand() //不在管控列表中则不管控
        ) {
            Logger.d(TAG, "need not to control, isControlBrand=" + isControlBrand());
            reportWidgetControlBabel(widgetClassName, "notControl", "");
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                return appWidgetManager.requestPinAppWidget(provider, extras, successCallback);
            } else {
                return false;
            }
        }
        Logger.d(TAG, "need to hookRequestPinAppWidget, widgetClassName=" + widgetClassName);

        String requestToken = null;
        if (extras != null) {
            requestToken = extras.getString(KEY_EXTRAS_TOKEN);
        }
        switch (isTokenValid(requestToken)) {
            case TOKEN_TYPE_SPECIAL: {
                Logger.d(TAG, "token is special: " + requestToken);
                reportWidgetControlBabel(widgetClassName, "specialToken", requestToken);
                //静默安装
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    return appWidgetManager.requestPinAppWidget(provider, extras, successCallback);
                } else {
                    return false;
                }
            }
            case TOKEN_TYPE_NORMAL: {
                Logger.d(TAG, "token is normal: " + requestToken);
                reportWidgetControlBabel(widgetClassName, "normalToken", requestToken);
                //仿系统权限弹窗，确认后安装 TODO: 还没加弹窗流程
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    return appWidgetManager.requestPinAppWidget(provider, extras, successCallback);
                } else {
                    return false;
                }
            }
            default: {
                Logger.d(TAG, "token is invalid: " + requestToken);
                reportWidgetControlBabel(widgetClassName, "invalidToken", requestToken);
                //拦截
                return false;
            }
        }
    }

    public static List<WidgetControlBrandData> getControlBrandListNew() {
        AppWidgetManagerHookConfig appWidgetManagerHookConfig = HadesUtils.getAppWidgetManagerHookConfig();
        if (appWidgetManagerHookConfig == null) {
            return CONTROL_BRAND_LIST_NEW;
        }
        return appWidgetManagerHookConfig.controlBrandListNew;
    }

    public static String getNormalRequestTokenList() {
        AppWidgetManagerHookConfig appWidgetManagerHookConfig = HadesUtils.getAppWidgetManagerHookConfig();
        if (appWidgetManagerHookConfig == null) {
            return NORMAL_REQUEST_TOKEN_LIST;
        }
        return appWidgetManagerHookConfig.normalRequestTokenList;
    }

    public static String getSpecialRequestTokenList() {
        AppWidgetManagerHookConfig appWidgetManagerHookConfig = HadesUtils.getAppWidgetManagerHookConfig();
        if (appWidgetManagerHookConfig == null) {
            return SPECIAL_REQUEST_TOKEN_LIST;
        }
        return appWidgetManagerHookConfig.specialRequestTokenList;
    }

    public static String getQQSpecialRequestToken() {
        AppWidgetManagerHookConfig appWidgetManagerHookConfig = HadesUtils.getAppWidgetManagerHookConfig();
        if (appWidgetManagerHookConfig == null) {
            return QQ_SPECIAL_REQUEST_TOKEN;
        }
        return appWidgetManagerHookConfig.QQSpecialRequestToken;
    }

    /**
     * 是否在管控列表中，true表示需要管控。
     * 品牌名和系统版本号均匹配才管控
     */
    public static boolean isControlBrand() {
        if (appWidgetManagerHookConfig == null) {
            return false;
        }

        String curBrand = Build.BRAND.toLowerCase();
        String OSVersionName = Build.DISPLAY; //各厂商的定制操作系统的版本号，例如华为：JAD-AL00 4.2.0.152(C00E100R4P7)
        List<WidgetControlBrandData> controlBrandList = getControlBrandListNew();

        if (controlBrandList == null) {
            Logger.d(TAG, "controlBrandList is null");
            return false;
        } else {
            for (WidgetControlBrandData brandData : controlBrandList) {
                if (brandData != null && TextUtils.equals(brandData.brand, curBrand)) {
                    try {
                        Pattern pattern = Pattern.compile(brandData.versionRegex);
                        Matcher matcher = pattern.matcher(OSVersionName);
                        return matcher.matches();
                    } catch (Exception e) {
                        Logger.d(TAG, "VersionRegex match exception: " + e);
                    }
                }
            }
        }
        return false;
    }

    /**
     * 判断Token是否有效
     */
    public static int isTokenValid(@Nullable String token) {
        if (token == null) {
            return TOKEN_TYPE_INVALID;
        }
        if (token.equals(QQ_SPECIAL_REQUEST_TOKEN)) {
            return TOKEN_TYPE_SPECIAL;
        }
        String[] normalRequestTokenList = getNormalRequestTokenList().split("\\|");
        String[] specialRequestTokenList = getSpecialRequestTokenList().split("\\|");

        for (String specialRequestToken : specialRequestTokenList) {
            if (TextUtils.equals(specialRequestToken.trim(), token)) {
                return TOKEN_TYPE_SPECIAL;
            }
        }
        for (String normalRequestToken : normalRequestTokenList) {
            if (TextUtils.equals(normalRequestToken.trim(), token)) {
                return TOKEN_TYPE_NORMAL;
            }
        }
        return TOKEN_TYPE_INVALID;
    }

    /**
     * 将Widget管控行为上报到Babel日志
     */
    private static void reportWidgetControlBabel(String widgetClassName, String controlAction, @Nullable String requestToken) {
        final Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("widgetClassName", widgetClassName);
        paramsMap.put("controlAction", controlAction);
        paramsMap.put("requestToken", requestToken);
        paramsMap.put("deviceBrand", Build.BRAND);

        try {
            com.meituan.android.common.kitefly.Log.Builder logBuilder =
                    new com.meituan.android.common.kitefly.Log.Builder("");
            logBuilder.tag(KEY_WIDGET_INTERCEPT).optional(paramsMap).generalChannelStatus(true);
            Babel.log(logBuilder.build());
        } catch (Exception e) {
            Logger.d(TAG, "reportWidgetControlBabel exception=" + e +
                    ", map=" + paramsMap);
        }
    }
}
