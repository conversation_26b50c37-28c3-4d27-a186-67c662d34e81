package com.sankuai.meituan.aop;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.IBinder;
import android.os.Messenger;
import android.util.Log;

import com.meituan.android.hades.hap.BoardUtil;
import com.meituan.android.hades.impl.BuildConfig;
import com.meituan.android.hades.impl.config.PinCommonServiceConfig;
import com.meituan.android.hades.impl.model.PinCommonConfigData;
import com.meituan.android.hades.impl.report.HapH5CreateReporter;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.hap.HapChannelService;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.pin.Pin;
import com.meituan.android.pin.bosswifi.biz.report.ConnectReporter;
import com.meituan.android.pin.bosswifi.biz.statusbar.StatusBarInitializer;
import com.meituan.android.singleton.ApplicationSingleton;
import com.meituan.android.walmai.process.LaunchManager;
import com.sankuai.common.utils.ProcessUtils;
import com.sankuai.waimai.manipulator.annotation.HookMethodEntry;
import com.sankuai.waimai.manipulator.runtime.MethodEntryHook;

import org.hapjs.features.channel.ChannelService;

public class HapCSHook {

    private static final String TAG = "HapCSHook";
    private static boolean mUseSdkChannel = false;
    private static boolean mCanHook = true;

    @HookMethodEntry(
            className = "org.hapjs.features.channel.ChannelService",
            methodName = "onCreate",
            methodDesc = "()"
    )
    public static MethodEntryHook<Void> beforeOnCreate(ChannelService caller) {
        try {
            mCanHook = canHook();
            if (BuildConfig.LOG_ENABLE) {
                Log.d(TAG, "MethodEntryHook canHook:" + mCanHook);
            }
            if (mCanHook) {
                HapH5CreateReporter.reportHapChannelCreate(
                        HapH5CreateReporter.STAGE_CHANNEL_SERVICE_CREATE, TAG, null, null, null);
                mUseSdkChannel = useSdkChannel();
                if (mUseSdkChannel) {
                    return MethodEntryHook.pass();
                } else {
                    if (HadesUtils.isOPPO(HadesUtils.getContext())) {
                        BoardUtil.hapDelivery(HadesUtils.getContext());
                    }
                    return MethodEntryHook.intercept();
                }
            }
        } catch (Throwable t) {
            QQThrowableReporter.report("HapCSHook", t);
        }

        return MethodEntryHook.intercept();
    }

    @HookMethodEntry(
            className = "org.hapjs.features.channel.ChannelService",
            methodName = "onBind",
            methodDesc = "(android.content.Intent)"
    )
    public static MethodEntryHook<IBinder> beforeOnBind(ChannelService caller, Intent intent) {
        if (BuildConfig.LOG_ENABLE) {
            Log.d(TAG, "MethodEntryHook onBind");
        }
        try {
            if (mCanHook) {
                HapH5CreateReporter.reportHapChannelCreate(
                        HapH5CreateReporter.STAGE_HAP_CHANNEL_ON_BIND, TAG, null, null, null);
                if (mUseSdkChannel) {
                    return MethodEntryHook.pass();
                } else {
                    Handler messengerHandler = new HapChannelService.InnerHandler();
                    Messenger messenger = new Messenger(messengerHandler);
                    return MethodEntryHook.intercept(messenger.getBinder());
                }
            }
        } catch (Throwable t) {
            QQThrowableReporter.report("HapCSHook", t);
        }

        return MethodEntryHook.pass();
    }

    @HookMethodEntry(
            className = "org.hapjs.features.channel.ChannelService",
            methodName = "onDestroy",
            methodDesc = "()"
    )
    public static MethodEntryHook<Void> beforeOnDestroy(ChannelService caller) {
        if (BuildConfig.LOG_ENABLE) {
            Log.d(TAG, "MethodEntryHook onDestroy caller = " + caller);
        }
        try {
            if (mCanHook) {
                HapH5CreateReporter.reportHapChannelCreate(
                        HapH5CreateReporter.STAGE_HAP_CHANNEL_ON_DESTROY, TAG, null, null, null);
                if (mUseSdkChannel && caller != null && caller.c != null) {
                    return MethodEntryHook.pass();
                } else {
                    return MethodEntryHook.intercept();
                }
            }
            if (caller != null && caller.c != null) {
                return MethodEntryHook.pass();
            } else {
                HapH5CreateReporter.reportHapChannelCreate(
                        HapH5CreateReporter.STAGE_HAP_CHANNEL_ON_DESTROY, TAG, null, "npe", null);
                return MethodEntryHook.intercept();
            }
        } catch (Throwable t) {
            QQThrowableReporter.report("HapCSHook", t);
        }
        return MethodEntryHook.intercept();
    }

    private static boolean canHook() {
        return HadesUtils.cshS(HadesUtils.getContext());
    }

    private static boolean useSdkChannel() {
        return HadesUtils.csOPS(HadesUtils.getContext()) && HadesUtils.isOPPO(HadesUtils.getContext());
    }


    @HookMethodEntry(
            className = "com.meituan.android.launcher.main.io.AwakenTask",
            methodName = "execute",
            methodDesc = "(android.app.Application)"
    )
    public static void execute() {
        Context context = HadesUtils.getContext();
        if (!ProcessUtils.isMainProcess(context)) {
            return;
        }
        PinCommonConfigData data = PinCommonServiceConfig.getInstance().getConfigCache(context);
        if (data != null && data.getMLDelay() < 0) {
            return;
        }
        int delay = data == null ? 10 * 1000 : data.getMLDelay() * 1000;
        Logger.d(TAG, "main wake " + delay);
        if (data!= null && data.getEnableCustomStateBar()) {
            Logger.d(TAG,"data!= null && data.getEnableCustomStateBar()");
            StatusBarInitializer.initLifecycle(ApplicationSingleton.getInstance(),data.getCustomStateBarConfig());
        }else {
            Logger.d(TAG,"data ==null || !data.getEnableCustomStateBar()");
        }
        Logger.d(TAG,"current process : "+ProcessUtils.getCurrentProcessName(context));
        HadesUtils.runOnExecutorDelay(() -> {
            Pin.onProcessCreate(HadesUtils.getContext());
            Logger.d(TAG, "launch pin");
        }, delay);
    }

    @HookMethodEntry(
            className = "com.meituan.android.pin.Pin",
            methodName = "onProcessCreate",
            methodDesc = "(android.content.Context)"
    )
    public static void onProcessCreate(Context context) {
        HadesUtils.runOnExecutor(() -> LaunchManager.getInstance().onProcessCreate(context));
    }
}
