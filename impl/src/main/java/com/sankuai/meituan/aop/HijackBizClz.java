package com.sankuai.meituan.aop;

import android.app.Activity;
import android.os.Bundle;

import com.meituan.android.floatlayer.core.MessageManager;
import com.meituan.android.floatlayer.entity.FloatlayerMessage;
import com.meituan.android.hades.hf.HadesStreamerManager;
import com.meituan.android.walmai.keypath.KeyPathManager;
import com.meituan.android.walmai.keypath.enumtype.ActivityType;
import com.sankuai.waimai.manipulator.annotation.HookMethodEntry;
import com.sankuai.waimai.manipulator.runtime.MethodEntryHook;

@SuppressWarnings("unused")
public class HijackBizClz {


    /**
     * fixme: 部分native activity监听失效，原因未知，暂弃用
     * ======================== hook android.app.Activity start ========================
     */
    /*
    @HookMethodEntry(className = "android.app.Activity+",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeFinish(Activity activity) {
        //mrn
        if (activity instanceof MRNBaseActivity) {
            return KeyPathManager.getInstance().handleBeforeActivityFinish(activity, ActivityType.MRN);
        }
        //mmp
        else if (activity instanceof HeraActivity) {
            return KeyPathManager.getInstance().handleBeforeActivityFinish(activity, ActivityType.MMP);
        }
        //h5(com.sankuai.titans.adapter.mtapp.KNBWebViewActivity)
        else if (activity instanceof KNBWebViewActivity) {
            return KeyPathManager.getInstance().handleBeforeActivityFinish(activity, ActivityType.H5);
        }
        //mgc(com.meituan.android.mgc.container.MGCGameActivity)
        else if (activity instanceof MGCGameActivity) {
            return KeyPathManager.getInstance().handleBeforeActivityFinish(activity, ActivityType.MGC);
        }
        //msc
        else if (activity instanceof MSCActivity) {
            return KeyPathManager.getInstance().handleBeforeActivityFinish(activity, ActivityType.MSC);
        }
        //native
        else {
            return KeyPathManager.getInstance().handleBeforeActivityFinish(activity, ActivityType.NATIVE);
        }
    }

    @HookMethodExit(className = "android.app.Activity+",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> afterFinish(Activity activity) {
        //mrn
        if (activity instanceof MRNBaseActivity) {
            return KeyPathManager.getInstance().handleAfterActivityFinish(activity, ActivityType.MRN);
        }
        //mmp
        else if (activity instanceof HeraActivity) {
            return KeyPathManager.getInstance().handleAfterActivityFinish(activity, ActivityType.MMP);
        }
        //h5(com.sankuai.titans.adapter.mtapp.KNBWebViewActivity)
        else if (activity instanceof KNBWebViewActivity) {
            return KeyPathManager.getInstance().handleAfterActivityFinish(activity, ActivityType.H5);
        }
        //mgc(com.meituan.android.mgc.container.MGCGameActivity)
        else if (activity instanceof MGCGameActivity) {
            return KeyPathManager.getInstance().handleAfterActivityFinish(activity, ActivityType.MGC);
        }
        //msc
        else if (activity instanceof MSCActivity) {
            return KeyPathManager.getInstance().handleAfterActivityFinish(activity, ActivityType.MSC);
        }
        //native
        else {
            return KeyPathManager.getInstance().handleAfterActivityFinish(activity, ActivityType.NATIVE);
        }
    }
    */

    /**
     * ======================== hook android.app.Activity end ========================
     */


    /**
     * ======================== hook all Activities start ========================
     */

    /**
     * 新链路native页面返回
     *
     * @return
     */
    @HookMethodEntry(className = "com.meituan.android.lightbox.activity.LightBoxActivity",
            methodName = "onBackPressed",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeLightBoxBack(Activity activity) {
        return KeyPathManager.getInstance().handleLightBoxBack(activity);
    }

    /**
     * 新链路native页面创建
     *
     * @return
     */
    @HookMethodEntry(className = "com.meituan.android.lightbox.activity.LightBoxActivity",
            methodName = "onCreate",
            methodDesc = "(android.os.Bundle)",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeLightBoxCreate(Activity activity, Bundle bundle) {
        KeyPathManager.getInstance().handleLightBoxCreate(activity);
        return MethodEntryHook.pass();
    }


    /**
     * 商超
     *
     * @return
     */
    @HookMethodEntry(className = "com.sankuai.waimai.store.poi.list.newp.PoiVerticalityHomeActivity",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeShopFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.NATIVE);
    }

    /**
     * 买菜
     *
     * @return
     */
    @HookMethodEntry(className = "com.meituan.retail.c.android.newhome.newmain.NewMainActivity",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeBuyFoodFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.NATIVE);
    }

    /**
     * 外卖
     *
     * @return
     */
    @HookMethodEntry(className = "com.sankuai.waimai.business.page.homepage.TakeoutActivity",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeTakeoutFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.NATIVE);
    }

    /**
     * 医药
     *
     * @return
     */
    @HookMethodEntry(className = "com.sankuai.waimai.store.drug.home.DrugHomeActivity",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeDrugFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.NATIVE);
    }

    /**
     * 酒店
     *
     * @return
     */
    @HookMethodEntry(className = "com.meituan.android.hotel.reuse.homepage.HotelPoiListFrontActivity",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeHotelFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.NATIVE);
    }

    /**
     * 摩拜
     *
     * @return
     */
    @HookMethodEntry(className = "com.meituan.android.bike.component.feature.main.view.MobikeMainActivity",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeMobikeFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.NATIVE);
    }

    /**
     * 手机充值
     *
     * @return
     */
    @HookMethodEntry(className = "com.sankuai.eh.framework.EHContainerActivity",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeChargeFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.H5);
    }

    /**
     * 电影
     *
     * @return
     */
    @HookMethodEntry(className = "com.meituan.android.movie.MovieMainActivity",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeMovieFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.NATIVE);
    }

    /**
     * 机票火车票
     *
     * @return
     */
    @HookMethodEntry(className = "com.meituan.android.traffichome.TrafficHomePageActivity",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeTrafficFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.NATIVE);
    }

    /**
     * 跑腿
     *
     * @return
     */
    @HookMethodEntry(className = "com.meituan.android.legwork.ui.abactivity.LegworkMainActivity",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeLegworkFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.NATIVE);
    }

    /**
     * 游戏
     *
     * @return
     */
    @HookMethodEntry(className = "com.meituan.android.mgc.container.MGCBaseActivity",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeMGCFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.MGC);
    }

    @HookMethodEntry(className = "com.sankuai.titans.adapter.mtapp.KNBWebViewActivity",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeKnbFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.H5);
    }

    @HookMethodEntry(className = "com.meituan.msc.modules.container.MSCActivity+",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeMscFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.MSC);
    }

    @HookMethodEntry(className = "com.meituan.android.mrn.container.MRNBaseActivity+",
            methodName = "finish",
            methodDesc = "()",
            insert = true,
            callSuper = true
    )
    public static MethodEntryHook<Void> beforeMrnFinish(Activity activity) {
        return KeyPathManager.getInstance().handleActivityFinish(activity, ActivityType.MRN);
    }

    /**
     * ======================== hook all Activities end ========================
     */

    /**
     * ======================== test HadesDevActivity start ========================
     */

//    /**
//     * 强制返回不hook
//     */
//    static volatile boolean forceBack = false;
//
//    @HookMethodEntry(className = "com.meituan.android.hades.HadesDevActivity",
//            methodName = "finish",
//            methodDesc = "()",
//            insert = true,
//            callSuper = true
//    )
//    public static MethodEntryHook<Void> beforeFinish(Activity activity) {
//        boolean switchOn = HadesConfigMgr.getInstance(activity).isKeyPathSwitchOn();
//        Logger.d(KeyPathManager.TAG, "beforeFinish activity:" + activity + ",forceBack:" + forceBack + ",switchOn:" + switchOn);
//        if (!switchOn) {
//            return MethodEntryHook.pass();
//        }
//        if (activity == null) {
//            return MethodEntryHook.pass();
//        }
//        if (forceBack) {
//            forceBack = false;
//            return MethodEntryHook.pass();
//        }
//        try {
//            KeyPathSubscribeHelper.getInstance().processSubscribe(activity, 40001,
//                    new KeyPathCallback<Void>() {
//                        @Override
//                        public void onSuccess(@Nullable Void var) {
//                            activity.finish();
//                        }
//
//                        @Override
//                        public void onFailure() {
//                            activity.finish();
//                        }
//                    });
//            forceBack = true;
//            return MethodEntryHook.intercept();
//        } catch (Exception e) {
//            Logger.e(KeyPathManager.TAG, "activity beforeFinish hook error:" + e.getMessage(), e);
//        }
//        return MethodEntryHook.pass();
//    }


    /**
     * ======================== test HadesDevActivity end ========================
     */

    @HookMethodEntry(
            className = "com.meituan.android.floatlayer.core.MessageManager",
            methodName = "showNotification",
            methodDesc = "(com.meituan.android.floatlayer.entity.FloatlayerMessage, int)"
    )

    public static MethodEntryHook<Void> hookShowNotification(MessageManager manager, FloatlayerMessage message, int mode) {
        return HadesStreamerManager.handleStreamer(manager, message, mode);
    }

}