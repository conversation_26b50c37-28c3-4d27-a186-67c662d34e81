package com.sankuai.meituan.android.hades.apm.memopt;

import android.content.Context;
import android.support.annotation.Keep;

import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.StorageHelper;
import com.sankuai.common.utils.ProcessUtils;
import com.sankuai.meituan.mtlive.core.log.LogUtils;

import org.json.JSONObject;

@Keep
public class MemOptJNIUtils {

    public static boolean executeJNI(Context context) {
        try {
            if (MemOptJNI.isLibraryLoaded) {
                String processName = ProcessUtils.getCurrentProcessName(context);
                String pkgName = context.getPackageName();
                return MemOptJNI.nReleaseDexMMAPMemory(processName, context, pkgName);
            } else {
                OptimTask.init(context);
            }
        } catch (Exception e) {
//            LogUtils.logan("executeJNI error = " + e.getMessage());
        }
        return false;
    }


    public static boolean checkWPSwitch(Context context) {
        JSONObject jsonObject = StorageHelper.getWidgetProcessOptimizeConfig(context);
        return HadesUtils.isXiaoMi(context) && jsonObject != null && "1".equals(jsonObject.optString("widgetOptConfig"));
    }

    public static String getMemOptJNIWhiteList() {
        return HadesUtils.getMemOptJNIWhileList();
    }
}
