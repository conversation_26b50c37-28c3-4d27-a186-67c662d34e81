package com.sankuai.meituan.android.hades.apm.memopt;

import android.content.Context;
import android.support.annotation.Keep;
@Keep
public class MemOptJNI {
    public static boolean isLibraryLoaded = false;
//    static {
//        try {
//            System.loadLibrary("optim"); // 加载 liboptim.so
//            isLibraryLoaded = true;
//            Log.e("XiaomiWidget22EX", "liboptim.so 加载成功");
//        } catch (UnsatisfiedLinkError e) {
//            isLibraryLoaded = false;
//        }
//    }

    public static native boolean nReleaseDexMMAPMemory(final String processName, Context context, final String pkgName);
}
