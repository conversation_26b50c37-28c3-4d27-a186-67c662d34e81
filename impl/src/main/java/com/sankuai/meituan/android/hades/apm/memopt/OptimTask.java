package com.sankuai.meituan.android.hades.apm.memopt;

import android.content.Context;

import com.meituan.android.pin.dydx.FileBaseInfo;
import com.meituan.android.walmai.so.ISoCallback;
import com.meituan.android.walmai.so.SoDataMgr;
import com.meituan.android.walmai.so.SoMgr;

public class OptimTask {
    private static final String TAG = "OptimTask";
    private static final String OPTIM_SO_NAME = "optim";

    public static void init(Context context) {
        if (MemOptJNI.isLibraryLoaded) {
            return;
        }
        Context appContext = context.getApplicationContext();
        FileBaseInfo fileInfo = SoDataMgr.getFileInfo(OPTIM_SO_NAME);
        if (fileInfo != null) {
            SoMgr.load(appContext, OPTIM_SO_NAME, new ISoCallback() {
                @Override
                public void onSuccess() {
                    MemOptJNI.isLibraryLoaded = true;
                    boolean result = MemOptJNIUtils.executeJNI(context);
//                    Logger.e(TAG, "load optim successful, OptimTask-nReleaseDexMMAPMemory result: " + result);
                }

                @Override
                public void onFail(int code, String msg) {
//                    Logger.e(TAG, "load optim failed: " + code + " " + msg);
                }
            });
        } else {
//            Logger.e(TAG, "liboptim.so file does not exist ");
        }


    }

}
