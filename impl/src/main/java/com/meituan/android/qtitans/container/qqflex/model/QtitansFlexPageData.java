package com.meituan.android.qtitans.container.qqflex.model;

import static com.meituan.android.qtitans.container.qqflex.QtitansFlexContainerActivity.BABEL_CUSTOM_PAGE_REPORT;

import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.annotations.SerializedName;
import com.meituan.android.hades.impl.report.BabelHelper;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.qtitans.container.qqflex.listview.QtitansFlexData;
import com.sankuai.common.utils.JsonHelper;

import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QtitansFlexPageData implements JsonDeserializer<QtitansFlexPageData> {
    @SerializedName("globalId")
    public String globalId;
    @SerializedName("hasNext")
    public boolean hasNext;
    @SerializedName("titleTemplateUrl")
    public String titleTemplateUrl;
    @SerializedName("titleTemplateName")
    public String titleTemplateName;
    @SerializedName("cardListHeaderTemplateUrl")
    public String cardListHeaderTemplateUrl;
    @SerializedName("cardListHeaderTemplateName")
    public String cardListHeaderTemplateName;
    @SerializedName("cardFeedHeaderTemplateName")
    public String cardFeedHeaderTemplateName;
    @SerializedName("cardFeedHeaderTemplateUrl")
    public String cardFeedHeaderTemplateUrl;
    @SerializedName("backgroundImageUrl")
    public String backgroundImageUrl;
    @SerializedName("backgroundColor")
    public String backgroundColor;
    @SerializedName("feedBackgroundColorStart")
    public String feedBackgroundColorStart;
    @SerializedName("feedBackgroundColorEnd")
    public String feedBackgroundColorEnd;
    public List<JSONObject> cardListData;
    public List<QtitansFlexData> flexDataList;

    public static  List<QtitansFlexData> from(JsonArray data, Map<String, Object> properties) {
        ArrayList<QtitansFlexData> dynamicDataList = new ArrayList<>();
        try {
            for (int i = 0; i < data.size(); i++) {
                try {
                    Object map = data.get(i);
                    if (map instanceof JsonObject) {
                        if (((JsonObject)map).size() > 0) {
                            if (properties != null && !properties.isEmpty()) {
                                for (String key: properties.keySet()) {
                                    ((JsonObject) map).addProperty(key, String.valueOf(properties.get(key)));
                                }
                            }

                            QtitansFlexData dynamicData = new QtitansFlexData(JsonHelper.toJSONObject((JsonObject) map));
                            dynamicDataList.add(dynamicData);
                        }
                    } else if (map instanceof JsonPrimitive) {
                        JSONObject mapStr = new JSONObject(((JsonPrimitive) map).getAsString());
                        QtitansFlexData dynamicData = new QtitansFlexData(mapStr);
                        dynamicDataList.add(dynamicData);
                    }

                } catch (Throwable e) {
                    QQThrowableReporter.report("QtitansFlexPageData", e, true);
                    HashMap<String, Object> info = new HashMap<>();
                    info.put("reason", "QtitansFlexPageData from for item failed: " + e.getMessage());
                    BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);
                }
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansFlexPageData", e, true);
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", "QtitansFlexPageData from for failed: " + e.getMessage());
            BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);
        }

        return dynamicDataList;

    }
    public static  List<QtitansFlexData> from(JsonArray data) {
        return from(data, null);
    }

    @Override
    public QtitansFlexPageData deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        JsonObject rootJsonObject;
        if (json == null || (rootJsonObject = json.getAsJsonObject())== null) return null;
        QtitansFlexPageData pageData = new QtitansFlexPageData();
        if (rootJsonObject.has("hasNext")) {
            pageData.hasNext = rootJsonObject.get("hasNext").getAsBoolean();
        }
        if (rootJsonObject.has("titleTemplateName")) {
            pageData.titleTemplateName = rootJsonObject.get("titleTemplateName").getAsString();
        }
        if (rootJsonObject.has("titleTemplateUrl")) {
            pageData.titleTemplateUrl = rootJsonObject.get("titleTemplateUrl").getAsString();
        }
        if (rootJsonObject.has("cardFeedHeaderTemplateName")) {
            pageData.cardFeedHeaderTemplateName = rootJsonObject.get("cardFeedHeaderTemplateName").getAsString();
        }
        if (rootJsonObject.has("cardFeedHeaderTemplateUrl")) {
            pageData.cardFeedHeaderTemplateUrl = rootJsonObject.get("cardFeedHeaderTemplateUrl").getAsString();
        }
        if (rootJsonObject.has("cardListHeaderTemplateName")) {
            pageData.cardListHeaderTemplateName = rootJsonObject.get("cardListHeaderTemplateName").getAsString();
        }
        if (rootJsonObject.has("cardListHeaderTemplateUrl")) {
            pageData.cardListHeaderTemplateUrl = rootJsonObject.get("cardListHeaderTemplateUrl").getAsString();
        }
        if (rootJsonObject.has("globalId")) {
            pageData.globalId = rootJsonObject.get("globalId").getAsString();
        }
        if (rootJsonObject.has("backgroundImageUrl")) {
            pageData.backgroundImageUrl = rootJsonObject.get("backgroundImageUrl").getAsString();
        }
        if (rootJsonObject.has("backgroundColor")) {
            pageData.backgroundColor = rootJsonObject.get("backgroundColor").getAsString();
        }
        if (rootJsonObject.has("feedBackgroundColorStart")) {
            pageData.feedBackgroundColorStart = rootJsonObject.get("feedBackgroundColorStart").getAsString();
        }
        if (rootJsonObject.has("feedBackgroundColorEnd")) {
            pageData.feedBackgroundColorEnd = rootJsonObject.get("feedBackgroundColorEnd").getAsString();
        }

        if (rootJsonObject.has("items") && rootJsonObject.get("items") instanceof JsonArray) {
            JsonArray jsonItemsArray = rootJsonObject.getAsJsonArray("items");
            if (jsonItemsArray.size() > 0) {
                pageData.flexDataList = from(jsonItemsArray);
            }
        }
        if (rootJsonObject.has("itemsNew") && rootJsonObject.get("itemsNew") instanceof JsonArray) {
            JsonArray jsonItemsArray = rootJsonObject.getAsJsonArray("itemsNew");
            if (jsonItemsArray.size() > 0) {
                pageData.flexDataList = from(jsonItemsArray);
            }
        }
        if (rootJsonObject.has("cardList") && rootJsonObject.get("cardList") instanceof JsonArray) {
            JsonArray jsonCardListArray = rootJsonObject.getAsJsonArray("cardList");
            JSONArray cardListArray = JsonHelper.toJSONArray(jsonCardListArray);
            ArrayList<JSONObject> cardDataList = new ArrayList<>();
            try {
                for (int i=0; i< cardListArray.length(); i++) {
                    JSONObject jsonObject = cardListArray.getJSONObject(i);
                    if (jsonObject != null) {
                        cardDataList.add(jsonObject);
                    }
                }

            } catch (Throwable e) {
                QQThrowableReporter.report("deserialize failed",e, true);
                HashMap<String, Object> info = new HashMap<>();
                info.put("reason", "QtitansFlexPageData construct cardList failed: " + e.getMessage());
                BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);
            }
            pageData.cardListData = cardDataList;
        }


        return pageData;
    }
}
