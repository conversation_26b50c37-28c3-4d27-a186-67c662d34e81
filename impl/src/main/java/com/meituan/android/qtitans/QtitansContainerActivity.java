package com.meituan.android.qtitans;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.FragmentActivity;

import com.meituan.android.hades.BuildConfig;
import com.meituan.android.hades.dyadater.container.ContainerServiceFactory;
import com.meituan.android.hades.dyadater.container.interfaces.IContainerProvider;
import com.meituan.android.hades.dyadater.container.interfaces.IQtitansContainerDelegate;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.Logger;
import com.sankuai.titans.base.TitansFragment;

import java.lang.reflect.Field;

public class QtitansContainerActivity extends FragmentActivity  {
    private static final String TAG = "QtitansContainer";
    public static final String QTITANS_TARGET = "qtitans_target";


    protected IQtitansContainerDelegate mDelegate;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        mDelegate = getContainerDelegate();
        if (mDelegate!= null) {
            mDelegate.beforeOnCreate(this, savedInstanceState);
        }
        super.onCreate(savedInstanceState);
        if (mDelegate != null) {
            mDelegate.onCreate(this, savedInstanceState);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        overridePendingTransition(0, 0);
        super.onNewIntent(intent);
        if (mDelegate != null) {
            mDelegate.onNewIntent(this, intent);
        }

    }

    @Override
    protected void onDestroy() {
        if (mDelegate != null) {
            mDelegate.beforeOnDestroy(this);
        }
        super.onDestroy();
        if (mDelegate != null) {
            mDelegate.onDestroy(this);
        }
    }

    @Override
    protected void onPostCreate(@Nullable Bundle savedInstanceState) {
        if (mDelegate != null) {
            mDelegate.onPostCreate(this, savedInstanceState);
        }
        super.onPostCreate(savedInstanceState);
    }

    @Override
    protected void onResume() {
        try {
            if (mDelegate != null) {
                mDelegate.beforeOnResume(this);
            }
            super.onResume();
            if (mDelegate != null) {
                mDelegate.onResume(this);
            }

        } catch (Throwable e) {
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M && Build.VERSION.SDK_INT <= Build.VERSION_CODES.P) {
                // Android 7-9 isTopOfTask方法容易因为ActivityRecord被回收造成crash
                // https://crash.sankuai.com/#/new/overview/message?filter=%5B%7B%22k%22%3A%22hash%22%2C%22v%22%3A%22java.lang.IllegalArgumentException%3Acom.sankuai.meituan.mbc.dsp.DspActivity.onResume%28Unknown%20Source%3A129%29%22%7D%2C%7B%22k%22%3A%22timelike%22%2C%22v%22%3A%22%E6%9C%80%E8%BF%9130%E5%A4%A9%22%7D%5D&channel=Unprovided&project=android_platform_monitor&type=crash
                // 尝试修复
                handleOnResumeException();
            }
            QQThrowableReporter.report("QtitansContainerActivity", e);
        }
    }

    @Override
    protected void onPause() {
        overridePendingTransition(0, 0);
        super.onPause();
        if (mDelegate != null) {
            mDelegate.onPause(this);
        }
    }

    @Override
    protected void onStop() {
        try {
            if (mDelegate != null) {
                mDelegate.beforeOnStop(this);
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerActivity", e);
        }
        super.onStop();
        if (mDelegate != null) {
            mDelegate.onStop(this);
        }
        overridePendingTransition(0, 0);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        try {
            if (BuildConfig.LOG_ENABLE) {
                Logger.d("onActivityResult " );
            }
            super.onRequestPermissionsResult(requestCode, permissions, grantResults);
            if (mDelegate != null && mDelegate.getTitansFragment() instanceof TitansFragment) {
                mDelegate.getTitansFragment().onRequestPermissionsResult(requestCode, permissions, grantResults);
            }
            return;
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerActivity", e);
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        try {

            if (BuildConfig.LOG_ENABLE) {
                Logger.d("onActivityResult " + toString());
            }
            super.onActivityResult(requestCode, resultCode, data);
            if (mDelegate != null && mDelegate.getTitansFragment() instanceof TitansFragment) {
                mDelegate.getTitansFragment().onActivityResult(requestCode, resultCode, data);
            }
            return;
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerActivity", e);
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onBackPressed() {
        if (mDelegate != null && mDelegate.onBackPressed(this)) {
            return;
        }
        handleBackPress();
        super.onBackPressed();
    }

    protected void handleBackPress() {
        try {
            if (mDelegate != null && mDelegate.getTitansFragment() instanceof TitansFragment) {
                ((TitansFragment) mDelegate.getTitansFragment()).onBackPressed();
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerActivity", e);
        }
    }

    /**
     * 修改mCalled = true
     */
    private void handleOnResumeException() {
        try {
            Class dspSuperClass = Activity.class;
            Field callField = dspSuperClass.getDeclaredField("mCalled");
            callField.setAccessible(true);
            callField.setBoolean(QtitansContainerActivity.this, true);
        } catch (Throwable throwable) {
            QQThrowableReporter.report("QtitansContainerActivity", throwable);
        }
    }

    protected IQtitansContainerDelegate getContainerDelegate() {
        IContainerProvider provider = ContainerServiceFactory.getInstance().getProvider();
        if (provider != null) {
            return provider.getContainerDelegate(getScene());
        }
         return null;
    }

    protected String getScene() {
        return "container";
    }
}
