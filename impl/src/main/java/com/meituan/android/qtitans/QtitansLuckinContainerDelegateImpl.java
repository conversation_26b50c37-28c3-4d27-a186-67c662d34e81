package com.meituan.android.qtitans;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.content.LocalBroadcastManager;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.meituan.android.aurora.ActivitySwitchCallbacks;
import com.meituan.android.aurora.Aurora;
import com.meituan.android.hades.HadesWidgetEnum;
import com.meituan.android.hades.dyadater.container.ContainerConst;
import com.meituan.android.hades.dyadater.desk.DeskResourceData;
import com.meituan.android.hades.dyadater.desk.DeskSourceEnum;
import com.meituan.android.hades.impl.desk.DeskTypeEnum;
import com.meituan.android.hades.impl.desk.FloatWinViewFactory;
import com.meituan.android.hades.impl.desk.feedback.FeedbackManager;
import com.meituan.android.hades.impl.model.FeedbackData;
import com.meituan.android.hades.impl.report.BabelHelper;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Lifecycle;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.router.IRedirectHandler;
import com.meituan.android.hades.router.SkyEyes;
import com.meituan.android.hades.router.tt.TTRedirectHandler;
import com.meituan.android.launcher.AbstractLauncher;
import com.meituan.android.launcher.main.io.SecondaryAsyncTask;
import com.meituan.android.launcher.secondary.SecondaryLauncher;
import com.meituan.android.mrn.router.MRNURL;
import com.meituan.android.qtitans.container.QtitansContainerManager;
import com.meituan.android.qtitans.container.bean.QtitansContainerParams;
import com.meituan.android.qtitans.container.qqflex.lucykin.IFlexLuckinPageRequestCallback;
import com.meituan.android.qtitans.container.qqflex.lucykin.QtitansLuckinPreRequestManager;
import com.meituan.android.qtitans.container.qqflex.lucykin.model.LuckinMainPage;
import com.meituan.android.qtitans.container.reporter.ContainerReporter;
import com.meituan.android.walmai.widget.AbsCoopFeatureWidget;
import com.meituan.passport.UserCenter;
import com.sankuai.meituan.mbc.dsp.core.CurrentActivityUtil;
import com.sankuai.meituan.mbc.dsp.core.Dsp;
import com.sankuai.titans.protocol.utils.PublishCenter;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Set;

import rx.Subscription;

public class QtitansLuckinContainerDelegateImpl extends QtitansContainerDelegateImpl{
    public final static String Qtitans_MINIPROGRAM_SCHEME_BASE = "imeituan://www.meituan.com/qtitans/container/luckin";
    public static final String ACTION_REFRESH_CARD = "com.meituan.android.hades.savemoneycard.update";
    public static final String EXTRA_CARD_URL = "extra_card_url";

    private static final String TAG = "SaveMoneyCard";
    private final static String KNB_EVENT_KEY_RESORT_TOP_SPU = "qq_event_resort_top_spu";
    private final static String KNB_EVENT_KEY_CHOOSE_SPEC = "qq_event_choose_spec";

    private String topSpu;
    private boolean refreshTopSpu = false;
    private Subscription loginSubscription;

    private final BroadcastReceiver knbWebViewInitReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                if (intent == null) {
                    return;
                }
                if (!isLuckinFlexPage()) {
                    Logger.d(TAG, "after web view inited");
                    //不要删或移动位置！ QQ 页面在 launcher 侧不会执行任何次级任务，需要提前执行 knb 任务
                    Set<String> tasks = SecondaryLauncher.getColdStartKNBTaskIDSet();
                    AbstractLauncher launcher = SecondaryAsyncTask.getSecondaryLauncher(context);
                    if (launcher != null) {
                        launcher.start(tasks);
                    }
                    Aurora.getInstance().startSecondaryByTaskIdSet(tasks);
                    LocalBroadcastManager.getInstance(context).unregisterReceiver(knbWebViewInitReceiver);
                }
            } catch (Throwable e) {
                QQThrowableReporter.report("QtitansLuckinContainerDelegateImpl", e);
            }
        }
    };

    private final BroadcastReceiver cardUpdateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                if (intent == null) {
                    return;
                }
                String action = intent.getAction();
                if (TextUtils.equals(action, ACTION_REFRESH_CARD)) {
                    String cardUrl = intent.getStringExtra(EXTRA_CARD_URL);
                    if (cardUrl == null) {
                        return;
                    }
                    if (cardUrl.contains("savemoneycard")) {
                        String newSpu = getTopSpuFromUrl(cardUrl);
                        Logger.d(TAG, "receive card update: curSpu=" + topSpu + " newSpu=" + newSpu);
                        if (newSpu != null && !TextUtils.equals(newSpu, topSpu)) {
                            refreshTopSpu = true;
                            topSpu = newSpu;
                        }
                    } else {
                        if (isPickUpPage(cardUrl)) {
                            Logger.d(TAG, "receive card update: inPickUp status");
                        }
                    }

                }
            } catch (Throwable e) {
                QQThrowableReporter.report("QtitansLuckinContainerDelegateImpl", e);
            }
        }
    };

    private final ActivitySwitchCallbacks activitySwitchCallbacks = new ActivitySwitchCallbacks() {
        @Override
        public void onBackground() {
            super.onBackground();
            Logger.d(TAG, "onBackground");
            try {
                HadesWidgetEnum widgetEnum = HadesWidgetEnum.getInstanceByWidgetNumCode(mQtitansContainerParams.getSceneCode());
                if (widgetEnum == null) {
                    return;
                }
                AbsCoopFeatureWidget.clickRefresh(HadesUtils.getContext(), widgetEnum);
            } catch (Throwable e) {
                QQThrowableReporter.report("QtitansLuckinContainerDelegateImpl", e);
            }
        }
    };

    private final PublishCenter.ReceivedActionCallback knbEventCallback = (action, jsonObject) -> {
        try {
            if (action == null || jsonObject == null) {
                Logger.d(TAG, "onReceivedAction action or jsonObject is null");
                return;
            }
            JSONObject data = jsonObject.optJSONObject("data");
            if (data == null) {
                return;
            }
            switch (action) {
                case "savemoney.spec_h5.close": {
                    String changeTab = data.optString("dist_tab");
                    if (TextUtils.equals(changeTab, "orderlist")) {
                        Logger.d(TAG, "onReceivedAction showOrderList");
                        if (luckinContainerFragment != null) {
                            HadesUtils.runOnMainThread(()->{
                                luckinContainerFragment.switchFragment("ORDER", "");
                            });
                        }
                    } else if (TextUtils.equals(changeTab, "coupon")) {
                        Logger.d(TAG, "onReceivedAction showMain");
                        if (luckinContainerFragment != null) {
                            HadesUtils.runOnMainThread(()->{
                                luckinContainerFragment.switchFragment("DEAL", "");
                            });

                        }
                    }
//                    startSelfAndClearTop();
                    break;
                }
                case "savemoney.spec_h5.changeshop": {
                    String shopId = data.optString("shopId");
                    String shopName = data.optString("shopName");
                    Logger.d(TAG, "onReceivedAction changeShop: shopId=" + shopId + ", shopName=" + shopName);
                    if (luckinContainerFragment != null) {
                        HadesUtils.runOnMainThread(()->{
                            luckinContainerFragment.switchFragment("DEAL", shopId);
                        });

                    }
                    break;
                }
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansLuckinContainerDelegateImpl", e);
        }
    };

    private String getTopSpuFromUrl(String targetUrl) {
        if (!TextUtils.isEmpty(targetUrl)) {
            Uri uri = Uri.parse(targetUrl);
            String innerUrl = uri.getQueryParameter("url");
            if (!TextUtils.isEmpty(innerUrl)) {
                Uri innerUri = Uri.parse(innerUrl);
                return innerUri.getQueryParameter("topSpuName");
            }
        }
        return null;
    }
    @Override
    public void onCreate(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        super.onCreate(activity, savedInstanceState);
        try {
            Logger.d(TAG, "onCreate savedInstanceState=" + savedInstanceState);
            if (isLuckinFlexPage()) {
                //跳链为省钱卡，上报页面pv
                ContainerReporter.reportLuckinSaveMoneyPagePv(activity, mQtitansContainerParams);
            }
            activity.getIntent().putExtra(Dsp.INTENT_EXTRA_IS_RETURN_HANDLED, true); //返回不需要 dsp 处理
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(ACTION_REFRESH_CARD);
            try {
                activity.registerReceiver(cardUpdateReceiver, intentFilter);
            } catch (Throwable e) {
                QQThrowableReporter.report("QtitansLuckinContainerDelegateImpl", e);
            }
            if (mQtitansContainerParams != null) {
                this.topSpu = getTopSpuFromUrl(mQtitansContainerParams.getTargetUrl());
            }
            Lifecycle.registerCallbackRemoveDuplication(activitySwitchCallbacks);
            PublishCenter.getInstance().registerCallback(knbEventCallback);

            if (!isLuckinFlexPage()) {
                LocalBroadcastManager localBroadcastManager = LocalBroadcastManager.getInstance(activity);
                localBroadcastManager.registerReceiver(knbWebViewInitReceiver, new IntentFilter("ACTION_WEBVIEW_INIT"));
            }
            HadesUtils.runOnMainThreadWithDelay(() -> addFeedbackIcon(activity), 1500);
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansLuckinContainerDelegateImpl", e);
        }
    }

    private void addFeedbackIcon(Activity context) {
        try {
            if (!HadesUtils.enableLuckinPageCache() || mQtitansContainerParams == null) return;
            DeskResourceData resourceData = new DeskResourceData();
            resourceData.target = mQtitansContainerParams.getTargetUrl();
            resourceData.sessionId = DeskSourceEnum.WIDGET.getMessage();
            resourceData.deskType = DeskTypeEnum.BUTTON;
            resourceData.riskAb = "pushclose";
            FeedbackData feedbackData = new FeedbackData();
            feedbackData.scene = FeedbackManager.SCENE_LUCKIN_PAGE;
            feedbackData.bizName = "QQ";
            feedbackData.display = true;
            feedbackData.entrance = "ICON";
            resourceData.feedbackData = feedbackData;
            final View feedbackView = FloatWinViewFactory.makeFloatWin(context, resourceData, DeskSourceEnum.WIDGET);
            if (feedbackView == null) {
                return;
            }
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
            );
            layoutParams.bottomMargin = 584;
            layoutParams.gravity = Gravity.END | Gravity.BOTTOM;
            ViewGroup decorView = (ViewGroup) context.getWindow().getDecorView();
            decorView.addView(feedbackView, layoutParams);
        }catch (Throwable e){
        }
    }

    @Override
    public boolean onBackPressed(@NonNull Activity activity) {
        if (luckinContainerFragment != null && luckinContainerFragment.onBackPressed()){
            return true;
        }
        return super.onBackPressed(activity);
    }
    private void updateLuckInView(LuckinMainPage mainPage, boolean isCache) {
        if (mainPage == null) return;
        if (luckinContainerFragment != null) {
            luckinContainerFragment.updateLuckInView(mainPage, isCache);
        }

    }

    @Override
    public void onReStart(Activity activity) {
        super.onReStart(activity);
        Logger.d(TAG, "onRestart");
        if (activity.getIntent().getExtras() != null) {
            //热启情况，从桌面点击会直接走到 onRestart，这里重新注册 tt 流程
            String paramsStr = activity.getIntent().getStringExtra(ContainerConst.Qtitans_CONTAINER_PARAMS);
            QtitansContainerParams params = HadesUtils.fromJson(paramsStr, QtitansContainerParams.class);
            if (params != null) {
                String targetUrl = params.getTargetUrl();
                String lastRedirectUrl = null;
                IRedirectHandler lastHandler = SkyEyes.getRedirectHandler(TTRedirectHandler.KEY);
                if (lastHandler instanceof TTRedirectHandler) {
                    lastRedirectUrl = ((TTRedirectHandler) lastHandler).getRedirectUrl();
                }
                TTRedirectHandler.registerIfNeed(targetUrl, lastRedirectUrl, false);
            }
        }
    }

    @Override
    public void onResume(@NonNull Activity activity) {

        try {
            if (refreshTopSpu) {
                refreshTopSpu = false;
                Logger.d(TAG, "resort top spu");
                if (!isLuckinFlexPage()) {
                    JSONObject params = new JSONObject();
                    params.put("topSpu", topSpu);
                    PublishCenter.getInstance().publish(KNB_EVENT_KEY_RESORT_TOP_SPU, params);
                }

                if (isLuckinFlexPage()) {
                    QtitansLuckinPreRequestManager.getInstance().refreshSkuName(activity, topSpu, new IFlexLuckinPageRequestCallback() {
                        @Override
                        public void onSuccess(LuckinMainPage data) {
                            updateLuckInView(data, false);
                        }

                        @Override
                        public void onFailed(String message) {
                            Logger.e(TAG, "refresh top sku failed");
                        }
                    });
                }
            }

            if (!UserCenter.getInstance(activity).isLogin() && loginSubscription == null) {
                loginSubscription = UserCenter.getInstance(activity).loginEventObservable().subscribe(loginEvent -> {
                    if (CurrentActivityUtil.isContainsTargetActivity(QTitansLuckinContainerActivity.class.getName())) {
                        startSelfAndClearTop(activity);
                    }
                });
            }
        } catch (Exception e) {
            QQThrowableReporter.report("QtitansLuckinContainerDelegateImpl", e);
        }
        super.onResume(activity);
    }

    @Override
    public void onNewIntent(@NonNull Activity activity, @NonNull Intent newIntent) {
        try {
            Logger.d(TAG, "newIntent");
            if (newIntent != null && newIntent.getData() != null) {
                Uri uri = newIntent.getData();
                String fromRedirect = uri.getQueryParameter("from_redirect");
                HashMap<String, Object> info = new HashMap<>();
                if ("h5".equals(fromRedirect)) {
                    Logger.d(TAG, "h5 redirect");
                    try {
                        Set<String> queryKeys = uri.getQueryParameterNames();
                        JSONObject params = new JSONObject();
                        for (String key : queryKeys) {
                            params.put(key, uri.getQueryParameter(key));
                        }
                        PublishCenter.getInstance().publish(KNB_EVENT_KEY_CHOOSE_SPEC, params);

                        info.put("reason", "SEND_CHOOSE_SPEC_EVENT_SUCCESS");
                        info.put("params", params.toString());
                        Logger.d(TAG, "send choose spec event: params=" + params);
                        BabelHelper.log(ContainerConst.BABEL_TYPE_SMC_REPORT, info);
                    } catch (Throwable t) {
                        info.put("reason", "SEND_CHOOSE_SPEC_EVENT_FAIL");
                        info.put("error", t.getMessage());
                        BabelHelper.log(ContainerConst.BABEL_TYPE_SMC_REPORT, info);
                    }
                } else if ("native".equals(fromRedirect)) {
                    Logger.d(TAG, "native redirect");
                    String kkRedirectUrl = uri.getQueryParameter("kkRedirectUrl");
                    Logger.d(TAG, "kkRedirectUrl: " + kkRedirectUrl);
                    if (!TextUtils.isEmpty(kkRedirectUrl)) {
                        Intent chooseSpecIntent = new Intent(Intent.ACTION_VIEW);
                        chooseSpecIntent.setData(Uri.parse(kkRedirectUrl));
                        activity.startActivity(chooseSpecIntent);

                        info.put("reason", "JUMP_CHOOSE_SPEC_SUCCESS");
                        BabelHelper.log(ContainerConst.BABEL_TYPE_SMC_REPORT, info);
                    }
                } else {
                    super.newIntent(activity, newIntent);
                    info.put("reason", "CHOOSE_SPEC_FAILED");
                    BabelHelper.log(ContainerConst.BABEL_TYPE_SMC_REPORT, info);
                }
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansLuckinContainerDelegateImpl", e);
        }
    }

    @Override
    public void onDestroy(@NonNull Activity activity) {
        super.onDestroy(activity);
        try {
            if (loginSubscription != null && !loginSubscription.isUnsubscribed()) {
                Logger.d(TAG, "unsubscribe login");
                loginSubscription.unsubscribe();
                loginSubscription = null;
            }
            Lifecycle.unRegisterCallback(activitySwitchCallbacks);
            activity.unregisterReceiver(cardUpdateReceiver);
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansLuckinContainerDelegateImpl", e);
        }
    }

    private void startSelfAndClearTop(Activity activity) {
        Logger.d(TAG, "start self");
        Intent self = new Intent(Intent.ACTION_VIEW, Uri.parse(Qtitans_MINIPROGRAM_SCHEME_BASE));
        self.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        activity.startActivity(self);
    }

    private boolean isPickUpPage(String url) {
        if (TextUtils.isEmpty(url)) {
            return false;
        }
        MRNURL mrnUrl = new MRNURL(url);
        return ("meishi".equals(mrnUrl.getBizName()) &&
                "ka-mop-order-detail".equals(mrnUrl.getEntryName()) &&
                "kaMopOrderDetail".equals(mrnUrl.getComponentName()));
    }
    @Override
    public boolean isLuckinFlexPage() {
        return HadesUtils.isEnableLuckinNativePage() && isLuckinDealPage(mQtitansContainerParams);
    }

    private boolean isLuckinDealPage(@NonNull QtitansContainerParams params) {
        try {
            Uri uri = Uri.parse(params.getTargetUrl());
            String lukinWebUrl = uri.getQueryParameter("url");
            if (!TextUtils.isEmpty(lukinWebUrl)) {
                return lukinWebUrl.contains("savemoneycard");
            }
        } catch (Throwable e) {
            QQThrowableReporter.report(TAG, e, true);
        }
        return false;
    }


}
