package com.meituan.android.qtitans.container.qqflex.lucykin.model;

//import static com.meituan.android.hades.router.tt.TTRedirectHandler.BABEL_TYPE_SMC_REPORT;
import static com.meituan.android.hades.dyadater.container.ContainerConst.BABEL_TYPE_SMC_REPORT;
import static com.meituan.android.qtitans.container.qqflex.model.QtitansFlexPageData.from;

import android.text.TextUtils;

import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.meituan.android.dynamiclayout.utils.JsonUtil;
import com.meituan.android.hades.impl.report.BabelHelper;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.qtitans.container.qqflex.listview.QtitansFlexData;
import com.sankuai.common.utils.JsonHelper;

import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class LuckinMainPage implements JsonDeserializer<LuckinMainPage> {

    private static final String TAG = LuckinMainPage.class.getSimpleName();
    private static final String defaultShopTemplate = "{\n" +
            "    \"headertemplate\": {\n" +
            "        \"templateUrl\": \"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_4846_1734760482837.zip\",\n" +
            "        \"templateName\": \"qq_luckin_shop_feed_header\",\n" +
            "        \"style\": {\n" +
            "            \"top\": 0,\n" +
            "            \"left\": 0,\n" +
            "            \"right\": 0,\n" +
            "            \"bottom\": 12,\n" +
            "            \"span\": 1\n" +
            "        }\n" +
            "    },\n" +
            "    \"feedcardtemplate\": {\n" +
            "        \"templateUrl\": \"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_4845_1734760423174.zip\",\n" +
            "        \"templateName\": \"qq_luckin_shop_feed_card_view\",\n" +
            "        \"style\": {\n" +
            "            \"top\": 0,\n" +
            "            \"left\": 0,\n" +
            "            \"right\": 0,\n" +
            "            \"bottom\": 12,\n" +
            "            \"span\": 1\n" +
            "        }\n" +
            "    },\n" +
            "    \"dialogtemplate\": {\n" +
            "        \"templateUrl\": \"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_0_1734754280399.zip\",\n" +
            "        \"templateName\": \"qq_luckin_common_dialog\"\n" +
            "    }\n" +
            "}";
    private String defaultLuckinTitleTemplateUrl = "";
    private String defaultLuckinTitleTemplateName = "";

    public List<QtitansFlexData> spuDetailList;
    public List<QtitansFlexData> myHisOrderList;
    public List<QtitansFlexData> shopList;
    public JsonObject feedHeaderData = new JsonObject();
    public JSONObject titleBarData;
    public double lng;
    public double lat;
    public int cityId;
    public JsonObject originData;
    public JSONObject bottomNavData;
    public String pageColor;
    public String feedBackgroundColor;
    public String buttonColor;
    public String miniProgramId;
    public String shopIdEncrypt;
    public String shopId;
    public String brandId;
    public String machproId;
    public Map<String, Object> brandConfig;
    public int feedBorderTopRadius;
    public String titlebarTemplateUrl;
    public String titlebarTemplateName;
    public String feedHeaderTemplateUrl;
    public String feedHeaderTemplateName;
    private String bottomNavTemplateName;
    private String bottomNavTemplateUrl;
    private String feedNoMoreTemplateUrl;
    private String feedNoMoreTemplateName;
    public String dialogTemplateUrl;
    public String dialogTemplateName;
    public boolean isCache;


    @Override
    public LuckinMainPage deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        JsonObject rootJsonObject;
        if (json == null || (rootJsonObject = json.getAsJsonObject())== null) return null;
        Logger.d("deserialize", "start deserialize");
        LuckinMainPage mainPage = getLuckinMainPage(rootJsonObject, false);
        Logger.d("deserialize", "end deserialize");
        return mainPage;
    }

    public LuckinMainPage getLuckinMainPage(JsonObject rootJsonObject, boolean isCache) {
        LuckinMainPage luckinMainPage = new LuckinMainPage();
        luckinMainPage.isCache = isCache;
        luckinMainPage.originData = rootJsonObject;
        if (rootJsonObject.has("pageTemplate") && rootJsonObject.get("pageTemplate") instanceof JsonObject) {
            try {
                if (rootJsonObject.get("pageTemplate").getAsJsonObject().has("feedBackgroundColor")) {
                    luckinMainPage.feedBackgroundColor = rootJsonObject.get("pageTemplate").getAsJsonObject().get("feedBackgroundColor").getAsString();
                }
                if (rootJsonObject.get("pageTemplate").getAsJsonObject().has("feedBorderTopRadius")) {
                    luckinMainPage.feedBorderTopRadius = rootJsonObject.get("pageTemplate").getAsJsonObject().get("feedBorderTopRadius").getAsInt();
                }
                if (rootJsonObject.get("pageTemplate").getAsJsonObject().has("titlebarTemplateUrl")) {
                    luckinMainPage.titlebarTemplateUrl = rootJsonObject.get("pageTemplate").getAsJsonObject().get("titlebarTemplateUrl").getAsString();
                }
                if (rootJsonObject.get("pageTemplate").getAsJsonObject().has("titlebarTemplateName")) {
                    luckinMainPage.titlebarTemplateName = rootJsonObject.get("pageTemplate").getAsJsonObject().get("titlebarTemplateName").getAsString();
                }
                if (rootJsonObject.get("pageTemplate").getAsJsonObject().has("feedHeaderTemplateUrl")) {
                    luckinMainPage.feedHeaderTemplateUrl = rootJsonObject.get("pageTemplate").getAsJsonObject().get("feedHeaderTemplateUrl").getAsString();
                }
                if (rootJsonObject.get("pageTemplate").getAsJsonObject().has("feedHeaderTemplateName")) {
                    luckinMainPage.feedHeaderTemplateName = rootJsonObject.get("pageTemplate").getAsJsonObject().get("feedHeaderTemplateName").getAsString();
                }
                if (rootJsonObject.get("pageTemplate").getAsJsonObject().has("bottomNavTemplateUrl")) {
                    luckinMainPage.bottomNavTemplateUrl = rootJsonObject.get("pageTemplate").getAsJsonObject().get("bottomNavTemplateUrl").getAsString();
                }
                if (rootJsonObject.get("pageTemplate").getAsJsonObject().has("bottomNavTemplateName")) {
                    luckinMainPage.bottomNavTemplateName = rootJsonObject.get("pageTemplate").getAsJsonObject().get("bottomNavTemplateName").getAsString();
                }
                if (rootJsonObject.get("pageTemplate").getAsJsonObject().has("feedNoMoreTemplateUrl")) {
                    luckinMainPage.feedNoMoreTemplateUrl = rootJsonObject.get("pageTemplate").getAsJsonObject().get("feedNoMoreTemplateUrl").getAsString();
                }
                if (rootJsonObject.get("pageTemplate").getAsJsonObject().has("feedNoMoreTemplateName")) {
                    luckinMainPage.feedNoMoreTemplateName = rootJsonObject.get("pageTemplate").getAsJsonObject().get("feedNoMoreTemplateName").getAsString();
                }

                luckinMainPage.bottomNavData = new JSONObject();
                luckinMainPage.bottomNavData.put("templateUrl",luckinMainPage.bottomNavTemplateUrl);
                luckinMainPage.bottomNavData.put("templateName",luckinMainPage.bottomNavTemplateName);
                luckinMainPage.feedHeaderData.addProperty("templateUrl", luckinMainPage.feedHeaderTemplateUrl);
                luckinMainPage.feedHeaderData.addProperty("templateName", luckinMainPage.feedHeaderTemplateName);
            } catch (Throwable e) {
                QQThrowableReporter.report(TAG, e, true);
                HashMap<String, Object> info = new HashMap<>();
                info.put("reason", TAG + ": parse pageTemplate failed: " + e.getMessage());
                BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
            }

        }
        if (rootJsonObject.has("brandConfig") && rootJsonObject.get("brandConfig") instanceof JsonObject) {
            try {
                luckinMainPage.brandConfig = new HashMap<>();
                for (String key: rootJsonObject.get("brandConfig").getAsJsonObject().keySet()) {
                    luckinMainPage.brandConfig.put(key, ((JsonObject) rootJsonObject.get("brandConfig")).get(key).getAsString());
                }
                luckinMainPage.pageColor = String.valueOf(luckinMainPage.brandConfig.get("pageColor"));
                luckinMainPage.buttonColor =  String.valueOf(luckinMainPage.brandConfig.get("buttonColor"));
                luckinMainPage.miniProgramId = String.valueOf(luckinMainPage.brandConfig.get("miniProgramId"));
                luckinMainPage.shopIdEncrypt = String.valueOf(luckinMainPage.brandConfig.get("shopIdEncrypt"));
                luckinMainPage.machproId = String.valueOf(luckinMainPage.brandConfig.get("machproId"));
                luckinMainPage.brandId = String.valueOf(luckinMainPage.brandConfig.get("brandId"));

            } catch (Throwable e) {
                QQThrowableReporter.report(TAG, e, true);
                HashMap<String, Object> info = new HashMap<>();
                info.put("reason", TAG + ": parse brandConfig failed: " + e.getMessage());
                BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
            }
        }
        if (rootJsonObject.has("shop") && rootJsonObject.get("shop") instanceof JsonObject) {
            try {
                JsonObject jsonObject = rootJsonObject.getAsJsonObject("shop");
                for (String key:jsonObject.keySet()) {
                    luckinMainPage.feedHeaderData.add(key, jsonObject.get(key));
                    if ("shopId".equals(key)) {
                        luckinMainPage.shopId = jsonObject.get(key).getAsString();
                    }
                }

            } catch (Throwable e) {
                QQThrowableReporter.report(TAG, e, true);
                HashMap<String, Object> info = new HashMap<>();
                info.put("reason", TAG + ": parse shop failed: " + e.getMessage());
                BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
            }

        }
        if (rootJsonObject.has("myHisOrderList") && rootJsonObject.get("myHisOrderList") instanceof JsonArray) {
            try {
                JsonArray newArray = new JsonArray();
                JsonArray jsonItemsArray = rootJsonObject.getAsJsonArray("myHisOrderList");
                if (jsonItemsArray != null && jsonItemsArray.size() > 0 && jsonItemsArray.get(0).getAsJsonObject().has("style")) {
                    luckinMainPage.feedHeaderData.add("style", ((JsonObject) jsonItemsArray.get(0)).getAsJsonObject("style"));
                    if (jsonItemsArray.size() > 0) {
                        luckinMainPage.feedHeaderData.addProperty("isOderListEmpty",false);
                    } else {
                        luckinMainPage.feedHeaderData.addProperty("isOderListEmpty",true);
                    }
                    newArray.add(luckinMainPage.feedHeaderData);
                    for (int i=0; i<jsonItemsArray.size(); i++) {
                        newArray.add(jsonItemsArray.get(i));
                    }
                }
                if (newArray.size() > 0) {
                    luckinMainPage.myHisOrderList = from(newArray);
                }
            } catch (Throwable e) {
                QQThrowableReporter.report(TAG, e, true);
                HashMap<String, Object> info = new HashMap<>();
                info.put("reason", TAG + ": parse myHisOrderList failed: " + e.getMessage());
                BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
            }
        }

        if (rootJsonObject.has("spuDetailList") && rootJsonObject.get("spuDetailList") instanceof JsonArray) {
            try {
                JsonArray newArray = new JsonArray();
                JsonArray jsonItemsArray = rootJsonObject.getAsJsonArray("spuDetailList");
                if (jsonItemsArray != null && jsonItemsArray.get(0) != null && jsonItemsArray.get(0) instanceof  JsonObject && ((JsonObject) jsonItemsArray.get(0)).has("style")) {
                    JsonElement style = jsonItemsArray.get(0).getAsJsonObject().get("style");
                    luckinMainPage.feedHeaderData.add("style", style);
                    luckinMainPage.feedHeaderData.addProperty("machproId", luckinMainPage.machproId);
                    newArray.add(luckinMainPage.feedHeaderData);
                    for (int i=0; i<jsonItemsArray.size(); i++) {
                        newArray.add(jsonItemsArray.get(i));
                    }
                    JsonObject  noMore = new JsonObject();
                    noMore.addProperty("brandId", luckinMainPage.brandId);
                    noMore.addProperty("shopId", luckinMainPage.shopId);
                    noMore.addProperty("templateUrl", luckinMainPage.feedNoMoreTemplateUrl);
                    noMore.addProperty("templateName", luckinMainPage.feedNoMoreTemplateName);
                    noMore.add("style", style);
                    newArray.add(noMore);
                }
                if (jsonItemsArray != null  && jsonItemsArray.size() > 0) {
                    luckinMainPage.brandConfig.put("isSoldOut",false);
                    luckinMainPage.brandConfig.put("isCache", isCache);
                    luckinMainPage.spuDetailList = from(newArray, luckinMainPage.brandConfig);
                }
            } catch (Throwable e) {
                QQThrowableReporter.report(TAG, e, true);
                HashMap<String, Object> info = new HashMap<>();
                info.put("reason", TAG + ": parse spuDetailList failed: " + e.getMessage());
                BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
            }
        }
        if (rootJsonObject.has("shopList") && rootJsonObject.get("shopList") instanceof JsonArray) {
            JsonArray jsonItemsArray = rootJsonObject.getAsJsonArray("shopList");
            HashMap<String,Object> map = new HashMap<>();
            if (rootJsonObject.has("selectedShopIdStr")) {
                map.put("selectedShopIdStr", rootJsonObject.get("selectedShopIdStr").getAsString());
            }
            JSONArray itemsArray = JsonHelper.toJSONArray(jsonItemsArray);
            luckinMainPage.shopList = handleShopList(luckinMainPage, itemsArray, map);
        }
        if (rootJsonObject.has("topTab") && rootJsonObject.get("topTab") instanceof JsonObject) {
            try {
                JsonObject jsonObject = rootJsonObject.getAsJsonObject("topTab");
                luckinMainPage.titleBarData = JsonHelper.toJSONObject(jsonObject);
                luckinMainPage.titleBarData.put("templateUrl", luckinMainPage.titlebarTemplateUrl);
                luckinMainPage.titleBarData.put("templateName", luckinMainPage.titlebarTemplateName);
                if (luckinMainPage.brandConfig != null && !luckinMainPage.brandConfig.isEmpty()) {
                    for (String key: luckinMainPage.brandConfig.keySet()) {
                        luckinMainPage.titleBarData.put(key, luckinMainPage.brandConfig.get(key));
                    }
                }

            } catch (Throwable e) {
                QQThrowableReporter.report(TAG, e, true);
                HashMap<String, Object> info = new HashMap<>();
                info.put("reason", TAG + ": parse topTab failed: " + e.getMessage());
                BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
            }

        }
        if (rootJsonObject.has("userLocation") && rootJsonObject.get("userLocation") instanceof JsonObject) {
            if (rootJsonObject.getAsJsonObject("userLocation").has("lng")) {
                luckinMainPage.lng = rootJsonObject.getAsJsonObject("userLocation").get("lng").getAsDouble();
            }
            if (rootJsonObject.getAsJsonObject("userLocation").has("lat")) {
                luckinMainPage.lat = rootJsonObject.getAsJsonObject("userLocation").get("lat").getAsDouble();
            }
            if (rootJsonObject.getAsJsonObject("userLocation").has("cityId")) {
                luckinMainPage.cityId = rootJsonObject.getAsJsonObject("userLocation").get("cityId").getAsInt();
            }
        }

        return luckinMainPage;
    }

    private List<QtitansFlexData> handleShopList(LuckinMainPage mainPage, JSONArray data, HashMap<String, Object> properties) {
        ArrayList<QtitansFlexData> dynamicDataList = new ArrayList<>();
        try {
            String shopFeedTemplate = defaultShopTemplate;
            if (!TextUtils.isEmpty(HadesUtils.getLuckinShopFeedTemplate())) {
                shopFeedTemplate = HadesUtils.getLuckinShopFeedTemplate();
            }
            JSONObject shopFeedTemplateJsonObject = new JSONObject(shopFeedTemplate);

            JSONObject dialogTemplateJsonObject = shopFeedTemplateJsonObject.optJSONObject("dialogtemplate");
            if (dialogTemplateJsonObject != null) {
                mainPage.dialogTemplateUrl = dialogTemplateJsonObject.optString("templateUrl");
                mainPage.dialogTemplateName = dialogTemplateJsonObject.optString("templateName");
            }

            JSONObject shopHeaderJsonObject = new JSONObject();
            if (shopFeedTemplateJsonObject.has("headertemplate") && shopFeedTemplateJsonObject.get("headertemplate") instanceof  JSONObject) {
                JSONObject headerJson = (JSONObject) shopFeedTemplateJsonObject.get("headertemplate");
                Iterator<String> keys = headerJson.keys();
                while (keys.hasNext()) {
                    String key = keys.next();
                    shopHeaderJsonObject.put(key, headerJson.get(key));
                }

            }
            QtitansFlexData shopHeader = new QtitansFlexData(shopHeaderJsonObject);
            dynamicDataList.add(shopHeader);
            for (int i = 0; i < data.length(); i++) {
                try {
                    Object map = data.get(i);
                    if (map instanceof JSONObject) {
                        if (((JSONObject)map).length() > 0) {
                            if (properties != null && !properties.isEmpty()) {
                                for (String key: properties.keySet()) {
                                    ((JSONObject) map).put(key, properties.get(key));
                                }
                            }
                            if (shopFeedTemplateJsonObject.has("feedcardtemplate") && shopFeedTemplateJsonObject.get("feedcardtemplate") instanceof  JSONObject) {
                                JSONObject headerJson = (JSONObject) shopFeedTemplateJsonObject.get("feedcardtemplate");
                                Iterator<String> keys = headerJson.keys();
                                while (keys.hasNext()) {
                                    String key = keys.next();
                                    ((JSONObject) map).put(key, headerJson.get(key));
                                }

                            }

                            try {
                                String serviceTime = "今日休息中";
                                if (((JSONObject) map).has("serviceHourConfig") && ((JSONObject) map).get("serviceHourConfig") instanceof JSONObject) {
                                    if (((JSONObject) ((JSONObject) map).get("serviceHourConfig")).has("serviceHours") && ((JSONObject) ((JSONObject) map).get("serviceHourConfig")).get("serviceHours") instanceof  JSONArray) {
                                        JSONArray serviceTimeArray = ((JSONObject) ((JSONObject) map).get("serviceHourConfig")).getJSONArray("serviceHours");
                                        long now = System.currentTimeMillis() / 1000;
                                        for (int j = 0; j< serviceTimeArray.length(); j++) {
                                            JSONObject jsonObject = serviceTimeArray.getJSONObject(j);
                                            long startTime = jsonObject.getLong("startTime");
                                            long endTime = jsonObject.getLong("endTime");
                                            if (now >= startTime && now <= endTime) {
                                                serviceTime = formatTimeRange(startTime, endTime);
                                                break;
                                            }
                                        }
                                    }
                                    ((JSONObject) map).put("serviceTime",serviceTime);
                                }

                            } catch (Throwable e) {
                                QQThrowableReporter.report(TAG, e, true);
                            }

                            QtitansFlexData dynamicData = new QtitansFlexData((JSONObject) map);
                            dynamicDataList.add(dynamicData);
                        }
                    } else if (map instanceof String) {
                        JSONObject mapStr = new JSONObject((String)map);
                        QtitansFlexData dynamicData = new QtitansFlexData(mapStr);
                        dynamicDataList.add(dynamicData);
                    }

                } catch (Throwable e) {
                    QQThrowableReporter.report("QtitansFlexPageData", e, true);
                }
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansFlexPageData", e, true);
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", TAG + ": handleShopList failed: " + e.getMessage());
            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
        }

        return dynamicDataList;
    }

    private static String formatTimeRange(long startTime, long endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm", Locale.getDefault());
        String start = sdf.format(new Date(startTime * 1000)); // 转换为毫秒
        String end = sdf.format(new Date(endTime * 1000)); // 转换为毫秒
        return start + "-" + end;
    }

    public void updateUtmLchParams(String utmParams) {
        try {
            if (titleBarData != null) {
                titleBarData.put("utm_qq_widget_lch_2025", utmParams);
            }

            if (feedHeaderData != null) {
                feedHeaderData.addProperty("utm_qq_widget_lch_2025", utmParams);
            }

            if (bottomNavData != null) {
                bottomNavData.put("utm_qq_widget_lch_2025", utmParams);
            }

            if (shopList != null && !shopList.isEmpty()) {
                for (int i = 0; i < shopList.size(); i++) {
                    shopList.get(i).jsonObject.put("utm_qq_widget_lch_2025", utmParams);
                }
            }

            if (myHisOrderList != null && !myHisOrderList.isEmpty()) {
                for (int i = 0; i < myHisOrderList.size(); i++) {
                    myHisOrderList.get(i).jsonObject.put("utm_qq_widget_lch_2025", utmParams);
                }
            }
            if (spuDetailList != null && !spuDetailList.isEmpty()) {
                for (int i = 0; i < spuDetailList.size(); i++) {
                    spuDetailList.get(i).jsonObject.put("utm_qq_widget_lch_2025", utmParams);
                }
            }
        } catch (Throwable e) {
            QQThrowableReporter.report(TAG, e, true);
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", TAG + ": update utm params failed: " + e.getMessage());
            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
        }

    }
}
