package com.meituan.android.qtitans.container.config;

import static com.meituan.android.hades.dyadater.container.ContainerUtilsAdapter.getHadesWidgetEnum;

import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.support.annotation.IdRes;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v7.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Pair;
import android.view.Choreographer;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Space;
import android.widget.TextView;

import com.facebook.react.bridge.ReactContext;
import com.facebook.react.uimanager.UIManagerModule;
import com.facebook.react.uimanager.events.Event;
import com.facebook.react.uimanager.events.EventDispatcherListener;
import com.meituan.android.aurora.ActivitySwitchCallbacks;
import com.meituan.android.hades.Hades;
import com.meituan.android.hades.HadesWidgetEnum;
import com.meituan.android.hades.WidgetAddStrategyEnum;
import com.meituan.android.hades.dyadater.container.interfaces.IContainerConfig;
import com.meituan.android.hades.impl.BuildConfig;
import com.meituan.android.hades.impl.R;
import com.meituan.android.hades.impl.config.HadesConfigMgr;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Lifecycle;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.mrn.container.MRNBaseActivity;
import com.meituan.android.mrn.container.MRNSceneCompatDelegate;
import com.meituan.android.pin.Pin;
import com.meituan.android.pin.PinCheckResult;
import com.meituan.android.qtitans.container.bean.PopupScene;
import com.meituan.android.qtitans.container.bean.QtitansContainerConfig;
import com.meituan.android.qtitans.container.bean.QtitansContainerParams;
import com.meituan.android.qtitans.container.bean.QtitansToolBar;
import com.meituan.android.hades.dyadater.container.QtitansContainerVisitType;
import com.meituan.android.qtitans.container.common.QtitansLog;
import com.meituan.android.qtitans.container.common.QtitansUtils;
import com.meituan.android.qtitans.container.model.QContainerConfigImpl;
import com.meituan.android.hades.dyadater.container.interfaces.ContainerConfigCallback;
import com.meituan.android.qtitans.container.reporter.ContainerReporter;
import com.meituan.android.qtitans.container.ui.view.QtitansTitleBar;
import com.meituan.msc.common.utils.StatusBarUtils;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class ContainerConfigManager implements IContainerConfig {
    private static final String TAG = "ContainerConfigManager";
    private QtitansContainerParams mQtitansContainerParams;

    public void setQtitansContainerParams(JSONObject qtitansContainerParams) {
        this.qtitansContainerParams = qtitansContainerParams;
    }

    private JSONObject qtitansContainerParams;

    public static final long BLOCK_TIME = 1000;

    /**
     * configType
     */
    public static final String KEY_BEHAVIOR_SUBSCRIBE = "key_behavior_subscribe";
    public static final String KEY_CONTAINER = "container";
    public static final String KEY_CONTAINER_EXTERNAL_LINK = "containerExternalLink";

    private final HashMap<String, ArrayList<View>> movedMap = new HashMap<>();
    private final HashMap<String, ArrayList<Pair<IconGroup, View>>> changedMap = new HashMap<>();
    private final QContainerConfigImpl mQContainerConfigImpl;

    public ActivitySwitchCallbacks lifecycleCallback;
    public ActivitySwitchCallbacks backGroundLifecycleCallback;
    private WeakReference<QtitansTitleBar> mTitleBar = new WeakReference<>(null);

    Map<String, String> externalLinkInfo = new HashMap<>();
    private QtitansToolBar mQtitansToolBar;

    public QtitansToolBar getQtitansToolBar() {
        return mQtitansToolBar;
    }

    public void setQtitansToolBar(@Nullable QtitansToolBar qtitansTitleBar) {
        this.mQtitansToolBar = qtitansTitleBar;
    }

    private static volatile ContainerConfigManager sInstance;

    private ContainerConfigManager() {
        mQContainerConfigImpl = new QContainerConfigImpl();
    }

    public static ContainerConfigManager getInstance() {
        if (sInstance == null) {
            synchronized (ContainerConfigManager.class) {
                if (sInstance == null) {
                    sInstance = new ContainerConfigManager();
                }
            }
        }
        return sInstance;
    }

    @Override
    public void init() {
        if (lifecycleCallback == null) {
            lifecycleCallback = new ActivitySwitchCallbacks() {
                @Override
                public void onActivityResumed(Activity activity) {
                    parseConfig(activity);
                }
            };
        }
        Lifecycle.registerCallbackRemoveDuplication(backGroundLifecycleCallback);
        Lifecycle.registerCallbackRemoveDuplication(lifecycleCallback);
    }

    @Override
    public void removeLifeCycleListener() {
        if (lifecycleCallback != null) {
            Lifecycle.unRegisterCallback(lifecycleCallback);
        }
        if (mTitleBar != null) {
            mTitleBar.clear();
            mTitleBar = null;
        }
    }


    public void removeBackGroundLifeCycleListener() {
        if (backGroundLifecycleCallback != null) {
            Lifecycle.unRegisterCallback(backGroundLifecycleCallback);
        }
    }

    public void parseConfig(Activity activity) {
        PageConfig pageConfig = getPageConfig();
        if (pageConfig == null) return;
        disPatchConfigType(activity, pageConfig);
    }

    private void disPatchConfigType(Activity activity, PageConfig pageConfig) {
        switch (pageConfig.type) {
            case 1: {
                handleConfigView(activity, pageConfig);
                break;
            }
            case 2:
            case 3: {
                handleNativeMRNConfigView(activity, pageConfig);
                break;
            }
            default:
                handleConfigView(activity, pageConfig);
                break;
        }

    }

    private void handleNativeMRNConfigView(Activity activity, PageConfig pageConfig) {
        if (activity == null) return;
        if (pageConfig.pages == null || pageConfig.pages.isEmpty()) {
            Logger.d(TAG, "config pages is empty!");
            return;
        }
        String classPathName = activity.getClass().getName();
        Page page = findPagesAccordingToClassPath(classPathName, pageConfig);
        if (page == null) {
            Logger.d(TAG, "page not found in page list!");
            return;
        }
        realHandleConfigView(activity, pageConfig, page);

    }

    private Page findPagesAccordingToClassPath(String classPathName, PageConfig pageConfig) {
        return pageConfig.getPageDataByBundleId(classPathName);
    }


    public void handleConfigView(Activity activity, PageConfig config) {
        try {
            if (!(activity instanceof MRNBaseActivity)) {
                Logger.d(TAG, "not support non mrn activity!");
                return;
            }
            if (config == null || config.pages.isEmpty()) {
                Logger.d(TAG, "config is empty");
                return;
            }
            MRNSceneCompatDelegate delegate = ((MRNBaseActivity) activity).getMRNDelegate();
            if (delegate == null) {
                Logger.d(TAG, "mrn delegate is null");
                return;
            }
            String bundleId = delegate.getBundleName();
            Page page = config.getPageDataByBundleId(bundleId);
            if (page == null) {
                Logger.d(TAG, "not supported page " + bundleId);
                return;
            }
            realHandleConfigView(activity, config, page);
        } catch (Throwable e) {
            Logger.e(TAG, "handle config view failed: " + e.getMessage());
            QQThrowableReporter.report("ContainerConfigManager", e);
        }
    }

    public void realHandleConfigView(@NonNull Activity activity, @NonNull PageConfig config, @NonNull Page page) {
        try {
            ViewGroup viewGroup = (ViewGroup) activity.getWindow().getDecorView();
            initMoveAndChangeData(page);
            viewGroup.postDelayed(() -> {
                if (isHandlePage(viewGroup)) {
                    Logger.d(TAG, "Page has handled!");
                    return;
                }
                setupScrollViewListener(activity, viewGroup, page);
                handleHideViews(activity, viewGroup, page, config.type);
                handleMovedViews(activity, viewGroup, page, config.type);
                handleViewGroup(activity, viewGroup, page, config.type);
                viewGroup.postDelayed(() -> {
                    adaptTitleBar(activity, page, config.type);
                }, 10);
            }, page.showDelay);

            if (BuildConfig.LOG_ENABLE) {
                showDebugRect(activity, page);
            }

        } catch (Throwable e) {
            Logger.e(TAG, "config view failed: " + e.getMessage());
            QQThrowableReporter.report("ContainerConfigManager", e);
        }

    }

    private void initMoveAndChangeData(Page page) {
        String key = page.bundleId;
        if (TextUtils.isEmpty(key)) return;
        if (movedMap.get(key) == null) {
            movedMap.put(page.bundleId, new ArrayList<>());
        }
        if (changedMap.get(key) == null) {
            changedMap.put(page.bundleId, new ArrayList<>());
        }
    }

    private void setupScrollViewListener(Activity activity, ViewGroup viewGroup, Page page) {
        if (page == null || page.scrollViewConfig == null) return;
        RecyclerView.OnScrollListener scrollListener = new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                // do nothing
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                try {
                    handleScroll(() -> Math.abs(dy) < QtitansUtils.dip2px(recyclerView.getContext(), page.scrollViewConfig.scrollDistance == 0 ? 200 : page.scrollViewConfig.scrollDistance), page);
                } catch (Throwable e) {
                    Logger.e(TAG, "setupScrollViewListener onScrolled failed: " + e.getMessage());
                    QQThrowableReporter.report("ContainerConfigManager", e);
                }
            }
        };
        findScrollViewGroup(viewGroup, page, page.scrollViewConfig, scrollListener);
    }

    private void findScrollViewGroup(ViewGroup viewGroup, Page page, ScrollViewConfig scrollViewConfig, RecyclerView.OnScrollListener scrollListener) {
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View view = viewGroup.getChildAt(i);
            if (view.getHeight() <= 0 || view.getWidth() <= 0)
                continue;
            if (view instanceof ViewGroup) {
                if (handleScrollType(page, scrollViewConfig, scrollListener, view)) return;
                findScrollViewGroup((ViewGroup) view, page, scrollViewConfig, scrollListener);
            }
        }
    }

    private boolean handleScrollType(Page page, ScrollViewConfig scrollViewConfig, RecyclerView.OnScrollListener scrollListener, View view) {
        if (scrollViewConfig.scrollType == 1) {
            if (view instanceof RecyclerView) {
                ((RecyclerView) view).removeOnScrollListener(scrollListener);
                ((RecyclerView) view).addOnScrollListener(scrollListener);
                return true;
            }
        } else if (scrollViewConfig.scrollType == 2) {
            if (view.getClass().getName().equals(scrollViewConfig.scrollViewId)) {
                if (view.getContext() instanceof ReactContext) {
                    ((ReactContext) view.getContext()).getNativeModule(UIManagerModule.class).getEventDispatcher().addListener(new EventDispatcherListener() {
                        @Override
                        public void onEventDispatch(Event event) {
                            try {
                                if (event.getViewTag() == view.getId() && "onScroll".equals(event.getEventName())) {
                                    handleScroll(() -> true, page);
                                }
                            } catch (Throwable e) {
                                Logger.e(TAG, "handle onEventDispatch failed: " + e.getMessage());
                                QQThrowableReporter.report("ContainerConfigManager", e);
                            }

                        }
                    });
                }
                return true;
            }
        }
        return false;
    }

    private void handleScroll(BooleanFlag flag, Page page) {
        if (flag.isTrue()) {
            reMeasureRNView(page);
        }
    }

    private void handleViewGroup(Activity activity, ViewGroup viewGroup, Page page, int type) {
        ArrayList<Pair<IconGroup, View>> changedList = changedMap.get(page.bundleId);
        if (changedList == null) return;
        changedList.clear();
        if (page.changedList == null || page.changedList.isEmpty()) return;
        if (viewGroup == null) return;
        for (IconGroup group : page.changedList) {
            findViewGroup(activity, viewGroup, group, page, changedList);
        }
    }

    private void findViewGroup(Activity activity, ViewGroup viewGroup, IconGroup group, Page page, ArrayList<Pair<IconGroup, View>> changedList) {
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View view = viewGroup.getChildAt(i);
            if (view.getHeight() <= 0 || view.getWidth() <= 0)
                continue;
            if (view instanceof ViewGroup) {
                Rect groupRect = QtitansUtils.getViewLocationOnScreen(view);
                Rect rect = new Rect(
                        QtitansUtils.dip2px(view.getContext(), group.x),
                        QtitansUtils.dip2px(view.getContext(), group.y),
                        QtitansUtils.dip2px(view.getContext(), group.x + group.width),
                        QtitansUtils.dip2px(view.getContext(), group.y + group.height)
                );
                if (rect.contains(groupRect) && ((ViewGroup) view).getChildCount() >= group.minimalChildCount) {
                    if (group.groupTop <= 0) {
                        group.groupTop = QtitansUtils.px2dip(view.getContext(), view.getTop());
                    }
                    group.realWidth = group.changedWidth + QtitansUtils.px2dip(view.getContext(), view.getWidth());
                    changedList.add(new Pair<>(group, view));
                    requestRNViewGroupLayout((ViewGroup) view,
                            QtitansUtils.dip2px(view.getContext(), group.groupLeft),
                            QtitansUtils.dip2px(view.getContext(), group.groupTop),
                            QtitansUtils.dip2px(view.getContext(), group.changedWidth) + view.getWidth(),
                            QtitansUtils.dip2px(view.getContext(), group.realHeight));

                    return;
                }
                findViewGroup(activity, (ViewGroup) view, group, page, changedList);
            }

        }
    }

    private boolean isHandlePage(ViewGroup viewGroup) {
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View view = viewGroup.getChildAt(i);
            if (view instanceof QtitansTitleBar) {
                return true;
            }
        }
        return false;
    }

    public void handleMovedViews(Activity activity, ViewGroup viewGroup, Page page, int type) {
        ArrayList<View> movedList = movedMap.get(page.bundleId);
        if (movedList == null) return;
        movedList.clear();
        if (page.moveIconList == null || page.moveIconList.isEmpty()) return;
        if (viewGroup == null) return;
        for (Icon icon : page.moveIconList) {
            findView(viewGroup, icon, page, movedList);
        }
    }

    public void handleHideViews(Activity activity, ViewGroup viewGroup, Page page, int type) {
        if (page.hideIconList == null || page.hideIconList.isEmpty()) return;
        ArrayList<View> list = new ArrayList<>();
        for (Icon icon : page.hideIconList) {
            findView(viewGroup, icon, page, list);
            if (!list.isEmpty()) {
                for (View view : list) {
                    if (type == PageConfigType.NATIVE.getType()) {
                        if (icon.visibility == Visibility.GONE.getVisibleType()) {
                            view.setVisibility(View.GONE);
                        } else {
                            view.setVisibility(View.INVISIBLE);
                            ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
                            if (layoutParams == null) {
                                layoutParams = new ViewGroup.LayoutParams(QtitansUtils.dip2px(view.getContext(), 1), view.getHeight());
                            }
                            layoutParams.width = QtitansUtils.dip2px(view.getContext(), 1);
                            view.setLayoutParams(layoutParams);
                        }
                    } else {
                        if (view.getParent() != null && view.getParent() instanceof ViewGroup) {
                            ViewGroup parent = (ViewGroup) view.getParent();
                            if (icon.isAnimated) {
                                ViewParent rootParent = parent.getParent();
                                if (rootParent instanceof ViewGroup) {
                                    ((ViewGroup) rootParent).removeView(parent);
                                }
                            } else {
                                parent.removeView(view);
                            }
                        }
                    }

                }

            }
        }
    }

    public void showDebugRect(Activity activity, Page page) {
        if (BuildConfig.LOG_ENABLE) {
            try {
                if (page.hideIconList == null
                        || page.hideIconList.isEmpty()
                        || activity == null
                        || page == null) {
                    return;
                }
                for (Icon icon : page.hideIconList) {
                    if (icon == null) {
                        return;
                    }
                    FrameLayout viewGroup = (FrameLayout) activity.getWindow().getDecorView();
                    TextView textView = new TextView(activity);
                    textView.setBackgroundColor(0x30FF0000);
                    FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(QtitansUtils.dip2px(activity, icon.width),
                            QtitansUtils.dip2px(activity, icon.height));
                    params.topMargin = QtitansUtils.dip2px(activity, icon.y);
                    params.leftMargin = QtitansUtils.dip2px(activity, icon.x);
                    viewGroup.addView(textView, params);
                }
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerConfigManager", e);
            }
        }
    }

    private void findView(@NonNull ViewGroup viewGroup, Icon icon, Page page, ArrayList<View> list) {
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View view = viewGroup.getChildAt(i);
            if (view.getHeight() <= 0 || view.getWidth() <= 0)
                continue;
            if (view instanceof ViewGroup) {
                findView((ViewGroup) view, icon, page, list);
            } else {
                Rect rect = new Rect(
                        QtitansUtils.dip2px(view.getContext(), icon.x),
                        QtitansUtils.dip2px(view.getContext(), icon.y),
                        QtitansUtils.dip2px(view.getContext(), icon.x + icon.width),
                        QtitansUtils.dip2px(view.getContext(), icon.y + icon.height)
                );
                Rect viewBound = QtitansUtils.getViewLocationOnScreen(view);
                if (rect.contains(viewBound) && !isExcludeId(view.getId())) {
                    list.add(view);
                }
            }

        }

    }

    private boolean isExcludeId(@IdRes int id) {
        return id == R.id.container_more_left
                || id == R.id.container_more_right
                || id == R.id.line;
    }

    private void reMeasureRNView(Page page) {
        ArrayList<Pair<IconGroup, View>> changedList = changedMap.get(page.bundleId);
        if (changedList == null) return;
        for (Pair<IconGroup, View> pair : changedList) {
            IconGroup group = pair.first;
            View view = pair.second;
            if (group.groupTop <= 0) {
                group.groupTop = QtitansUtils.px2dip(view.getContext(), view.getTop());
            }
            requestRNViewGroupLayout((ViewGroup) view,
                    QtitansUtils.dip2px(view.getContext(), group.groupLeft),
                    QtitansUtils.dip2px(view.getContext(), group.groupTop),
                    QtitansUtils.dip2px(view.getContext(), group.realWidth),
                    QtitansUtils.dip2px(view.getContext(), group.realHeight));

        }
    }

    private void requestRNViewGroupLayout(ViewGroup viewGroup, int left, int top, int width, int height) {
        Choreographer.getInstance().postFrameCallback(new Choreographer.FrameCallback() {
            @Override
            public void doFrame(long frameTimeNanos) {
                viewGroup.measure(View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY),
                        View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.EXACTLY));
                viewGroup.layout(left, top, width, height);
                viewGroup.getViewTreeObserver().dispatchOnGlobalLayout();
            }


        });

    }

    private void adaptTitleBar(Activity activity, Page page, int type) {
        if (activity == null || QtitansUtils.isActivityFinish(activity)
                || activity.getWindow() == null || page == null || !page.showTitlebar) {
            if (BuildConfig.LOG_ENABLE) {
                Logger.d("adaptTitleBar error");
            }
            return;
        }
        mTitleBar = new WeakReference<>(new QtitansTitleBar(activity));
        QtitansTitleBar titleBar = mTitleBar.get();
        titleBar.init(
                activity,
                page.capsule == null ? 1 : page.capsule.type,
                mQtitansToolBar,
                page.isHome || page.isBusinessHome ? null : v -> activity.finish()
        );
        if (!TextUtils.isEmpty(page.title)) {
            titleBar.setTitle(page.title);
        }
        titleBar.setCapsuleVisibility(page.showCapsule);
        int colorInt = page.getBackGroundColor();
        titleBar.setBackground(new ColorDrawable(colorInt));
        if (page.isImersive) {
            StatusBarUtils.compatStatusBar(activity);
            StatusBarUtils.setStatusBarTextColor(activity, true);
        }
        titleBar.setStatusBarHeight(QtitansUtils.getStatusBarHeight());
        ViewGroup decorView = (ViewGroup) activity.getWindow().getDecorView();
        int titleBarHeight = QtitansUtils.dip2px(activity, 48) + QtitansUtils.getStatusBarHeight();
        FrameLayout.LayoutParams titleParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, titleBarHeight);
        decorView.addView(mTitleBar.get(), decorView.getChildCount(), titleParams);
        if (decorView.getChildAt(0) instanceof LinearLayout) {
            LinearLayout linearLayout = (LinearLayout) decorView.getChildAt(0);
            int height = page.isImersive ? QtitansUtils.getStatusBarHeight() + QtitansUtils.dip2px(activity, page.extraTitleBarSpace) : QtitansUtils.dip2px(activity, page.extraTitleBarSpace);
            Space space = new Space(activity);
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, height);
            space.setLayoutParams(layoutParams);
            linearLayout.addView(space, 0); //添加到顶部
        }
        addMovedViewToTitleBar(page, type);
    }

    private void addMovedViewToTitleBar(Page page, int type) {
        ArrayList<View> movedList = movedMap.get(page.bundleId);
        if (mTitleBar == null || mTitleBar.get() == null || movedList == null || movedList.isEmpty())
            return;
        for (View view : movedList) {
            if (view != null && mTitleBar != null && mTitleBar.get() != null) {
                View.OnClickListener listener = ViewClickSetupUtils.getClickListenerFromSuperClass(view);
                if (view instanceof ImageView) {
                    ImageView imageView = new ImageView(view.getContext());
                    LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(view.getWidth(), view.getHeight());
                    if (listener != null) {
                        imageView.setOnClickListener(v -> {
                            if (page.clickType == 1) {
                                listener.onClick(view);
                            } else {
                                if (PageConfigType.MRN.getType() == type && view.getParent() != null && view.getParent() instanceof ViewGroup) {
                                    ((ViewGroup) view.getParent()).performClick();
                                } else {
                                    view.performClick();
                                }
                            }
                        });
                    }

                    layoutParams.gravity = Gravity.CENTER;
                    imageView.setLayoutParams(layoutParams);
                    imageView.setBackground(((ImageView) view).getDrawable());
                    mTitleBar.get().addBizView(imageView);
                } else if (view instanceof TextView) {
                    TextView textView = new TextView(view.getContext());
                    LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(view.getWidth(), view.getHeight());
                    if (listener != null) {
                        textView.setOnClickListener(v -> {
                            if (page.clickType == 1) {
                                listener.onClick(view);
                            } else {
                                if (PageConfigType.MRN.getType() == type && view.getParent() != null && view.getParent() instanceof ViewGroup) {
                                    ((ViewGroup) view.getParent()).performClick();
                                } else {
                                    view.performClick();
                                }
                            }
                        });
                    }
                    layoutParams.gravity = Gravity.CENTER;
                    textView.setLayoutParams(layoutParams);
                    textView.setTextColor(((TextView) view).getCurrentTextColor());
                    textView.setTextSize(((TextView) view).getTextSize());
                    textView.setText(((TextView) view).getText());
                    mTitleBar.get().addBizView(textView);
                }
            }
        }
    }

    public Pair<String, Boolean> autoInstallCheckResult() {
        try {
            String checkSource = getExternalLinkInfo().get("checkSource");
            if (TextUtils.isEmpty(checkSource)) {
                return new Pair<>("", false);
            }
            HadesWidgetEnum hadesWidgetEnum = getHadesWidgetEnum(checkSource);
            if (hadesWidgetEnum == null) {
                return new Pair<>("", false);
            }
            PinCheckResult pinCheckResult = Pin.getDeskAppPinCheckResult(checkSource);
            return new Pair<>(checkSource, pinCheckResult != null
                    && pinCheckResult.pinCheckResult
                    && pinCheckResult.strategy == WidgetAddStrategyEnum.SILENT
                    && pinCheckResult.isAutoInstall);
        } catch (Throwable e) {
            QQThrowableReporter.report("ContainerConfigManager", e);
        }
        return new Pair<>("", false);
    }

    public Map<String, String> getExternalLinkInfo() {
        if (externalLinkInfo == null) {
            externalLinkInfo = new HashMap<>();
        }
        return externalLinkInfo;
    }

    public void clear(String type) {
        sInstance = null;
        mQtitansToolBar = null;
        mQtitansContainerParams = null;
        externalLinkInfo = new HashMap<>();
        if (BuildConfig.LOG_ENABLE) {
            QtitansLog.log("clear: " + type);
        }
    }

    public QtitansContainerParams getQtitansContainerParams() {
        return mQtitansContainerParams;
    }

    @Override
    public void updateQtitansContainerParams(String qtitansContainerParams) {
        mQtitansContainerParams = HadesUtils.fromJson(qtitansContainerParams, QtitansContainerParams.class);
        externalLinkInfo = new HashMap<>();
        if (BuildConfig.LOG_ENABLE) {
            QtitansLog.log("updateQtitansContainerParams: " +  qtitansContainerParams);
        }
    }

    public boolean isSameBusiness(QtitansContainerParams params) {
        try {
            boolean isOpen = HadesConfigMgr.getInstance(HadesUtils.getContext()).sameBusinessCheck();
            if (BuildConfig.LOG_ENABLE) {
                Logger.d("isSameBusiness horn =  " + isOpen);
            }
            if (!isOpen) {
                return false;
            }
            Pair<Boolean, String> res;
            if (mQtitansContainerParams == null || params == null) {
                res = new Pair<>(false, "params empty  = ");
            } else if (mQtitansContainerParams.getLoadingViewParams() == null || params.getLoadingViewParams() == null) {
                res = new Pair<>(false, "getLoadingViewParams empty");
            } else if (!getCurrentCheckSource().equals(params.getLoadingViewParams().getCheckSource())) {
                res = new Pair<>(false, "checkSource");
            } else if (!getCurrentBusinessType().equals(params.getLoadingViewParams().getBusinessType())) {
                res = new Pair<>(false, "businessType");
            } else if (!getCurrentTargetUrl().equals(params.getTargetUrl())) {
                res = new Pair<>(false, "targetUrl");
            } else if (!mQtitansContainerParams.getJumpScene().equals(params.getJumpScene())) {
                res = new Pair<>(false, "jumpScene");
            } else if (!TextUtils.isEmpty(mQtitansContainerParams.getLoadingViewParams().getVisitType())
                    && !mQtitansContainerParams.getLoadingViewParams().getVisitType().equals(params.getLoadingViewParams().getVisitType())) {
                res = new Pair<>(false, "visitType");
            } else if (!TextUtils.isEmpty(mQtitansContainerParams.getLoadingViewParams().getFunctionType())
                    && !mQtitansContainerParams.getLoadingViewParams().getFunctionType().equals(params.getLoadingViewParams().getFunctionType())) {
                res = new Pair<>(false, "functionType");
            } else {
                res = new Pair<>(true, "");
            }
            Map<String, Object> log = new HashMap<>();
            log.put("tag", TAG);
            log.put("error", res.second);
            ContainerReporter.reportLog("isSameBusiness", log);
            return res.first;
        } catch (Throwable e) {
            QQThrowableReporter.report("ContainerConfigManager", e);
        }
        return false;
    }

    public String getCurrentTargetUrl() {
        try {
            if (mQtitansContainerParams != null
                    && !TextUtils.isEmpty(mQtitansContainerParams.getTargetUrl())) {
                return mQtitansContainerParams.getTargetUrl();
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("ContainerConfigManager", e);
        }
        return "";
    }
    @Override
    public String getCurrentCheckSource() {
        try {
            if (mQtitansContainerParams != null
                    && mQtitansContainerParams.getLoadingViewParams() != null
                    && !TextUtils.isEmpty(mQtitansContainerParams.getLoadingViewParams().getCheckSource())) {
                return mQtitansContainerParams.getLoadingViewParams().getCheckSource();
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("ContainerConfigManager", e);
        }
        return "";
    }

    @Override
    public String getCurrentBusinessType() {
        try {
            if (mQtitansContainerParams != null
                    && mQtitansContainerParams.getLoadingViewParams() != null
                    && !TextUtils.isEmpty(mQtitansContainerParams.getLoadingViewParams().getBusinessType())) {
                return mQtitansContainerParams.getLoadingViewParams().getBusinessType();
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("ContainerConfigManager", e);
        }
        return "";
    }

    public String getVisitType() {
        try {
            if (mQtitansContainerParams != null
                    && mQtitansContainerParams.getLoadingViewParams() != null
                    && !TextUtils.isEmpty(mQtitansContainerParams.getLoadingViewParams().getVisitType())) {
                return mQtitansContainerParams.getLoadingViewParams().getVisitType();
            }
        } catch (Throwable e) {
            if (BuildConfig.LOG_ENABLE) {
                e.printStackTrace();
            }
        }
        return "";
    }

    @Override
    public void requestContainerConfig(Context context,
                                       String businessType,
                                       String checkSource,
                                       String configType,
                                       String urlId,
                                       ContainerConfigCallback callback) {
        if (BuildConfig.LOG_ENABLE) {
            Logger.d(TAG, "requestContainerConfig: businessType = " + businessType
                    + ", " + "checkSource = " + checkSource + ",configType = " + configType);
        }
        if (TextUtils.isEmpty(checkSource) || TextUtils.isEmpty(businessType) || TextUtils.isEmpty(configType)) {
            callback.onGetConfigFail("params null");
            return;
        }

        mQContainerConfigImpl.requestQContainerConfig(context, businessType, checkSource, configType, urlId, callback);
    }

    public PageConfig getPageConfig() {
        return mQContainerConfigImpl.getPageConfig();
    }

    public QtitansContainerConfig getContainerConfig() {
        return mQContainerConfigImpl.getContainerConfig();
    }

    public PageConfig getContainerPageConfig(QtitansConfig config) {
        return mQContainerConfigImpl.getPageConfigInner(config);
    }

    public PopupScene getVisitPopupScene() {
        if (getPageConfig() == null) {
            return PopupScene.ENTER_POPUP;
        }
        return PopupScene.containType(getPageConfig().visitPopupScene);
    }

    public long getBlockTime() {
        if (getPageConfig() == null) {
            return BLOCK_TIME;
        }
        return getPageConfig().blockTime <= 0 ?
                BLOCK_TIME
                : getPageConfig().blockTime;
    }

    //https://km.sankuai.com/collabpage/1895961839
    public int getDeskAppDefaultIcon(String checkSource) {
        HadesWidgetEnum widgetEnum = getHadesWidgetEnum(checkSource);
        if (widgetEnum == null) {
            return -1;
        }
        return Hades.getInstance(HadesUtils.getContext()).getDeskAppDefaultIcon(widgetEnum);
    }

    public String getDeskAppDefaultName(String checkSource) {
        HadesWidgetEnum widgetEnum = getHadesWidgetEnum(checkSource);
        if (widgetEnum == null) {
            return "";
        }
        return Hades.getInstance(HadesUtils.getContext()).getDeskAppDefaultName(widgetEnum);
    }

    public boolean isVideo(String mParams) {
        try {
            if (TextUtils.isEmpty(mParams)) {
                return false;
            }
            JSONObject jsonObject = new JSONObject(mParams);
            JSONObject loadingViewParams = jsonObject.optJSONObject("loadingViewParams");
            if (loadingViewParams != null) {
                return "30001".equals(loadingViewParams.optString("checkSource"));
            }
        } catch (Throwable throwable) {
            QQThrowableReporter.report("ContainerConfigManager", throwable);
        }
        return false;
    }

    public boolean isFormMtAPPVisit(String params) {
        try {
            if (TextUtils.isEmpty(params)) {
                return false;
            }
            JSONObject jsonObject = new JSONObject(params);
            JSONObject loadingViewParams = jsonObject.optJSONObject("loadingViewParams");
            return loadingViewParams != null
                    && QtitansContainerVisitType.containType(loadingViewParams.getString("visitType")) == QtitansContainerVisitType.VisitMTAPP;
        } catch (Throwable throwable) {
            QQThrowableReporter.report("ContainerConfigManager", throwable);
        }
        return false;
    }
}