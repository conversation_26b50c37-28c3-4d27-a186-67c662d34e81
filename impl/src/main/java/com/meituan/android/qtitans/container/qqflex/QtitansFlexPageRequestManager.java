package com.meituan.android.qtitans.container.qqflex;

import android.content.Context;
import android.net.Uri;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.common.fingerprint.FingerprintManager;
import com.meituan.android.hades.impl.BuildConfig;
import com.meituan.android.hades.impl.model.BaseResponse;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.qtitans.container.qqflex.model.QtitansFlexPageData;
import com.meituan.android.hades.dyadater.container.PreloadState;
import com.meituan.android.qtitans.container.qqflex.model.WifiInfo;
import com.meituan.android.singleton.FingerprintManagerSingleton;
import com.sankuai.meituan.retrofit2.Response;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Set;

public class QtitansFlexPageRequestManager {

    private static final String TAG = "QtitansFlexPageRequestManager";
    private static final QtitansFlexPageRequestManager INSTANCE = new QtitansFlexPageRequestManager();
    private IFlexPageRequestCallback preloadCallback;

    private int mPageNum = 1;

    private String mGlobalId;

    private final HashMap<String, Object> queryMap = new HashMap<>();

    private ArrayList<WifiInfo> wifiInfos;

    public String getTargetUrl() {
        return targetUrl;
    }

    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }

    private String targetUrl;

    public PreloadState getPreloadState() {
        return preloadState;
    }

    public void setPreloadState(PreloadState preloadState) {
        this.preloadState = preloadState;
    }

    private PreloadState preloadState;
    private String fingerprint;

    public QtitansFlexPageData getFlexPageData() {
        return flexPageData;
    }

    public void setFlexPageData(QtitansFlexPageData flexPageData) {
        this.flexPageData = flexPageData;
    }

    private QtitansFlexPageData flexPageData;

    private QtitansFlexPageRequestManager() {
        HadesUtils.runOnWorkThread(()->{
            try {
                FingerprintManager fingerprintManager = FingerprintManagerSingleton.getInstance();
                if (fingerprintManager != null && !TextUtils.isEmpty(fingerprintManager.fingerprint())) {
                    fingerprint = Uri.encode(fingerprintManager.fingerprint());
                }
            } catch (Throwable e) {
                QQThrowableReporter.report(TAG, e, true);
            }

        });
    }

    public static QtitansFlexPageRequestManager getInstance() {
        return INSTANCE;
    }


    public void requestSupplyDetailPageData(Context context, Uri uri, double lat, double lng,  IFlexPageRequestCallback callback) {
        getQueryMapFromUri(uri);
        requestSupplyDetailPageData(context, queryMap, lat, lng, callback);

    }

    private void getQueryMapFromUri(Uri uri) {
        Set<String> params = uri.getQueryParameterNames();
        if (params != null && !params.isEmpty()) {
            for (String param: params) {
                if (!queryMap.containsKey(param)){
                    queryMap.put(param, uri.getQueryParameter(param));
                }
            }
        }
    }

    public void requestSupplyDetailPageData(Context context, HashMap<String, Object> queryMap, double lat, double lng, IFlexPageRequestCallback callback) {
        mPageNum = 1;
        mGlobalId = "";
        requestFeedData(context, queryMap, mGlobalId, lng, lat, mPageNum, 6, null, callback);
    }

    public void requestLoadMoreSupplyPageData(Context context, double lat, double lng, IFlexPageRequestCallback callback) {
        requestFeedData(context, queryMap, mGlobalId, lng, lat, ++mPageNum, 10, wifiInfos, callback);
    }

    public void locationWifiRequestSupplyPageData(Context context, @Nullable Uri uri, double lng, double lat, ArrayList<WifiInfo> wifiList, IFlexPageRequestCallback callback) {
        mPageNum = 1;
        this.wifiInfos = wifiList;
        requestFeedData(context, queryMap, "", lng, lat, 1, 6, wifiList, callback);
    }

    private void requestFeedData(Context context, HashMap<String, Object> queryMap , String globalId, double lng, double lat, int pageNum, int pageSize, ArrayList<WifiInfo> wifiInfos, IFlexPageRequestCallback callback) {
        HashMap<String, Object> map = new HashMap<>(queryMap);
        map.put("globalId", globalId);
        map.put("pageNum",pageNum);
        map.put("pageSize", pageSize);
        map.put("lng", lng);
        map.put("lat", lat);
        map.put("wifiInfos", wifiInfos);
        map.put("isNative",true);
        map.put("fingerprint", fingerprint);
        HadesUtils.runOnWorkThread(()->{
            try {
                Response<BaseResponse<QtitansFlexPageData>> response = QtitansFlexRetrofit.getInstance(context).getSupplyDetails(map).execute();
                if (response != null && response.body() != null && response.body().hasData()) {
                    flexPageData = response.body().data;
                    if (preloadState != null && PreloadState.PRELOAD_START.ordinal() == preloadState.ordinal()) {
                        callback.onSuccess(null);
                        if (preloadCallback != null) {
                            preloadCallback.onSuccess(flexPageData);

                        }
                    } else {
                        callback.onSuccess(flexPageData);
                    }
                    if (flexPageData != null) {
                        mGlobalId = flexPageData.globalId;
                    }


                } else {
                    callback.onFailed("no data");
                }
            } catch (Throwable e) {
                if (BuildConfig.LOG_ENABLE) {
                    Logger.e(TAG, "request data failed: "+e.getMessage());
                }
                callback.onFailed(e.getMessage());

            }
        });
    }

    public IFlexPageRequestCallback getPreloadCallback() {
        return preloadCallback;
    }

    public void setPreloadCallback(IFlexPageRequestCallback preloadCallback) {
        this.preloadCallback = preloadCallback;
    }

    public void reset() {
        mPageNum = 1;
        mGlobalId = null;
        flexPageData = null;
        queryMap.clear();
    }

}
