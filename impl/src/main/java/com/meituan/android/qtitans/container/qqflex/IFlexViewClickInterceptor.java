package com.meituan.android.qtitans.container.qqflex;

import android.view.View;

import com.meituan.android.dynamiclayout.controller.event.Event;
import com.meituan.android.dynamiclayout.viewmodel.BaseTag;
public interface IFlexViewClickInterceptor {
    boolean handleClick(View var1, BaseTag var2, String var3);
    boolean handleClick(View view, BaseTag baseTag, String s, Event event, String s1);
}
