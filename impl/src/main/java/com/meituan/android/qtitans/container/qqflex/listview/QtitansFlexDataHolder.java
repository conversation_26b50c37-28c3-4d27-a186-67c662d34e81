package com.meituan.android.qtitans.container.qqflex.listview;

import android.content.Context;
import android.support.annotation.NonNull;

import com.meituan.android.dynamiclayout.adapters.LayoutControllerFactoryImpl;
import com.meituan.android.dynamiclayout.adapters.LayoutLoader;
import com.meituan.android.dynamiclayout.controller.LayoutController;
import com.meituan.android.dynamiclayout.controller.LayoutFileManager;
import com.meituan.android.dynamiclayout.utils.CommonUtil;
import com.sankuai.litho.LithoViewEngine;
import com.sankuai.litho.recycler.LithoDynamicDataHolder;

public class QtitansFlexDataHolder extends LithoDynamicDataHolder<QtitansFlexData, QtitansFlexData> {

    public QtitansFlexDataHolder(QtitansFlexData data) {
        super(data, data.viewType.getType());
    }

    @NonNull
    @Override
    public LayoutController createLayoutController(Context context) {
        LayoutController controller = LayoutControllerFactoryImpl.newLayoutController(context, "qqflex");
        controller.setViewEngine(new LithoViewEngine());
        controller.setTemplateName(data.templateName);
        this.setBusinessAndActivity("qqflex", CommonUtil.getActivityName(context));
        return controller;
    }

    @NonNull
    @Override
    protected LayoutFileManager.LayoutLoader getLayoutLoader(Context context) {
        return new LayoutLoader();
    }
}
