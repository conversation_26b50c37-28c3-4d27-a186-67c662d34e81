package com.meituan.android.qtitans.container.qqflex;

import android.app.Dialog;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.ViewGroup;
import android.view.WindowManager;

import com.meituan.android.dynamiclayout.controller.LayoutController;
import com.meituan.android.dynamiclayout.controller.event.Event;
import com.meituan.android.dynamiclayout.controller.event.EventListener;
import com.meituan.android.dynamiclayout.controller.event.EventScope;
import com.meituan.android.hades.dyadater.container.interfaces.OnDialogClickListener;
import com.meituan.android.hades.impl.R;
import com.meituan.android.hades.impl.report.QQThrowableReporter;

import org.json.JSONObject;

public class QFlexDialog extends Dialog {

    private final OnDialogClickListener listener;

    private boolean isFullScreen;
    private JSONObject flexData;
    private QtitansFlexCardView flexCardView;

    private final EventListener negativeListener = new EventListener("negativeClick", EventScope.GLOBAL, null) {
        @Override
        public void handleEvent(Event event, LayoutController layoutController) {
            JSONObject data = event.getData();
            if (listener != null) {
                listener.onNegativeClick(data);
            }
        }
    };

    private final EventListener positiveListener = new EventListener("positiveClick", EventScope.GLOBAL, null) {
        @Override
        public void handleEvent(Event event, LayoutController layoutController) {
            JSONObject data = event.getData();
            if (listener != null) {
                listener.onPositiveClick(data);
            }
        }
    };

    private final EventListener disMissListener = new EventListener("dismissDialog", EventScope.GLOBAL, null) {
        @Override
        public void handleEvent(Event event, LayoutController layoutController) {
            try {
                dismiss();
            } catch (Throwable e) {
                QQThrowableReporter.report("QFlexDialog", e);
            }

        }
    };

    public QFlexDialog(@NonNull Context context, OnDialogClickListener listener) {
        super(context);
        this.listener = listener;
    }

    public QFlexDialog(@NonNull Context context, OnDialogClickListener listener,  boolean isFullScreen) {
        super(context, R.style.QContainer_Full_Screen_Dialog);
        this.listener =  listener;
        this.isFullScreen = isFullScreen;
    }

    public void setFlexData(JSONObject flexData) {
        if (flexData == null) {
            return;
        }
        this.flexData = flexData;
    }

    public void show(JSONObject flexData) {
        this.flexData = flexData;
        this.show();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        if (getWindow() != null && !isFullScreen) {
            getWindow().setBackgroundDrawableResource(R.drawable.qtitans_container_capsule_white);
            getWindow().setGravity(Gravity.CENTER);
            //淡入淡出动画
            getWindow().setWindowAnimations(R.style.dialog);
        }
        super.onCreate(savedInstanceState);
        if (flexData != null && !flexData.optBoolean("cancelable", true)) {
            setCanceledOnTouchOutside(false);
            setCancelable(false);
        }
        flexCardView = new QtitansFlexCardView(getContext());
        ViewGroup.LayoutParams lp = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        flexCardView.showView(flexData);
        flexCardView.addEventListener(negativeListener);
        flexCardView.addEventListener(positiveListener);
        flexCardView.addEventListener(disMissListener);
        setContentView(flexCardView, lp);
        if (isFullScreen && Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            if (getWindow() != null) {
                WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
                layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
            }
        }
        flexCardView.postDelayed(this::triggerReport, 100);
    }

    public void triggerReport() {
        if (flexCardView == null) return;
        if (getOwnerActivity() != null && getOwnerActivity().isFinishing()) return;
        if ( !flexCardView.isAttachedToWindow()) return;
        if (!isShowing()) return;
        flexCardView.dynamicReport();
        flexCardView.postDelayed(this::triggerReport, 100); // 每隔100ms循环检查
    }

    @Override
    public boolean dispatchTouchEvent(@NonNull MotionEvent ev) {
        try {
            return super.dispatchTouchEvent(ev);
        } catch (Exception e) {
            return false;
        }
    }
}
