package com.meituan.android.qtitans;

import static com.meituan.android.hades.router.RouterManager.DEFAULT_LCH_WIDGET;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.meituan.android.hades.BuildConfig;
import com.meituan.android.hades.HadesWidgetEnum;
import com.meituan.android.hades.dyadater.container.ContainerConst;
import com.meituan.android.hades.dyadater.container.ContainerServiceFactory;
import com.meituan.android.hades.dyadater.container.ContainerType;
import com.meituan.android.hades.impl.net.HadesRetrofit;
import com.meituan.android.hades.impl.report.BabelHelper;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.report.ReportParamsKey;
import com.meituan.android.hades.impl.utils.HadesBizReportUtils;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.impl.utils.LxReportUtils;
import com.meituan.android.hades.impl.utils.StorageHelper;
import com.meituan.android.qtitans.container.bean.LoadingViewParams;
import com.meituan.android.qtitans.container.bean.QtitansContainerParams;
import com.meituan.android.hades.dyadater.container.QtitansContainerVisitType;
import com.meituan.android.qtitans.container.common.QtitansLog;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class ContainerReporterLocal {
    private static final String CONTAINER_JUMP = "containerJump";
    public static final String CID_CONTAINER_INNER_PV = "c_lintopt_lu8ykump";
    public static final String BID_CONTAINER_OUT_PUSH_MC = "b_lintopt_zo4ur58n_mc";
    public static final String CID_CONTAINER_OUT_PV = "c_lintopt_epfskihb";
    public static final String BID_CONTAINER_OUT_WIDGET_MC = "b_lintopt_3gkdhgf6_mc";
    private static final String MT_QQ_CONTAINER = "mt-qq-container";// 容器标记
    private static final String CONTAINER_STOP = "containerStop";

    public static void logOpenIntent(String tag, Intent intent, String paramsStr, String errorMsg) {
        if (TextUtils.isEmpty(paramsStr)) return;
        try {
            logOpenIntent(tag, intent, new JSONObject(paramsStr), errorMsg);
        } catch (Throwable e) {

        }
    }

    public static void logOpenIntent(String tag, Intent intent, JSONObject params, String errorMsg) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                if (params == null) return;
                BabelHelper.log(ReportParamsKey.CONTAINER.QQ_INTENT_CREATE, new HashMap<String, Object>() {
                    {

                        put("tag", tag);
                        put("checkSource", getCheckSourceFromContainerParams(params));
                        put("visitType", getVisitTypeFromContainerParams(params));
                        put("targetUrl", getUrlFromContainerParams(params));
                        put("intentUrl", getUrlFromIntent(intent));
                        put("errorMsg", errorMsg);
                    }
                });
            } catch (Throwable throwable) {
                QQThrowableReporter.report("ContainerReporterLocal", throwable);
            }
        });
    }

    private static String getUrlFromContainerParams(JSONObject params) {
        if (params == null) {
            return "";
        }
        return params.optString( "targetUrl");
    }

    private static String getVisitTypeFromContainerParams(JSONObject params) {
        if (params == null) {
            return "";
        }
        JSONObject loadingViewParams = params.optJSONObject("loadingViewParams");
        if (loadingViewParams == null) {
            return "";
        }
        return loadingViewParams.optString("visitType");
    }

    private static String getCheckSourceFromContainerParams(JSONObject params) {
        if (params == null) {
            return "-1";
        }
        JSONObject loadingViewParams = params.optJSONObject("loadingViewParams");
        if (loadingViewParams == null) {
            return "-1";
        }
        return loadingViewParams.optString("checkSource");
    }

    public static void logCreateIntent(String tag, Intent intent, String targetUrl,
                                       String visitType, int checkSource) {
        logCreateIntent(tag, intent, targetUrl, checkSource, visitType, "");
    }
    public static void logCreateIntent(String tag, Intent intent, String targetUrl,
                                       int checkSource, String visitType, String errorMsg) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                BabelHelper.log(ReportParamsKey.CONTAINER.QQ_INTENT_CREATE, new HashMap<String, Object>() {
                    {
                        put("tag", tag);
                        put("checkSource", (checkSource < 0 ? getCheckSourceFromIntent(intent) : checkSource));
                        put("visitType", TextUtils.isEmpty(visitType) ? getVisitTypeFromIntent(intent) : visitType);
                        put("targetUrl", targetUrl);
                        put("intentUrl", getUrlFromIntent(intent));
                        put("errorMsg", errorMsg);
                    }
                });
            } catch (Throwable throwable) {
                QQThrowableReporter.report("ContainerReporterLocal", throwable);
            }
        });
    }

    public static void reportContainerOnStop(String page) {
        long time = System.currentTimeMillis();
        HadesUtils.runOnWorkThread(() -> {
            try {
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, CONTAINER_STOP);
                valLabMap.put(ReportParamsKey.CONTAINER.TIME, time);
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PAGE_NAME, page);
                LxReportUtils.pageEvent(CID_CONTAINER_INNER_PV, valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporterLocal", e);
            }
        });
    }
    private static String getCheckSourceFromIntent(Intent intent) {
        if (intent == null) {
            return "-1";
        }
        String paramsStr = intent.getStringExtra(ContainerConst.Qtitans_CONTAINER_PARAMS);
        QtitansContainerParams qtitansContainerParams = HadesUtils.fromJson(paramsStr, QtitansContainerParams.class);
        if (qtitansContainerParams == null) {
            return "-1";
        }
        LoadingViewParams loadingViewParams = qtitansContainerParams.getLoadingViewParams();
        if (loadingViewParams == null) {
            return "-1";
        }
        return loadingViewParams.getCheckSource();
    }

    private static String getUrlFromIntent(Intent intent) {
        if (intent == null) {
            return "";
        }
        return intent.getDataString();
    }

    private static String getVisitTypeFromIntent(Intent intent) {
        if (intent == null) {
            return "";
        }
        String paramsStr = intent.getStringExtra(ContainerConst.Qtitans_CONTAINER_PARAMS);
        QtitansContainerParams qtitansContainerParams = HadesUtils.fromJson(paramsStr, QtitansContainerParams.class);
        if (qtitansContainerParams == null) {
            return "";
        }
        LoadingViewParams loadingViewParams = qtitansContainerParams.getLoadingViewParams();
        if (loadingViewParams == null) {
            return "";
        }
        return loadingViewParams.getVisitType();
    }

    public static void reportContainerJump(String paramsStr, long time, long uptime) {
        if (TextUtils.isEmpty(paramsStr)) {
            return;
        }
        HadesUtils.runOnWorkThread(() -> {
            try {
                QtitansContainerParams params = HadesUtils.fromJson(paramsStr, QtitansContainerParams.class);
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, CONTAINER_JUMP);
                valLabMap.put(ReportParamsKey.CONTAINER.TIME, time);
                valLabMap.put(ReportParamsKey.CONTAINER.UPTIME, uptime);
                valLabMap.put("container_provider", ContainerServiceFactory.getInstance().getProvider() instanceof LocalContainerProviderImpl? "local" : "dex");
                addQtitansContainerParams(valLabMap, params);
                valLabMap.put(ReportParamsKey.CONTAINER.QC_U_ALIAS, StorageHelper.getQcUpdateAlias(HadesUtils.getContext()));
                boolean isContainerPush = params.getLoadingViewParams() != null
                        && !TextUtils.isEmpty(params.getLoadingViewParams().getPushTypeContainer());
                if (isContainerPush) {
                    valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PUSH_TYPE, params.getLoadingViewParams().getPushTypeContainer());
                }
                LxReportUtils.clickEvent(CID_CONTAINER_OUT_PV,
                        isContainerPush ? BID_CONTAINER_OUT_PUSH_MC : BID_CONTAINER_OUT_WIDGET_MC, valLabMap);
                reportLog(valLabMap);
                HadesBizReportUtils.reportDeskAppSubscribe(HadesBizReportUtils.EVENT_TYPE_DESK_APP_CLICK, valLabMap);

                // 小程序、省钱卡、短视频22点击
                HadesUtils.runOnWorkThread(() -> {
                    try {
                        String visitType = getContainerVisitType(params);
                        int widgetNumCode = params.getSceneCode();
                        HadesWidgetEnum widgetEnum = HadesWidgetEnum.getInstanceByWidgetNumCode(widgetNumCode);
                        String checkSource = getCheckSource(params);
                        String scene = StorageHelper.getCardBindScene(HadesUtils.getContext(), widgetEnum);
                        Context context = HadesUtils.getContext();
                        if (TextUtils.equals(visitType, QtitansContainerVisitType.VisitWidget.getType())
                                || TextUtils.equals(visitType, QtitansContainerVisitType.VisitVideoWidget22.getType())
                        ) {
                            HadesRetrofit.getInstance(context).reportWidgetClick(widgetEnum, widgetEnum.getCardType(),
                                    checkSource, scene, "1").execute();
                        } else if (TextUtils.equals(visitType, QtitansContainerVisitType.VisitPush.getType())) {
                            HadesRetrofit.getInstance(context).reportWidgetClick(widgetEnum, widgetEnum.getCardType(),
                                    checkSource, scene, "0").execute();
                        } else {
                            HadesRetrofit.getInstance(context).reportWidgetClick(widgetEnum, widgetEnum.getCardType(),
                                    checkSource, scene, "-1").execute();
                        }
                    } catch (Throwable e) {
                        QQThrowableReporter.report("ContainerReporterLocal", e);
                    }
                });
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporterLocal", e);
            }
        });
    }

    private static void addQtitansContainerParams(Map<String, Object> valLabMap, QtitansContainerParams params) {
        try {
            if (valLabMap == null || params == null) {
                return;
            }
            try {
                int widgetNumCode = params.getSceneCode();
                HadesWidgetEnum widgetEnum = HadesWidgetEnum.getInstanceByWidgetNumCode(widgetNumCode);
                String scene = StorageHelper.getCardBindScene(HadesUtils.getContext(), widgetEnum);
                valLabMap.put(ReportParamsKey.WIDGET.PIN_SCENE, scene);
                valLabMap.put(ReportParamsKey.WIDGET.CARD_MARK, StorageHelper.getCardBindCardMark(HadesUtils.getContext(), widgetEnum));
            } catch (Exception ignored) {
            }
            valLabMap.put(ReportParamsKey.CONTAINER.JUMP_SCENE, params.getJumpScene());
            valLabMap.put(ReportParamsKey.CONTAINER.SCENE_CODE, params.getSceneCode());
            String lch = HadesUtils.getLchFromTargetUrl(params.getTargetUrl());
            valLabMap.put(ReportParamsKey.CONTAINER.LCH, TextUtils.isEmpty(lch) ? DEFAULT_LCH_WIDGET : lch);
            valLabMap.put(ReportParamsKey.CONTAINER.TARGET_URL, params.getTargetUrl());
            valLabMap.put(ReportParamsKey.CONTAINER.RESOURCE_ID, params.getResourceId());
            valLabMap.put(ReportParamsKey.CONTAINER.REOPEN, params.isReopen());
            valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_BUSINESS, getBusinessType(params));
            valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_SOURCE, getCheckSource(params));
            valLabMap.put(ReportParamsKey.CONTAINER.CARD_STATUS, getCardStatus(params));
            valLabMap.put(ReportParamsKey.CONTAINER.BRAN_ID, getBrandId(params));
            valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_TYPE, getContainerType(params));
            valLabMap.put(ReportParamsKey.CONTAINER.MGC, params.isMgc());
            valLabMap.put(ReportParamsKey.CONTAINER.GAME_SOURCE, isGameSource(params));
            valLabMap.put(ReportParamsKey.CONTAINER.PUSH_TYPE, getPushTypeContainer(params));
            valLabMap.put(ReportParamsKey.CONTAINER.CLEAR_TOP, params.isClearTop());
            valLabMap.put(ReportParamsKey.CONTAINER.GOTO_HOME, params.isGoToMTHome());
            valLabMap.put(ReportParamsKey.CONTAINER.VISIT_TYPE, getContainerVisitType(params));
            valLabMap.put(ReportParamsKey.CONTAINER.FUNCTION_TYPE, getContainerFunctionType(params));
            valLabMap.put(ReportParamsKey.CONTAINER.SCREEN_ON, HadesUtils.isScreenOn(HadesUtils.getContext()));
            valLabMap.put(ReportParamsKey.CONTAINER.QC_EXTR, HadesUtils.toJson(params));
            valLabMap.put("useCapsule", params.useCapsule());
        } catch (Throwable e) {
            QQThrowableReporter.report("ContainerReporterLocal", e);
        }
    }

    public static String getPushTypeContainer(QtitansContainerParams qtitansContainerParams) {
        if (qtitansContainerParams != null
                && qtitansContainerParams.getLoadingViewParams() != null
                && !TextUtils.isEmpty(qtitansContainerParams.getLoadingViewParams().getPushTypeContainer())) {
            return qtitansContainerParams.getLoadingViewParams().getPushTypeContainer();
        }
        return "";
    }
    private static String getContainerVisitType(QtitansContainerParams qtitansContainerParams) {
        if (qtitansContainerParams != null
                && qtitansContainerParams.getLoadingViewParams() != null
                && !TextUtils.isEmpty(qtitansContainerParams.getLoadingViewParams().getVisitType())) {
            return qtitansContainerParams.getLoadingViewParams().getVisitType();
        }
        return "";
    }

    private static boolean isGameSource(QtitansContainerParams qtitansContainerParams) {
        if (qtitansContainerParams != null
                && qtitansContainerParams.getLoadingViewParams() != null) {
            return qtitansContainerParams.getLoadingViewParams().isGameSource();
        }
        return false;
    }

    public static String getContainerType(QtitansContainerParams qtitansContainerParams) {
        return qtitansContainerParams != null
                && qtitansContainerParams.getContainerType() != null
                ? qtitansContainerParams.getContainerType().name() : ContainerType.UNKNOWN.name();
    }

    public static String getCheckSource(QtitansContainerParams qtitansContainerParams) {
        return qtitansContainerParams != null
                && qtitansContainerParams.getLoadingViewParams() != null
                ? qtitansContainerParams.getLoadingViewParams().getCheckSource() : "";
    }

    public static String getBusinessType(QtitansContainerParams qtitansContainerParams) {
        return qtitansContainerParams != null
                && qtitansContainerParams.getLoadingViewParams() != null
                ? qtitansContainerParams.getLoadingViewParams().getBusinessType() : "";
    }

    public static String getCardStatus(QtitansContainerParams qtitansContainerParams) {
        return qtitansContainerParams != null
                && qtitansContainerParams.getCardStatus() != null
                ? qtitansContainerParams.getCardStatus() : "-1";
    }

    public static String getBrandId(QtitansContainerParams qtitansContainerParams) {
        return qtitansContainerParams != null
                && qtitansContainerParams.getBrandId() != null
                ? qtitansContainerParams.getBrandId() : "";
    }

    private static String getContainerFunctionType(QtitansContainerParams qtitansContainerParams) {
        if (qtitansContainerParams != null
                && qtitansContainerParams.getLoadingViewParams() != null
                && !TextUtils.isEmpty(qtitansContainerParams.getLoadingViewParams().getFunctionType())) {
            return qtitansContainerParams.getLoadingViewParams().getFunctionType();
        }
        return "";
    }

    public static void reportLog(Map<String, Object> custom) {
        reportLog("", custom);
    }

    public static void reportLog(String stage, Map<String, Object> custom) {
        if (custom == null) {
            return;
        }
        //重复
        if (BuildConfig.LOG_ENABLE) {
            if (custom.containsKey("channel")
                    || custom.containsKey("source")
                    || custom.containsKey("modelName")
                    || custom.containsKey("eventType")
                    || custom.containsKey(MT_QQ_CONTAINER)) {
                QtitansLog.log("reportLog: 上报日志 关键字重复 ");
            }
        }
        HadesUtils.runOnWorkThread(() -> {
            try {
                if (!TextUtils.isEmpty(stage)) {
                    custom.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, stage);
                }
                custom.put("reportLog", "containerreporter");
                if (BuildConfig.LOG_ENABLE) {
                    QtitansLog.log(custom.toString());
                }
                BabelHelper.log(MT_QQ_CONTAINER, custom);
                Logger.d(MT_QQ_CONTAINER, custom);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporterLocal", e);
            }
        });
    }
}
