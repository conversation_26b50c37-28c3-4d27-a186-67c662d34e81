package com.meituan.android.qtitans.container.qqflex.listview;

import android.os.Handler;
import android.support.v7.widget.RecyclerView;
import android.view.ViewGroup;

import com.meituan.android.dynamiclayout.controller.LayoutController;
import com.meituan.android.dynamiclayout.controller.event.Event;
import com.meituan.android.dynamiclayout.controller.event.EventListener;
import com.meituan.android.dynamiclayout.controller.event.EventScope;
import com.meituan.android.hades.dyadater.container.FlexEventListener;
import com.meituan.android.hades.impl.report.QQThrowableReporter;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class QtitansFlexListAdapter extends RecyclerView.Adapter<QtitansBaseViewHolder.BaseViewHolder<QtitansBaseData>> {

    private RecyclerView recyclerView;

    private List<QtitansBaseData> dataList = new ArrayList<>();
    private List<EventListener> listeners = new ArrayList<>();
    private boolean canShowLoading = true;

    public boolean isEnableLoadMore() {
        return enableLoadMore;
    }

    public void setEnableLoadMore(boolean enableLoadMore) {
        this.enableLoadMore = enableLoadMore;
    }

    private boolean enableLoadMore = true;

    public FlexListLoadMoreListener getLoadMoreListener() {
        return loadMoreListener;
    }

    public void setLoadMoreListener(FlexListLoadMoreListener loadMoreListener) {
        this.loadMoreListener = loadMoreListener;
    }

    private FlexListLoadMoreListener loadMoreListener;

    public FlexListViewItemClickInterceptor getItemClickInterceptor() {
        return itemClickInterceptor;
    }

    public void setItemClickInterceptor(FlexListViewItemClickInterceptor itemClickInterceptor) {
        this.itemClickInterceptor = itemClickInterceptor;
    }

    private QtitansFlexLoadMoreListener dynamicLoadMoreListener;

    private FlexListViewItemClickInterceptor itemClickInterceptor;

    public QtitansFlexListAdapter(RecyclerView recyclerView) {
        this.recyclerView = recyclerView;
        dynamicLoadMoreListener = new QtitansFlexLoadMoreListener() {
            @Override
            public void tryLoadMore() {
                showLoading();
            }
        };
        recyclerView.addOnScrollListener(dynamicLoadMoreListener);
    }

    public void setData(List<QtitansFlexData> data) {
        ((QtitansFlexReporter) QtitansFlexReporter.getInstance()).reset();
        if (data == null || data.isEmpty())
            return;
        dataList.clear();
        dataList.addAll(data);
        notifyDataSetChanged();
        if (dynamicLoadMoreListener != null) {
            //数据刷新主动触发埋点上报
            dynamicLoadMoreListener.dynamicReport(recyclerView);
        }
    }

    public List<QtitansBaseData> getData() {
        return dataList;
    }

    public void updateItem(int position) {
        this.notifyItemChanged(position);
    }

    @Override
    public QtitansBaseViewHolder.BaseViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        QtitansBaseViewHolder viewHolder = QtitansViewHolderFactory.getInstance().createViewHolderByType(viewType);
        if (viewHolder instanceof QtitansFlexViewHolder) {
            if (itemClickInterceptor != null) {
                ((QtitansFlexViewHolder) viewHolder).setClickInterceptor(itemClickInterceptor);
            }
            if (listeners != null && !listeners.isEmpty()) {
                ((QtitansFlexViewHolder) viewHolder).addEventListeners(listeners);
            }


        }
        return (QtitansBaseViewHolder.BaseViewHolder) viewHolder.createViewHolder(parent);
    }

    @Override
    public void onBindViewHolder(QtitansBaseViewHolder.BaseViewHolder holder, int position) {
        if (dataList.isEmpty()) return;
        QtitansBaseData data = dataList.get(position);
        holder.bindView(position, data);
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    @Override
    public int getItemViewType(int position) {
        QtitansBaseData data = dataList.get(position);
        if (data != null) {
            return data.viewType.getType();
        } else return -1;
    }

    @Override
    public long getItemId(int position) {
        return (long) position;
    }

    public void showLoading() {
        if (canShowLoading && enableLoadMore) {
            canShowLoading = false;
            new Handler().post(() -> {
                QtitansBaseData data = new QtitansBaseData();
                data.viewType = ItemViewType.DYNAMIC_LOAD_MORE;
                dataList.add(data);
                notifyItemInserted(dataList.size() - 1);
                if (loadMoreListener != null) {
                    loadMoreListener.loadMore();
                }
            });

        }
    }

    public void hideLoading() {
        canShowLoading = true;
        if (!dataList.isEmpty()) {
            if (dataList.get(dataList.size() - 1).viewType == ItemViewType.DYNAMIC_LOAD_MORE) {
                dataList.remove(dataList.size() - 1);
                notifyItemRemoved(dataList.size());

            }

        }

    }

    public void addEventListener(EventListener eventListener) {
        if (eventListener != null && !listeners.contains(eventListener)) {
            listeners.add(eventListener);
        }

    }

    public void addEventListener(FlexEventListener eventListener) {
        if (eventListener != null) {
            EventScope scope = EventScope.get(eventListener.getScope());
            EventListener listener = new EventListener(eventListener.getAction(), scope, null) {
                @Override
                public void handleEvent(Event event, LayoutController layoutController) {
                    JSONObject data = event.getData();
                    eventListener.handleEventData(data);
                }
            };
            listeners.add(listener);
        }

    }

    public void addData(List<QtitansFlexData> list) {
        int start = dataList.size();
        dataList.addAll(list);
        notifyItemRangeInserted(start, list.size());
    }

    @Override
    public void onViewAttachedToWindow(QtitansBaseViewHolder.BaseViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        if (holder != null) {
            holder.onViewAttachedToWindow(holder);
        }
    }

    @Override
    public void onViewDetachedFromWindow(QtitansBaseViewHolder.BaseViewHolder holder) {
        super.onViewDetachedFromWindow(holder);
        if (holder != null) {
            holder.onViewDetachedFromWindow(holder);
        }
    }

    @Override
    public void onViewRecycled(QtitansBaseViewHolder.BaseViewHolder holder) {
        super.onViewRecycled(holder);
        if (holder != null) {
            holder.onViewRecycled(holder);
        }
    }
}
