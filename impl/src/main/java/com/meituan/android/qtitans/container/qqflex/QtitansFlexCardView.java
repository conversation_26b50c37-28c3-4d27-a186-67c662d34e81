package com.meituan.android.qtitans.container.qqflex;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.Size;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;

import com.meituan.android.dynamiclayout.adapters.LayoutControllerFactoryImpl;
import com.meituan.android.dynamiclayout.adapters.LayoutLoader;
import com.meituan.android.dynamiclayout.adapters.ReporterImpl;
import com.meituan.android.dynamiclayout.controller.LayoutController;
import com.meituan.android.dynamiclayout.controller.event.Event;
import com.meituan.android.dynamiclayout.controller.event.EventListener;
import com.meituan.android.dynamiclayout.controller.presenter.DynamicLayoutPresenter;
import com.meituan.android.dynamiclayout.controller.presenter.TemplateData;
import com.meituan.android.dynamiclayout.extend.interceptor.OnViewClickIntercepter;
import com.meituan.android.dynamiclayout.utils.CommonUtil;
import com.meituan.android.dynamiclayout.viewmodel.BaseTag;
import com.meituan.android.hades.impl.BuildConfig;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.impl.utils.UiUtils;
import com.meituan.android.qtitans.container.qqflex.event.IFlexViewClickEventListener;
import com.meituan.android.qtitans.container.qqflex.event.QtitansFlexCardViewClickEvent;
import com.meituan.android.qtitans.container.qqflex.listview.QtitansFlexReporter;

import org.json.JSONObject;

import java.util.Collections;
import java.util.Map;

public class QtitansFlexCardView extends FrameLayout {

    private static final String TAG = "QtitansDynamicCardView";

    private static final String DEFAULT_BUSINESS = "qqflex";

    private LayoutController layoutController;
    private LayoutLoader layoutLoader;

    private  DynamicLayoutPresenter layoutPresenter;

    public IFlexViewClickEventListener getClickEventListener() {
        return clickEventListener;
    }

    public void setClickEventListener(IFlexViewClickEventListener clickEventListener) {
        this.clickEventListener = clickEventListener;
    }

    private IFlexViewClickEventListener clickEventListener;

    public IFlexViewClickInterceptor getViewClickInterceptor() {
        return viewClickInterceptor;
    }

    public void setViewClickInterceptor(IFlexViewClickInterceptor viewClickInterceptor) {
        this.viewClickInterceptor = viewClickInterceptor;
    }

    private IFlexViewClickInterceptor viewClickInterceptor;

    public IFlexCardShowStatusListener getShowStatusListener() {
        return showStatusListener;
    }

    public void setShowStatusListener(IFlexCardShowStatusListener showStatusListener) {
        this.showStatusListener = showStatusListener;
    }

    private IFlexCardShowStatusListener showStatusListener;

    public QtitansFlexCardView(@NonNull Context context) {
        super(context);
        init(context);

    }

    public QtitansFlexCardView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public QtitansFlexCardView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        layoutLoader = new LayoutLoader();
        layoutPresenter = createLayoutPresenter();
        layoutController = LayoutControllerFactoryImpl.newLayoutController(context);
        layoutPresenter.setContainer(this);
        layoutPresenter.business = DEFAULT_BUSINESS;
        layoutPresenter.activityName = CommonUtil.getActivityName(getContext());
        layoutController.addViewClickInterceptor(new OnViewClickIntercepter() {
            @Override
            public boolean handleClick(View view, BaseTag baseTag, String s, Event event, String s1) {
                if (viewClickInterceptor != null) {
                    return viewClickInterceptor.handleClick(view, baseTag, s, event, s1);
                }
                return false;
            }

            @Override
            public boolean handleClick(View view, BaseTag baseTag, String s) {
                if (viewClickInterceptor != null) {
                    return viewClickInterceptor.handleClick(view, baseTag, s);
                }
                return false;
            }
        });
        layoutController.addEventListener(new QtitansFlexCardViewClickEvent() {

            @Override
            protected void handleClick(String action, Map<String, Object> data) {
                if (clickEventListener != null) {
                    clickEventListener.handleClickEvent(action, data);
                }
            }
        });
        layoutController.setReporter(QtitansFlexReporter.getInstance());
    }

    public void updateActivityAndBusinessName(String activityName, String businessName) {
        if (layoutController != null) {
            layoutPresenter.business = businessName;
            layoutPresenter.activityName = activityName;
        }
    }

    public void showView(JSONObject data) {
        try {
            TemplateData templateData = new TemplateData();
            templateData.jsonData = data;
            String templateName = data.getString("templateName");
            String templateUrl = data.getString("templateUrl");
            templateData.templateName = templateName;
            templateData.templates = Collections.singletonList(templateUrl);
            layoutController.setTemplateName(templateName);
            layoutPresenter.show(templateData, true);

        } catch (Throwable e) {
            if (BuildConfig.LOG_ENABLE) {
                e.printStackTrace();
            }
        }

    }

    private DynamicLayoutPresenter createLayoutPresenter() {
         return new DynamicLayoutPresenter(this.getContext(), DEFAULT_BUSINESS, CommonUtil.getActivityName(this.getContext()), () -> layoutController, layoutLoader, new DynamicLayoutPresenter.OnShowingListener() {
            @Override
            public void onShow(TemplateData data, boolean viewChanged) {
                if (BuildConfig.LOG_ENABLE) {
                    Logger.d(TAG, "data:" + data.jsonData.toString() + "viewChanged:" + viewChanged);
                }
                if (showStatusListener != null) {
                    showStatusListener.onShow(data, viewChanged);
                }
            }

            @Override
            public void onFailed(TemplateData data) {
                if (BuildConfig.LOG_ENABLE) {
                    Logger.d(TAG, "data:" + data.jsonData.toString());
                }
                if (showStatusListener != null) {
                    showStatusListener.onFailed(data);
                }
            }
        });
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.post(this::dynamicReport);
    }

    public void addEventListener(EventListener eventListener) {
        if (layoutController != null) {
            layoutController.addEventListener(eventListener);
        }
    }

    public void sendEvent(Event event) {
        if (layoutController != null) {
            layoutController.sendEvent(event);
        }
    }

    public void dynamicReport() {
        int[] visibleScreenBound = getDynamicHeaderVisibleBound();
        if (visibleScreenBound.length < 4) {
            return;
        }
        setExposeStrategy((LayoutController) layoutController, visibleScreenBound);
        layoutController.setExposureTraceStrategy(0, 0);
        layoutController.notifyExposureChanged(this);

    }

    private void setExposeStrategy(LayoutController layoutController,
                                   @Size(4) int[] visibleScreenBound) {
        if (layoutController == null || visibleScreenBound.length != 4) {
            return;
        }

        layoutController.setExposureVisibleScreenBound(visibleScreenBound[0], visibleScreenBound[1],
                visibleScreenBound[2], visibleScreenBound[3]);

    }

    private int[] getDynamicHeaderVisibleBound() {
        int[] visibleBound = new int[4];
        int[] location = new int[2];
        getLocationOnScreen(location);
        visibleBound[0] = getLeft();
        visibleBound[1] = getRight();
        visibleBound[2] = location[1];
        visibleBound[3] = UiUtils.windowHeight(HadesUtils.getContext());
        return visibleBound;
    }
}
