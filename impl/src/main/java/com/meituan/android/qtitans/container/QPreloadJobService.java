package com.meituan.android.qtitans.container;

import android.app.job.JobInfo;
import android.app.job.JobParameters;
import android.app.job.JobScheduler;
import android.app.job.JobService;
import android.content.ComponentName;
import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.PersistableBundle;
import android.text.TextUtils;
import android.util.Log;

import com.meituan.android.hades.impl.config.HadesConfigMgr;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.HadesWidgetUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.impl.utils.StorageHelper;
import com.meituan.android.hades.router.metrics.HadesOutLinkMetrics;
import com.meituan.android.qtitans.container.preload.QPreloadManager;

import java.lang.ref.WeakReference;

/**
 * BroadcastReceiver 生命周期很短，onReceive 中不适合做耗时操作
 * webview loadurl 需要配合 JobService 使用
 */
public class QPreloadJobService extends JobService {

    private static final String TAG = "QPreload";

    private static final String EXTRA_TARGET = "preload_target";
    private static final String EXTRA_FINISH = "preload_finish";

    private static final int DEFAULT_JOB_FINISHED_MAX_DURATION = 20000; //默认预热20s
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private final AutoFinishJob defaultJobFinishRunnable = new AutoFinishJob(this, false);

    private boolean isPreload = false;

    public static void warmupMainProcess(Context context, String targetUrl) {
        if (HadesWidgetUtils.useMgcRouter(context, targetUrl)) {
            return;
        }
        if (!HadesConfigMgr.getInstance(context).getPreloadSwitch()) {
            return;
        }
        if (!HadesUtils.wmS(context)) {
            return;
        }
        PersistableBundle bundle = new PersistableBundle();
        bundle.putString(EXTRA_TARGET, targetUrl);
        scheduleJob(context, bundle);
    }

    public static void finishPreRender(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
            if (!HadesUtils.cplS(HadesUtils.getContext())) {
                return;
            }
            PersistableBundle bundle = new PersistableBundle();
            bundle.putBoolean(EXTRA_FINISH, true);
            scheduleJob(context, bundle);
        }
    }

    private static void scheduleJob(Context context, PersistableBundle bundle) {
        ComponentName componentName = new ComponentName(context, QPreloadJobService.class);
        JobInfo jobInfo = new JobInfo.Builder(1, componentName)
                .setRequiredNetworkType(JobInfo.NETWORK_TYPE_ANY)
                .setExtras(bundle)
                .build();

        JobScheduler jobScheduler = (JobScheduler) context.getSystemService(Context.JOB_SCHEDULER_SERVICE);
        try {
            jobScheduler.schedule(jobInfo);
        } catch (Throwable throwable) {
            QQThrowableReporter.report("QPreloadJobService", throwable);
        }
    }

    @Override
    public boolean onStartJob(JobParameters params) {
        try {
            //Returning false from this method means your job is already finished.
            //The system's wakelock for the job will be released, and onStopJob(JobParameters) will not be invoked.
            return onStartJobInternal(params);
        } catch (Throwable e) {
            Log.e(TAG, "onStartJob error", e);
            QQThrowableReporter.report("QPreloadJobService", e);
            return false;
        }
    }

    private boolean onStartJobInternal(JobParameters params) {
        Logger.d(TAG + " onStartJob");
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP_MR1) {
            boolean finish = params.getExtras().getBoolean(EXTRA_FINISH);
            if (finish) {
                Logger.d(TAG, "finish pre render due to push closed");
                return false;
            }
        }

        if (isPreload) {
            Logger.d(TAG ,"preload service already started");
            return false;
        }
        isPreload = true;
        defaultJobFinishRunnable.setParams(params);
        long start = System.currentTimeMillis();
        String target = params.getExtras().getString(EXTRA_TARGET, null);
        if (!TextUtils.isEmpty(target)) {
            // 如果某些预热任务没有提供 callback，那么预热任务最多持续 20s
            mainHandler.postDelayed(defaultJobFinishRunnable, DEFAULT_JOB_FINISHED_MAX_DURATION);

            // 提前执行对应容器的次级任务
            QPreloadManager.getInstance().executeSecondaryTask(this, target);

            HadesUtils.runOnWorkThread(() -> {
                StorageHelper.setQtitansMainProcessPreloadFlag(HadesUtils.getLchFromTargetUrl(target), true);
            });
            QPreloadManager.getInstance().preload(this, target, new QPreloadManager.IPreloadCallback() {
                @Override
                public void onPreloadSuccess(String targetUrl) {
                    HadesOutLinkMetrics.getInstance().recordPreloadServiceStage(start, System.currentTimeMillis(), true);

                    mainHandler.removeCallbacks(defaultJobFinishRunnable);
                    StorageHelper.setQtitansMimiContainerPreload(HadesUtils.getLchFromTargetUrl(targetUrl), true);
                    jobFinished(params, false);
                }

                @Override
                public void onPreloadFailed() {
                    Logger.e(TAG + " preload failed");
                    HadesOutLinkMetrics.getInstance().recordPreloadServiceStage(start, System.currentTimeMillis(), false);
                    mainHandler.removeCallbacks(defaultJobFinishRunnable);
                    jobFinished(params, false);
                }
            });
            return true; //true 表示该任务需要继续在后台运行, 需要手动调用 jobFinish
        }
        return false;
    }

    @Override
    public boolean onStopJob(JobParameters params) {
        /**
         * Returns:
         * true to indicate to the JobManager whether you'd like to reschedule this job based on the retry criteria provided at job creation-time;
         * or false to end the job entirely. Regardless of the value returned, your job must stop executing.
         */
        Logger.d(TAG + " onStopJob");
        return false;
    }

    private static class AutoFinishJob implements Runnable {

        private final WeakReference<JobService> serviceRef;
        private JobParameters params;
        private final boolean needReschedule;

        public AutoFinishJob(JobService jobService, boolean needReschedule) {
            this.serviceRef = new WeakReference<>(jobService);
            this.needReschedule = needReschedule;
        }

        public void setParams(JobParameters params) {
            this.params = params;
        }

        @Override
        public void run() {
            try {
                JobService service = serviceRef.get();
                if (service == null) {
                    return;
                }
                if (params == null) {
                    return;
                }
                service.jobFinished(params, needReschedule);
            } catch (Throwable throwable) {
                QQThrowableReporter.report("QPreloadJobService", throwable);
            }
        }
    }
}

