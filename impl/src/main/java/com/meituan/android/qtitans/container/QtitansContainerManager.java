package com.meituan.android.qtitans.container;

import static com.meituan.android.hades.dyadater.container.ContainerUtilsAdapter.getHadesWidgetEnum;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;
import android.widget.Toast;

import com.meituan.android.hades.BuildConfig;
import com.meituan.android.hades.ContainerCallback;
import com.meituan.android.hades.HadesWidgetEnum;
import com.meituan.android.hades.dyadater.container.ContainerConst;
import com.meituan.android.hades.dyadater.container.ContainerType;
import com.meituan.android.hades.dyadater.container.QtitansContainerVisitType;
import com.meituan.android.hades.dyadater.container.QtitansLoadingPageContent;
import com.meituan.android.hades.dyadater.container.interfaces.IContainerService;
import com.meituan.android.hades.dyadater.desk.DeskResourceData;
import com.meituan.android.hades.impl.config.HadesConfigMgr;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.report.ReportParamsKey;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.HadesWidgetUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.impl.utils.StorageHelper;
import com.meituan.android.hades.impl.widget.util.WidgetRecorder;
import com.meituan.android.hades.router.RouterManager;
import com.meituan.android.pin.Pin;
import com.meituan.android.pin.PinCallback;
import com.meituan.android.qtitans.QTitansLuckinContainerActivity;
import com.meituan.android.qtitans.QTitansLuckinSplashActivity;
import com.meituan.android.qtitans.container.bean.LoadingViewParams;
import com.meituan.android.qtitans.container.bean.QtitansBridgeParams;
import com.meituan.android.qtitans.container.bean.QtitansContainerConfig;
import com.meituan.android.qtitans.container.bean.QtitansContainerParams;
import com.meituan.android.qtitans.container.common.QtitansContainerFunctionType;
import com.meituan.android.qtitans.container.common.QtitansLog;
import com.meituan.android.qtitans.container.common.QtitansUtils;
import com.meituan.android.qtitans.container.config.ContainerConfigManager;
import com.meituan.android.qtitans.container.config.PageConfig;
import com.meituan.android.qtitans.container.reporter.ContainerReporter;
import com.sankuai.meituan.mbc.dsp.core.CurrentActivityUtil;
import com.sankuai.meituan.mbc.dsp.core.Dsp;
import com.squareup.picasso.Picasso;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class QtitansContainerManager implements IContainerService {
    public static final String TAG = "QtitansContainer";
    public final static String Qtitans_LOADING_VIEW_PARAMS = "LoadingViewParams";
    public final static String Qtitans_URL = "qtitans_url";

    public final static String Qtitans_SCHEME_BASE = "imeituan://www.meituan.com/qtitans/container";
    private final static String Qtitans_Container_Class = "com.meituan.android.qtitans.QtitansContainerActivity";
    private static final int DEFAULT_PUSH_TIME_OUT = 100;

    private static volatile QtitansContainerManager sINSTANCE;


    private QtitansContainerManager() {
    }

    public static QtitansContainerManager getInstance() {
        if (sINSTANCE == null) {
            synchronized (QtitansContainerManager.class) {
                if (sINSTANCE == null) {
                    sINSTANCE = new QtitansContainerManager();
                }
            }
        }
        return sINSTANCE;
    }

    /**
     * canUseHadesContainer对参数进行校验过了
     *
     * @param activity
     * @param params
     */
    @Override
    public void openQtitansContainer(Activity activity, String params) {
        openQtitansContainer(activity, params, false);
    }

    public void openQtitansContainer(Activity activity, String params, boolean isReOpen) {
        openQtitansContainer(activity, params, isReOpen, false);
    }

    public void openQtitansContainer(Activity activity, String params, boolean isReOpen, boolean gotoMtHome) {
        try {
            if (activity == null || TextUtils.isEmpty(params)) {
                //无容器
                if (gotoMtHome) {
                    QtitansUtils.gotoMTHomePage(activity);
                    ContainerReporter.logOpenIntent("openQtitansContainer params null & goHome", null, params == null ? "":params.toString());
                    return;
                }
                ContainerReporter.logOpenIntent("openQtitansContainer params null", null, params == null ? "": params.toString());
                return;
            }
            QtitansContainerParams containerParams = HadesUtils.fromJson(params, QtitansContainerParams.class);
            if (containerParams == null) {
                ContainerReporter.logOpenIntent("openQtitansContainer containerParams null", null, params);
                return;
            }
            if (!canUseQtitansContainer(containerParams)) {
                //降级，走业务侧容器
                try {
                    RouterManager.jumpToBizActivity(activity, containerParams.getTargetUrl());
                    ContainerReporter.logOpenIntent("openQtitansContainer downgrade", null, containerParams);
                } catch (Throwable e) {
                    Map<String, Object> map = new HashMap<>();
                    map.put(ReportParamsKey.CONTAINER.TIME, System.currentTimeMillis());
                    map.put("errorMsg", e.getMessage());
                    map.put("降级", e.getMessage());
                    map.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "openQtitansContainer");
                    ContainerReporter.reportLog(map);
                    ContainerReporter.logOpenIntent("openQtitansContainer downgrade error", null, containerParams,
                            Log.getStackTraceString(e));
                    QQThrowableReporter.report("QtitansContainerManager", e);
                }
                return;
            }
            //有容器场景需要再返回判断拦截
            if (gotoMtHome &&
                    !CurrentActivityUtil.isContainsTargetActivity(Qtitans_Container_Class)) {
                QtitansUtils.gotoMTHomePage(activity);
                ContainerReporter.logOpenIntent("openQtitansContainer isContainsTargetActivity", null, params);
                return;
            }
            Intent targetIntent = new Intent(Intent.ACTION_VIEW);
            Uri uri = Uri.parse(containerParams.getTargetUrl());
            if (uri == null) {
                ContainerReporter.logOpenIntent("openQtitansContainer uri null", null, params);
                return;
            }
            String path = uri.getPath();
            containerParams.setContainerType(ContainerType.containType(path));
            long time = System.currentTimeMillis();
            long uptime = SystemClock.uptimeMillis();
            containerParams.setTime(time)
                    .setUptime(uptime)
                    .setReopen(isReOpen)
                    .setGoToMTHome(gotoMtHome);
            boolean fromLuckin = false;
            if (activity.getIntent() != null) {
                fromLuckin = activity.getIntent().getBooleanExtra(QTitansLuckinSplashActivity.EXTRA_KEY_LUCKIN_SOURCE, false);
                boolean fromHades = activity.getIntent().getBooleanExtra(RouterManager.EXTRA_HADES_ROUTER_JUMP, false);
                targetIntent.putExtra(RouterManager.EXTRA_HADES_ROUTER_JUMP, fromHades);
            }
            String scheme = fromLuckin ? QTitansLuckinContainerActivity.Qtitans_MINIPROGRAM_SCHEME_BASE : Qtitans_SCHEME_BASE;
            targetIntent.setData(Uri.parse(scheme));
            targetIntent.putExtra(ContainerConst.Qtitans_CONTAINER_PARAMS, HadesUtils.toJson(containerParams));
            containerParams.setClearTop(false);
            if (!(isReOpen || gotoMtHome || ContainerConfigManager.getInstance().isFormMtAPPVisit(params))) {
                if (!fromLuckin && ContainerConfigManager.getInstance().isSameBusiness(containerParams)
                        && (isQtitansContainerActivityUp() || QtitansUtils.isVideoTab(containerParams.getLoadingViewParams()))) {
                    activity.finish();
                    activity.overridePendingTransition(0, 0);
                    Map<String, Object> log = new HashMap<>();
                    log.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "openQtitansContainer isQtitansContainerActivityUp");
                    log.put("tag", TAG);
                    ContainerReporter.reportLog(log);
                    ContainerReporter.logOpenIntent("openQtitansContainer isQtitansContainerActivityUp", targetIntent,
                            params);
                    return;
                }
            }
            targetIntent.putExtra(Dsp.INTENT_EXTRA_IS_DSP_COLD_START, true);
            activity.startActivity(targetIntent);
            activity.overridePendingTransition(0, 0);
            ContainerReporter.reportContainerJump(params, time, uptime);
            if (containerParams.getLoadingViewParams() != null &&
                    TextUtils.equals(containerParams.getLoadingViewParams().getVisitType(), QtitansContainerVisitType.VisitVideoWidget22.getType())) {
                WidgetRecorder.recordClick(activity, HadesWidgetEnum.FEATURE22_VIDEO, "", "videoFeature22Widget", true, "", false);
            }
            ContainerReporter.logOpenIntent("openQtitansContainer done", targetIntent, params);
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerManager", e);
            try {
                if (!QtitansUtils.isActivityFinish(activity)) {
                    activity.finish();
                }
                Map<String, Object> map = new HashMap<>();
                map.put(ReportParamsKey.CONTAINER.TIME, System.currentTimeMillis());
                map.put("errorMsg", e.getMessage());
                map.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "openQtitansContainer");
                ContainerReporter.reportLog(map);
                ContainerReporter.logOpenIntent("openQtitansContainer error", null, params, Log.getStackTraceString(e));
            } catch (Throwable ignore) {

            }
        }
    }

    public static boolean canUseQtitansContainer(JSONObject params) {
        try {
            if (params == null) {
                return false;
            }
            boolean checkRes = false;
            JSONObject loadingViewParams = params.optJSONObject("loadingViewParams");
            if (loadingViewParams!= null
                    && !loadingViewParams.optBoolean("gameSource")
                    && !TextUtils.isEmpty(params.optString("targetUrl"))
                    && useMiniProgramHorn()) {
                Uri uri = Uri.parse(params.optString("targetUrl"));
                if (uri != null) {
                    checkRes = true;
                }
            }
            return checkRes;
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerManager", e);
        }
        return false;
    }
    public static boolean canUseQtitansContainer(QtitansContainerParams params) {
        try {
            if (params == null) {
                return false;
            }
            boolean checkRes = false;
            if (params.getLoadingViewParams() != null
                    && !params.getLoadingViewParams().isGameSource()
                    && !TextUtils.isEmpty(params.getTargetUrl())
                    && useMiniProgramHorn()) {
                Uri uri = Uri.parse(params.getTargetUrl());
                if (uri != null) {
                    checkRes = true;
                }
            }
            return checkRes;
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerManager", e);
        }
        return false;
    }

    private static boolean useMiniProgramHorn() {
        boolean isOpen = HadesConfigMgr.getInstance(HadesUtils.getContext()).useMiniProgram();
        if (com.meituan.android.hades.BuildConfig.LOG_ENABLE) {
            Logger.d(" useMiniProgramHorn = " + isOpen);
        }
        return isOpen;
    }

    private static boolean isQtitansContainerActivityUp() {
        try {
            List<Activity> activities = CurrentActivityUtil.activities();
            if (activities == null || activities.isEmpty()) {
                return false;
            }
            int mainIndex = -1;
            int qtitansIndex = -1;
            int count = 0;
            for (Activity stackActivity : activities) {
                count++;
                if (stackActivity == null) {
                    continue;
                }
                if (TextUtils.equals(Dsp.ACTIVITY_MAIN, stackActivity.getClass().getName())) {
                    mainIndex = count;
                } else if (TextUtils.equals(Qtitans_Container_Class, stackActivity.getClass().getName())) {
                    qtitansIndex = count;
                }
            }
            if (qtitansIndex < 0) {
                return false;
            }
            return qtitansIndex > mainIndex;
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerManager", e);
        }
        return false;
    }

    public static void preloadIcon(Context context, String iconUrl) {
        try {
            if (context != null && !TextUtils.isEmpty(iconUrl)) {
                HadesUtils.runOnMainThread(() -> Picasso.with(context).load(iconUrl).fetch());
            }
        } catch (Throwable throwable) {
            QQThrowableReporter.report("QtitansContainerManager", throwable);
        }
    }

    /**
     * 跳转小程序intent，添加开屏数据loadingPageContent
     * <p>
     * 新增场景要添加类型QtitansContainerVisitType
     * * 新增场景要添加类型QtitansContainerVisitType
     * * 新增场景要添加类型QtitansContainerVisitType
     *
     * @param checkSource
     * @param businessType
     * @param loadingPageContent
     * @param intent
     */
    public void handleLoadingViewParams(String targetUrl, final String checkSource, String businessType,
                                        QtitansLoadingPageContent loadingPageContent, Intent intent,
                                        @NonNull QtitansContainerVisitType visitType,
                                        @Nullable QtitansContainerFunctionType functionType) {
        try {
            if (loadingPageContent != null) {
                LoadingViewParams loadingViewParams = new LoadingViewParams()
                        .setBottomText(loadingPageContent.bottomText)
                        .setBackgroundUrl(loadingPageContent.backgroundUrl)
                        .setLogoUrl(loadingPageContent.logoUrl)
                        .setIconUrl(loadingPageContent.icon)
                        .setIconName(loadingPageContent.iconName)
                        .setSlogan(loadingPageContent.slogan)
                        .setSubText(loadingPageContent.subText)
                        .setCheckSource(TextUtils.isEmpty(checkSource) ? "" : checkSource)
                        .setBusinessType(TextUtils.isEmpty(businessType) ? "" : businessType)
                        .setUseMiniProgram(loadingPageContent.useMiniProgram)
                        .setGameSource(loadingPageContent.gameSource)
                        .setUseScreenPage(loadingPageContent.useScreenPage)
                        .setUseCapsule(loadingPageContent.useCapsule)
                        .setVisitType(visitType == null ? QtitansContainerVisitType.UNKNOWN.getType()
                                : visitType.getType())
                        .setFunctionType(functionType == null ? QtitansContainerFunctionType.UNKNOWN.getType()
                                : functionType.getType());

                //push 类型
                if (loadingPageContent.mContainerPushInfo != null) {
                    loadingViewParams.setPushTypeContainer(loadingPageContent.mContainerPushInfo.pushTypeContainer);
                }
                String loadingViewParamsStr = HadesUtils.toJson(loadingViewParams);
                if (BuildConfig.LOG_ENABLE) {
                    QtitansLog.log("handleLoadingViewParams: loadingViewParamsStr = " + loadingViewParamsStr);
                }
                if (!TextUtils.isEmpty(loadingViewParamsStr)) {
                    intent.putExtra(QtitansContainerManager.Qtitans_LOADING_VIEW_PARAMS, loadingViewParamsStr);
                }
            }
            HadesUtils.runOnWorkThread(() -> {
                try {
                    Map<String, Object> map = new HashMap<>();
                    map.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "handleLoadingViewParams");
                    if (!TextUtils.isEmpty(targetUrl)) {
                        map.put("targetUrl", targetUrl);
                    }
                    map.put("visitType", visitType == null ? QtitansContainerVisitType.UNKNOWN.getType()
                            : visitType.getType());
                    map.put("functionType", functionType == null ? QtitansContainerFunctionType.UNKNOWN.getType()
                            : functionType.getType());
                    if (loadingPageContent != null) {
                        map.put("useMiniProgram", loadingPageContent.useMiniProgram);
                    }
                    ContainerReporter.reportLog(map);
                } catch (Throwable e) {
                    QQThrowableReporter.report("QtitansContainerManager", e);
                }
            });
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerManager", e);
        }
    }


    /**
     * 跳链追加来源类型
     *
     * @param target
     * @param qtitansContainerVisitType
     * @return
     */
    public String appendPinContainerVisitKey(String target, String qtitansContainerVisitType) {
        try {
            long start = 0;
            if (BuildConfig.LOG_ENABLE) {
                Log.d(TAG, "appendPinContainerKey: old target " + target);
                start = System.currentTimeMillis();
            }
            if (TextUtils.isEmpty(target)) {
                return target;
            }
            Uri oldUri = Uri.parse(target);
            Set<String> params = new HashSet<>(oldUri.getQueryParameterNames());
            //重复key
            if (qtitansContainerVisitType != null) {
                params.remove(ContainerConst.pinContainerVisitKey);
            }

            Uri.Builder newUri = oldUri.buildUpon().clearQuery();
            for (String param : params) {
                newUri.appendQueryParameter(param, oldUri.getQueryParameter(param));
            }
            if (qtitansContainerVisitType != null) {
                newUri.appendQueryParameter(ContainerConst.pinContainerVisitKey,
                        qtitansContainerVisitType);
            }
            target = newUri.toString();
            if (BuildConfig.LOG_ENABLE) {
                Log.d(TAG, "appendPinContainerKey: new target " + target + "  diff time = " + (System.currentTimeMillis() - start));
            }
            return target;
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerManager", e);
        }
        return target;
    }

    /**
     * 容器桥通用能力
     * https://km.sankuai.com/collabpage/1856730349
     *
     * @param bridgeData
     * @param containerCallback
     */
    public void pinContainer(QtitansBridgeParams bridgeData, ContainerCallback
            containerCallback) {
        try {
            if (bridgeData == null || bridgeData.mBridgeData == null) {
                if (containerCallback != null) {
                    containerCallback.onFail(-1, "pinContainer error 数据");
                }
                return;
            }
            String function = bridgeData.mBridgeData.function;
            QtitansContainerFunctionType qtitansContainerFunctionType = QtitansContainerFunctionType.containType(function);
            switch (qtitansContainerFunctionType) {
                case UNKNOWN:
                default:
            }
            Map<String, Object> map = new HashMap<>();
            map.put(ReportParamsKey.CONTAINER.TIME, System.currentTimeMillis());
            map.put("pinContainerParams", bridgeData.toString());
            map.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "openQtitansContainer");
            ContainerReporter.reportLog(map);
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerManager", e);
        }

    }

    private void moveTaskToBack(Activity hostActivity) {
        try {
            if (QtitansUtils.isActivityFinish(hostActivity)) {
                return;
            }
            hostActivity.moveTaskToBack(true);
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerManager", e);
        }
    }

    public static DeskResourceData getDebugDeskResourceData(Context context) {
        DeskResourceData resourceData = null;
        if (BuildConfig.LOG_ENABLE) {
            if (StorageHelper.getKeyDevHadesContainer(context)) {
                Object object = HadesUtils.invoke("com.meituan.android.hades.DevTools", "buildTestDeskResourceData");
                if (object != null) {
                    resourceData = (DeskResourceData) object;
                }
            }
        }
        return resourceData;
    }

    public boolean isContainerDegraded(QtitansContainerConfig containerConfig) {
        return containerConfig != null
                && containerConfig.containerDegraded;
    }

    public boolean isStartUrlSameAsLatest(QtitansContainerConfig containerConfig, String startUrl) {
        return containerConfig != null
                && !TextUtils.isEmpty(containerConfig.latestUnderTakeUrl)
                && !TextUtils.isEmpty(startUrl)
                && containerConfig.latestUnderTakeUrl.equals(startUrl);
    }

    public void autoCheck(Activity activity) {
        try {
            if (QtitansUtils.isActivityFinish(activity)) {
                return;
            }
            PageConfig pageConfig = ContainerConfigManager.getInstance().getPageConfig();
            if (pageConfig == null || pageConfig.checkSource <= 0) {
                Map<String, Object> map = new HashMap<>();
                map.put("msg", "checkSource == null");
                ContainerReporter.reportLog("autoInstall", map);
                return;
            }
            WeakReference<Activity> weakReference = new WeakReference<>(activity);
            HadesUtils.runOnWorkThread(() -> {
                if (weakReference.get() == null) {
                    return;
                }
                String res = check(weakReference.get(), pageConfig);
                Map<String, Object> map = new HashMap<>();
                map.put("msg", res);
                map.put("checkSource", pageConfig.checkSource);
                ContainerReporter.reportLog("autoInstall", map);
            });
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerManager", e);
        }
    }

    private String check(Activity activity, PageConfig pageConfig) {
        try {
            if (activity == null
                    || pageConfig == null
                    || pageConfig.checkSource <= 0
                    || QtitansUtils.isActivityFinish(activity)) {
                return "null";
            }
            HadesWidgetEnum hadesWidgetEnum = getHadesWidgetEnum(String.valueOf(pageConfig.checkSource));
            if (hadesWidgetEnum == null) {
                return "desk app null ";
            }
            final String subscribeScene = "qq_extern_auto";
            Pin.check(activity, pageConfig.checkSource, "", subscribeScene, hadesWidgetEnum.getFwTemplateId(), new PinCallback() {
                @Override
                public void onSuccess(JSONObject jsonObject) {
                    ContainerConfigManager.getInstance().getExternalLinkInfo().put("checkSource", String.valueOf(pageConfig.checkSource));
                    Map<String, Object> map = new HashMap<>();
                    map.put("msg", "check onSuccess");
                    map.put("checkSource", pageConfig.checkSource);
                    ContainerReporter.reportLog("autoInstall", map);
                }

                @Override
                public void onError(int errorCode, String errMsg) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("msg", "check onError");
                    map.put("errorCode", errorCode);
                    map.put("errMsg", errMsg);
                    map.put("checkSource", pageConfig.checkSource);
                    ContainerReporter.reportLog("autoInstall", map);
                }
            });
        } catch (Throwable e) {
            Map<String, Object> map = new HashMap<>();
            map.put("msg", "check throwable = " + e.getMessage());
            ContainerReporter.reportLog("autoInstall", map);
            QQThrowableReporter.report("QtitansContainerManager", e);
        }
        return "Check ok";
    }

    public void autoInstall(Activity activity) {
        try {
            if (QtitansUtils.isActivityFinish(activity)) {
                return;
            }
            Pair<String, Boolean> pairCheck = ContainerConfigManager.getInstance().autoInstallCheckResult();
            if (!pairCheck.second) {
                return;
            }
            HadesWidgetEnum hadesWidgetEnum = getHadesWidgetEnum(pairCheck.first);
            if (hadesWidgetEnum == null) {
                return;
            }
            boolean isWidgetInstalled = HadesWidgetUtils.isWidgetInstalled(activity, hadesWidgetEnum);
            if (isWidgetInstalled) {
                Toast.makeText(activity, "已安装", Toast.LENGTH_LONG).show();
                return;
            }
            WeakReference<Activity> weakReference = new WeakReference<>(activity);
            Pin.process(weakReference, Integer.parseInt(pairCheck.first), "", hadesWidgetEnum.getFwTemplateId(), new PinCallback() {
                @Override
                public void onSuccess(JSONObject jsonObject) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("msg", "install onSuccess");
                    map.put("checkSource", pairCheck.first);
                    ContainerReporter.reportLog("autoInstall", map);
                }

                @Override
                public void onError(int errorCode, String errMsg) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("msg", "install onError");
                    map.put("errorCode", errorCode);
                    map.put("errMsg", errMsg);
                    map.put("checkSource", pairCheck.first);
                    ContainerReporter.reportLog("autoInstall", map);
                }
            });
        } catch (Throwable e) {
            Map<String, Object> map = new HashMap<>();
            map.put("msg", "install throwable = " + e.getMessage());
            ContainerReporter.reportLog("autoInstall", map);
            QQThrowableReporter.report("QtitansContainerManager", e);
        }
    }

    public void clear() {
        sINSTANCE = null;
    }


}
