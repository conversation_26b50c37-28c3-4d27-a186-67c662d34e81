package com.meituan.android.qtitans.container.qqflex.listview;

import android.support.annotation.Size;
import android.support.v7.widget.RecyclerView;
import android.support.v7.widget.StaggeredGridLayoutManager;
import android.view.View;

import com.meituan.android.dynamiclayout.controller.LayoutController;
import com.meituan.android.dynamiclayout.utils.CommonUtil;

public abstract class QtitansFlexLoadMoreListener extends RecyclerView.OnScrollListener {
    @Override
    public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
        super.onScrolled(recyclerView, dx, dy);
        if (dy > 0) {
            loadMore(recyclerView);
        }
        dynamicReport(recyclerView);

    }

    @Override
    public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
        super.onScrollStateChanged(recyclerView, newState);
        if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
            loadMore(recyclerView);
        }
        if (newState == RecyclerView.SCROLL_STATE_IDLE) {
            dynamicReport(recyclerView);
        }
    }

    private void loadMore(RecyclerView recyclerView) {
            StaggeredGridLayoutManager layoutManager = (StaggeredGridLayoutManager) recyclerView.getLayoutManager();
            int[] positions = new int[layoutManager.getSpanCount()];
            layoutManager.findLastVisibleItemPositions(positions);
            int maxPosition = -1;
            for (int position:positions) {
                if (position > maxPosition) {
                    maxPosition = position;
                }
            }
            if ( maxPosition >= layoutManager.getItemCount() - 1) {
                tryLoadMore();
            }

    }
    public abstract void tryLoadMore();

    public void dynamicReport(RecyclerView recyclerView) {
        if (recyclerView == null) return;
        recyclerView.post(()->{
            int[] visibleScreenBound = getDynamicHeaderVisibleBound(recyclerView);
            if (visibleScreenBound.length < 4) {
                return;
            }
            int childCount = recyclerView.getChildCount();

            for (int i = 0; i < childCount; i++) {
                View view = recyclerView.getChildAt(i);
                if (view == null) {
                    return;
                }
                Object layoutController = view.getTag();
                if (layoutController instanceof LayoutController) {
                    setExposeStrategy((LayoutController) layoutController, visibleScreenBound);
                    ((LayoutController) layoutController).notifyExposureChanged(null);
                }

            }
        });

    }

    private int[] getDynamicHeaderVisibleBound(RecyclerView recyclerView) {
        int[] visibleBound = new int[4];
        if (recyclerView == null) {
            return visibleBound;
        }
        int[] location = new int[2];
        int height = CommonUtil.getDisplayHeight(recyclerView.getContext());
        recyclerView.getLocationOnScreen(location);
        visibleBound[0] = recyclerView.getLeft();
        visibleBound[1] = recyclerView.getRight();
        visibleBound[2] = location[1];
        visibleBound[3] = height;
        return visibleBound;
    }

    private void setExposeStrategy(LayoutController layoutController,
                                   @Size(4) int[] visibleScreenBound) {
        if (layoutController == null || visibleScreenBound.length != 4) {
            return;
        }
        layoutController.setExposureVisibleScreenBound(visibleScreenBound[0], visibleScreenBound[1],
                visibleScreenBound[2], visibleScreenBound[3]);
        layoutController.setExposureTraceStrategy(0, 0);
    }
}
