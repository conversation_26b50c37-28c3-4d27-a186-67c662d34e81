package com.meituan.android.qtitans.container.qqflex.event;

import com.google.gson.reflect.TypeToken;
import com.meituan.android.dynamiclayout.controller.LayoutController;
import com.meituan.android.dynamiclayout.controller.event.Event;
import com.meituan.android.dynamiclayout.controller.event.EventListener;
import com.meituan.android.dynamiclayout.controller.event.EventScope;
import com.meituan.android.dynamiclayout.viewmodel.BaseTag;
import com.meituan.android.hades.impl.BuildConfig;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;

import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

public abstract class QtitansFlexCardViewClickEvent extends EventListener {
    private static final String TAG = QtitansFlexCardViewClickEvent.class.getName();

    public QtitansFlexCardViewClickEvent() {
        super("cardViewClickAction", EventScope.PAGE, null);
    }

    public QtitansFlexCardViewClickEvent(String action, EventScope scope, List<BaseTag> children) {
        super(action, scope, children);
    }

    @Override
    public void handleEvent(Event event, LayoutController layoutController) {
        super.handleEvent(event, layoutController);
        try {
            JSONObject object = event.getData();
            if (object != null) {
                String action = object.getString("action");
                Type type = new TypeToken<Map<String, Object>>() {
                }.getType();
               Map<String, Object> data =  HadesUtils.fromJson(object.toString(), type);
                handleClick(action, data);
            }
        } catch (Throwable e) {
            if (BuildConfig.LOG_ENABLE) {
                e.printStackTrace();
            }
            Logger.e(TAG, "handle event failed:" + e.getMessage());
        }
    }
    protected abstract void handleClick(String action, Map<String, Object> data);
}
