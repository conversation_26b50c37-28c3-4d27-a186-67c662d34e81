package com.meituan.android.qtitans.container.qqflex.listview;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.v7.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

public abstract class QtitansBaseViewHolder<VH extends RecyclerView.ViewHolder, D>{
    private static final String TAG = "QtitansBaseItemView";
    protected Context context;
    public QtitansBaseViewHolder() {
    }

    public VH createViewHolder(ViewGroup parent) {
        this.context = parent.getContext();
        return this.createViewHolder(parent, LayoutInflater.from(context));
    }

    protected abstract VH createViewHolder(ViewGroup var2, LayoutInflater inflater);

    public abstract void onBindViewHolder(VH var1, D var2, int var3);

    public void onViewAttachedToWindow(@NonNull VH holder) {
    }

    public void onViewDetachedFromWindow(@NonNull VH holder) {
    }

    public void onViewRecycled(@NonNull VH holder) {
    }

    public static class BaseViewHolder<D> extends RecyclerView.ViewHolder {
        public ViewGroup parent;
        public QtitansBaseViewHolder<BaseViewHolder<D>, D> baseItemView;

        public BaseViewHolder(View itemView, ViewGroup parent, QtitansBaseViewHolder<BaseViewHolder<D>, D> baseItemView) {
            super(itemView);
            this.parent = parent;
            this.baseItemView = baseItemView;
        }

        public void bindView(int position, D itemData) {
            this.baseItemView.onBindViewHolder(this, itemData, position);
        }

        public void onViewAttachedToWindow(@NonNull BaseViewHolder<D> holder) {
            this.baseItemView.onViewAttachedToWindow(holder);
        }

        public void onViewDetachedFromWindow(@NonNull BaseViewHolder<D> holder) {
            this.baseItemView.onViewDetachedFromWindow(holder);
        }

        public void onViewRecycled(@NonNull BaseViewHolder<D> holder) {
            this.baseItemView.onViewRecycled(holder);
        }
    }
}
