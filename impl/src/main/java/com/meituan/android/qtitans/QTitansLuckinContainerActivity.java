package com.meituan.android.qtitans;

import com.meituan.android.hades.dyadater.container.ContainerServiceFactory;
import com.meituan.android.hades.dyadater.container.interfaces.IContainerProvider;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.sankuai.titans.base.TitansFragment;

public class QTitansLuckinContainerActivity extends QtitansContainerActivity {

    public final static String Qtitans_MINIPROGRAM_SCHEME_BASE = "imeituan://www.meituan.com/qtitans/container/luckin";
    public static final String ACTION_REFRESH_CARD = "com.meituan.android.hades.savemoneycard.update";
    public static final String EXTRA_CARD_URL = "extra_card_url";
    private static final String TAG = "QtitansContainer";
    private final static String KNB_EVENT_KEY_RESORT_TOP_SPU = "qq_event_resort_top_spu";

    @Override
    protected void onRestart() {
        try {
            super.onRestart();
            if (mDelegate != null) {
                mDelegate.onReStart(this);
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QTitansLuckinContainerActivity", e);
        }
    }

    @Override
    protected void handleBackPress() {
        try {
            if (mDelegate != null &&  mDelegate.getTitansFragment() != null && mDelegate.getTitansFragment() instanceof  TitansFragment && ((TitansFragment)mDelegate.getTitansFragment()).onBackPressed()) {
                return;
            }
            moveTaskToBack(true);
            IContainerProvider provider = ContainerServiceFactory.getInstance().getProvider();
            if (provider != null && provider.getContainerConfig() != null) {
                provider.getContainerConfig().removeLifeCycleListener();
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QTitansLuckinContainerActivity", e);
        }
    }

    @Override
    protected String getScene() {
        return "luckin";
    }
}
