package com.meituan.android.qtitans.container.qqflex.listview;

import android.text.TextUtils;

import com.meituan.android.dynamiclayout.adapters.ReporterImpl;
import com.meituan.android.dynamiclayout.controller.reporter.IReporter;

import org.json.JSONObject;

import java.util.Map;
import java.util.Set;

public class QtitansFlexReporter implements IReporter{
    private IReporter reporter = ReporterImpl.getInstance();
    private Set<Integer> mge = new ConcurrentHashSet<>();
    private Set<Integer> mge2 = new ConcurrentHashSet<>();
    private Set<Integer> mge4 = new ConcurrentHashSet<>();
    private Set<Integer> ad = new ConcurrentHashSet<>();
    private Set<Integer> custom = new ConcurrentHashSet<>();

    private static final IReporter INSTANCE = new QtitansFlexReporter();

    public static IReporter getInstance() {
        return INSTANCE;
    }

    public void mge(int reportMode, String cid, String act, String val, String lab) {
        int code = (reportMode + cid + act + val + lab).hashCode();
        if (reportMode == REPORT_MODE_SEE && !canReport(mge, code)) {
            return;
        }
        reporter.mge(reportMode, cid, act, val, lab);
    }

    public void tag(int reportMode, String s, Map<String, Object> map) {
        reporter.tag(reportMode, s, map);
    }

    public void ad(int reportMode, String s, String s1, String s2) {
        reporter.ad(reportMode, s, s1, s2);
    }

    public void g(int reportMode, String s) {
        reporter.g(reportMode, s);
    }

    public void custom(int reportMode, Map<String, String> map) {
        if (reportMode == REPORT_MODE_SEE && !canReport(custom, map.hashCode())) {
            return;
        }
        reporter.custom(reportMode, map);
    }

    public void mge2(int reportMode, JSONObject jsonObject) {
        int code = jsonObject.toString().hashCode();
        if (reportMode == REPORT_MODE_SEE && !canReport(mge2, code)) {
            return;
        }
        reporter.mge2(reportMode, jsonObject);
    }

    public void mge4(int reportMode, JSONObject jsonObject) {
        String eventType;
        switch (reportMode) {
            case REPORT_MODE_SEE:
            case REPORT_MODE_LOAD:
                eventType = "view";
                break;
            case REPORT_MODE_CLICK:
                eventType = "click";
                break;
            default:
                return;
        }
        if (TextUtils.equals(eventType, "view") && !canReport(mge4, jsonObject.toString().hashCode())) {
            return;
        }
        reporter.mge4(reportMode, jsonObject);
    }
    private boolean canReport(Set<Integer> set, int code) {
        return set.add(code);
    }

    public void reset() {
        mge.clear();
        mge2.clear();
        mge4.clear();
        custom.clear();
        ad.clear();
    }
}
