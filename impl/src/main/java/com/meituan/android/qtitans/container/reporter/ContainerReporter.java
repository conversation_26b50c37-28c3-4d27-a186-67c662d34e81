package com.meituan.android.qtitans.container.reporter;

import static com.meituan.android.hades.router.RouterManager.DEFAULT_LCH_WIDGET;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.SystemClock;
import android.support.annotation.Keep;
import android.support.v4.content.ContextCompat;
import android.text.TextUtils;

import com.meituan.android.hades.BuildConfig;
import com.meituan.android.hades.HadesWidgetEnum;
import com.meituan.android.hades.dyadater.container.ContainerConst;
import com.meituan.android.hades.dyadater.container.ContainerType;
import com.meituan.android.hades.dyadater.container.QtitansContainerVisitType;
import com.meituan.android.hades.dyadater.desk.DeskResourceData;
import com.meituan.android.hades.impl.model.BaseResponse;
import com.meituan.android.hades.impl.net.HadesRetrofit;
import com.meituan.android.hades.impl.report.BabelHelper;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.report.ReportParamsKey;
import com.meituan.android.hades.impl.utils.DeviceInfo;
import com.meituan.android.hades.impl.utils.HadesBizReportUtils;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.impl.utils.LxReportUtils;
import com.meituan.android.hades.impl.utils.StorageHelper;
import com.meituan.android.qtitans.container.bean.LoadingViewParams;
import com.meituan.android.qtitans.container.bean.PopupType;
import com.meituan.android.qtitans.container.bean.QtitansContainerParams;
import com.meituan.android.qtitans.container.bean.QtitansNotificationStatus;
import com.meituan.android.qtitans.container.common.QtitansLog;
import com.meituan.android.qtitans.container.config.ContainerConfigManager;
import com.meituan.android.qtitans.container.qqflex.lucykin.QtitansLuckinPreRequestManager;
import com.meituan.passport.UserCenter;
import com.sankuai.common.utils.JsonHelper;
import com.sankuai.meituan.retrofit2.Call;
import com.sankuai.meituan.retrofit2.Callback;
import com.sankuai.meituan.retrofit2.Response;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

@Keep
public class ContainerReporter {
    private static final String TAG = "ContainerReporter";
    private static final String MT_QQ_CONTAINER = "mt-qq-container";// 容器标记

    /**
     * 站外 桌面 PUSH等
     */
    public static final String CID_CONTAINER_OUT_PV = "c_lintopt_epfskihb";

    /**
     * 站内 容器内
     */
    public static final String CID_CONTAINER_INNER_PV = "c_lintopt_lu8ykump";

    public static final String CID_LUCKIN_SAVE_MONEY_PAGE = "c_lintopt_76r2m2am";

    public static final String BID_CONTAINER_VISIT_REWARD_DIALOG_MV = "b_lintopt_18l2z1ex_mv";//复访弹窗曝光
    public static final String BID_CONTAINER_VISIT_REWARD_DIALOG_MC = "b_lintopt_jod977zd_mc";//复访弹窗点击
    public static final String BID_CONTAINER_VISIT_CLOSE_MC = "b_lintopt_irk9q33p_mc";//复访弹窗关闭
    public static final String BID_CONTAINER_BOTTOM_FLOAT_MV = "b_lintopt_6a12ifpd_mv";//底部浮层曝光
    public static final String BID_CONTAINER_BOTTOM_FLOAT_MC = "b_lintopt_x0fafhhq_mc";//底部浮层去领取点击
    public static final String BID_CONTAINER_BOTTOM_FLOAT_CLOSE_MC = "b_lintopt_dj7cms7a_mc";//底部浮层关闭


    /**
     * 站外入口widget
     */
    public static final String BID_CONTAINER_OUT_WIDGET_MC = "b_lintopt_3gkdhgf6_mc";
    //站外入口push
    public static final String BID_CONTAINER_OUT_PUSH_MC = "b_lintopt_zo4ur58n_mc";
    public static final String BID_CONTAINER_OUT_PUSH_MV = "b_lintopt_zo4ur58n_mv";
    //胶囊
    public static final String BID_CONTAINER_CAPSULE_LEFT_MC = "b_lintopt_h52piwma_mc";
    public static final String BID_CONTAINER_CAPSULE_RIGHT_MC = "b_lintopt_dsmv421z_mc";
    //复访去完成引导弹窗
    public static final String BID_CONTAINER_REVISIT_DIALOG_MV = "b_lintopt_zfrcoiff_mv";
    public static final String BID_CONTAINER_REVISIT_DIALOG_MC = "b_lintopt_d9spvh1e_mc";
    //省钱卡页面mv
    private static final String BID_LUCKIN_PAGE_DEAL = "b_lintopt_4f4ismep_mv";
    private static final String BID_LUCKIN_DEAL_CLICK_BUY_STATUS = "b_lintopt_ut4h0t41_mv";


    // 节点
    /**
     * 跳容器
     */
    private static final String CONTAINER_JUMP = "containerJump";
    private static final String CONTAINER_CREATE = "containerCreate";
    private static final String CONTAINER_NEW_INTENT = "containerNewIntent";
    private static final String CONTAINER_DESTROY = "containerDestroy";
    private static final String CONTAINER_AUTO_TIMER = "containerAutoTimer";
    private static final String CONTAINER_T1_REPORT = "containerT3Report";
    private static final String CONTAINER_T0_REPORT = "containerT0Report";
    private static final String PRE_RENDER_CONTAINER_REPORT = "preRenderContainer";
    private static final String CONTAINER_STOP = "containerStop";


    private static final String CONTAINER_DURATION = "containerDuration";

    /**
     * 复访PUSH
     *
     * @param map
     * @param deskResourceData
     */
    public static void reportRevisitPushExposure(Map<String, Object> map, DeskResourceData deskResourceData) {
        if (deskResourceData == null
                || deskResourceData.mContainerPushInfo == null
                || map == null) {
            return;
        }
        HadesUtils.runOnWorkThread(() -> {
            try {
                map.put(ReportParamsKey.CONTAINER.CONTAINER_BUSINESS, deskResourceData.mContainerPushInfo.businessType);
                map.put(ReportParamsKey.CONTAINER.CONTAINER_SOURCE, deskResourceData.mContainerPushInfo.checkSource);
                map.put(ReportParamsKey.CONTAINER.CONTAINER_PUSH_TYPE, deskResourceData.mContainerPushInfo.pushTypeContainer);
                LxReportUtils.viewEvent(CID_CONTAINER_OUT_PV, BID_CONTAINER_OUT_PUSH_MV, map);
                reportLog(map);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });

    }

    /**
     * 胶囊
     */
    public static void reportContainerCapsule(String bid) {
        if (bid == null) {
            return;
        }
        HadesUtils.runOnWorkThread(() -> {
            try {
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_BUSINESS,
                        ContainerConfigManager.getInstance().getCurrentBusinessType());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_SOURCE,
                        ContainerConfigManager.getInstance().getCurrentCheckSource());
                LxReportUtils.clickEvent(CID_CONTAINER_INNER_PV, bid, valLabMap);
                reportLog(valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });

    }

    public static void reportContainerNf(String mgs, QtitansNotificationStatus notificationStatus) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_BUSINESS,
                        ContainerConfigManager.getInstance().getCurrentBusinessType());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_SOURCE,
                        ContainerConfigManager.getInstance().getCurrentCheckSource());
                if (notificationStatus != null) {
                    valLabMap.put("mainNotificationStatus", notificationStatus.mainNotificationStatus);
                    valLabMap.put("notifications", notificationStatus.notifications);
                }
                reportLog("ContainerNf-" + mgs, valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });

    }

    /**
     * 容器跳转
     *
     * @param params
     * @param time
     * @param uptime
     */
    public static void reportContainerJump(String paramsStr, long time, long uptime) {
        if (TextUtils.isEmpty(paramsStr)) {
            return;
        }
        HadesUtils.runOnWorkThread(() -> {
            try {
                QtitansContainerParams params = HadesUtils.fromJson(paramsStr, QtitansContainerParams.class);
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, CONTAINER_JUMP);
                valLabMap.put(ReportParamsKey.CONTAINER.TIME, time);
                valLabMap.put(ReportParamsKey.CONTAINER.UPTIME, uptime);
                valLabMap.put("container_provider", "local");
                addQtitansContainerParams(valLabMap, params);
                valLabMap.put(ReportParamsKey.CONTAINER.QC_U_ALIAS, StorageHelper.getQcUpdateAlias(HadesUtils.getContext()));
                boolean isContainerPush = params.getLoadingViewParams() != null
                        && !TextUtils.isEmpty(params.getLoadingViewParams().getPushTypeContainer());
                if (isContainerPush) {
                    valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PUSH_TYPE, params.getLoadingViewParams().getPushTypeContainer());
                }
                LxReportUtils.clickEvent(CID_CONTAINER_OUT_PV,
                        isContainerPush ? BID_CONTAINER_OUT_PUSH_MC : BID_CONTAINER_OUT_WIDGET_MC, valLabMap);
                reportLog(valLabMap);
                HadesBizReportUtils.reportDeskAppSubscribe(HadesBizReportUtils.EVENT_TYPE_DESK_APP_CLICK, valLabMap);

                // 小程序、省钱卡、短视频22点击
                HadesUtils.runOnWorkThread(() -> {
                    try {
                        String visitType = getContainerVisitType(params);
                        int widgetNumCode = params.getSceneCode();
                        HadesWidgetEnum widgetEnum = HadesWidgetEnum.getInstanceByWidgetNumCode(widgetNumCode);
                        String checkSource = getCheckSource(params);
                        String scene = StorageHelper.getCardBindScene(HadesUtils.getContext(), widgetEnum);
                        Context context = HadesUtils.getContext();
                        if (TextUtils.equals(visitType, QtitansContainerVisitType.VisitWidget.getType())
                                || TextUtils.equals(visitType, QtitansContainerVisitType.VisitVideoWidget22.getType())
                        ) {
                            HadesRetrofit.getInstance(context).reportWidgetClick(widgetEnum, widgetEnum.getCardType(),
                                    checkSource, scene, "1").execute();
                        } else if (TextUtils.equals(visitType, QtitansContainerVisitType.VisitPush.getType())) {
                            HadesRetrofit.getInstance(context).reportWidgetClick(widgetEnum, widgetEnum.getCardType(),
                                    checkSource, scene, "0").execute();
                        } else {
                            HadesRetrofit.getInstance(context).reportWidgetClick(widgetEnum, widgetEnum.getCardType(),
                                    checkSource, scene, "-1").execute();
                        }
                    } catch (Throwable e) {
                        QQThrowableReporter.report("ContainerReporter", e);
                    }
                });
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    public static void reportContainerDuration(QtitansContainerParams params, long q_duration
            , long q_duration_back, String q_duration_back_times, boolean longReport) {
        if (params == null || q_duration <= 0) {
            return;
        }
        long time = System.currentTimeMillis();
        long uptime = SystemClock.uptimeMillis();
        HadesUtils.runOnWorkThread(() -> {
            try {
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put("container_provider", "local");
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, CONTAINER_DURATION);
                valLabMap.put(ReportParamsKey.CONTAINER.TIME, time);
                valLabMap.put("longReport", longReport);
                valLabMap.put(ReportParamsKey.CONTAINER.UPTIME, uptime);
                valLabMap.put(ReportParamsKey.CONTAINER.Q_DURATION, q_duration);
                valLabMap.put(ReportParamsKey.CONTAINER.Q_DURATION_Back, q_duration_back);
                valLabMap.put(ReportParamsKey.CONTAINER.Q_DURATION_Back_Time, q_duration_back_times);
                addQtitansContainerParams(valLabMap, params);
                boolean isContainerPush = params.getLoadingViewParams() != null
                        && !TextUtils.isEmpty(params.getLoadingViewParams().getPushTypeContainer());
                if (isContainerPush) {
                    valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PUSH_TYPE, params.getLoadingViewParams().getPushTypeContainer());
                }
                LxReportUtils.pageEvent(CID_CONTAINER_INNER_PV, valLabMap);
                reportLog(valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    public static void reportContainerCreate(QtitansContainerParams params, long time, long uptime) {
        if (params == null) {
            return;
        }
        long jumpTime = params.getTime();
        long jumpUptime = params.getUptime();
        HadesUtils.runOnWorkThread(() -> {
            try {
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put("container_provider", "local");
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, CONTAINER_CREATE);
                valLabMap.put(ReportParamsKey.CONTAINER.TIME, time);
                valLabMap.put(ReportParamsKey.CONTAINER.TIME_DUR, time - jumpTime);
                valLabMap.put(ReportParamsKey.CONTAINER.UPTIME, uptime);
                valLabMap.put(ReportParamsKey.CONTAINER.UPTIME_DUR, uptime - jumpUptime);
                addQtitansContainerParams(valLabMap, params);
                reportLog(valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    public static void reportContainerNewIntent(QtitansContainerParams params) {
        if (params == null) {
            return;
        }
        long time = System.currentTimeMillis();
        HadesUtils.runOnWorkThread(() -> {
            try {
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put("container_provider", "local");
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, CONTAINER_NEW_INTENT);
                valLabMap.put(ReportParamsKey.CONTAINER.TIME, time);
                reportLog(valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    private static void addQtitansContainerParams(Map<String, Object> valLabMap, QtitansContainerParams params) {
        try {
            if (valLabMap == null || params == null) {
                return;
            }
            try {
                int widgetNumCode = params.getSceneCode();
                HadesWidgetEnum widgetEnum = HadesWidgetEnum.getInstanceByWidgetNumCode(widgetNumCode);
                String scene = StorageHelper.getCardBindScene(HadesUtils.getContext(), widgetEnum);
                valLabMap.put(ReportParamsKey.WIDGET.PIN_SCENE, scene);
                valLabMap.put(ReportParamsKey.WIDGET.CARD_MARK, StorageHelper.getCardBindCardMark(HadesUtils.getContext(), widgetEnum));
            } catch (Exception ignored) {
            }
            valLabMap.put("container_provider", "local");
            valLabMap.put(ReportParamsKey.CONTAINER.JUMP_SCENE, params.getJumpScene());
            valLabMap.put(ReportParamsKey.CONTAINER.SCENE_CODE, params.getSceneCode());
            String lch = HadesUtils.getLchFromTargetUrl(params.getTargetUrl());
            valLabMap.put(ReportParamsKey.CONTAINER.LCH, TextUtils.isEmpty(lch) ? DEFAULT_LCH_WIDGET : lch);
            valLabMap.put(ReportParamsKey.CONTAINER.TARGET_URL, params.getTargetUrl());
            valLabMap.put(ReportParamsKey.CONTAINER.RESOURCE_ID, params.getResourceId());
            valLabMap.put(ReportParamsKey.CONTAINER.REOPEN, params.isReopen());
            valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_BUSINESS, getBusinessType(params));
            valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_SOURCE, getCheckSource(params));
            valLabMap.put(ReportParamsKey.CONTAINER.CARD_STATUS, getCardStatus(params));
            valLabMap.put(ReportParamsKey.CONTAINER.BRAN_ID, getBrandId(params));
            valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_TYPE, getContainerType(params));
            valLabMap.put(ReportParamsKey.CONTAINER.MGC, params.isMgc());
            valLabMap.put(ReportParamsKey.CONTAINER.GAME_SOURCE, isGameSource(params));
            valLabMap.put(ReportParamsKey.CONTAINER.PUSH_TYPE, getPushTypeContainer(params));
            valLabMap.put(ReportParamsKey.CONTAINER.CLEAR_TOP, params.isClearTop());
            valLabMap.put(ReportParamsKey.CONTAINER.GOTO_HOME, params.isGoToMTHome());
            valLabMap.put(ReportParamsKey.CONTAINER.VISIT_TYPE, getContainerVisitType(params));
            valLabMap.put(ReportParamsKey.CONTAINER.FUNCTION_TYPE, getContainerFunctionType(params));
            valLabMap.put(ReportParamsKey.CONTAINER.SCREEN_ON, HadesUtils.isScreenOn(HadesUtils.getContext()));
            valLabMap.put(ReportParamsKey.CONTAINER.QC_EXTR, HadesUtils.toJson(params));
            valLabMap.put("useCapsule", params.useCapsule());
        } catch (Throwable e) {
            QQThrowableReporter.report("ContainerReporter", e);
        }
    }

    public static void reportContainerT1Time(String paramsStr) {
        if (TextUtils.isEmpty(paramsStr)) return;
        HadesUtils.runOnWorkThread(() -> {
            try {
                QtitansContainerParams params = HadesUtils.fromJson(paramsStr, QtitansContainerParams.class);
                if (params == null) return;
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put("container_provider", "local");
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, CONTAINER_T1_REPORT);
                valLabMap.put(ReportParamsKey.CONTAINER.SPLASH_CREATE_TIME, params.getSplashOnCreateTime());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_CREATE_TIME, params.getContainerOnCreateTime());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_HIDE_LOADING_TIME, params.getContainerOnHideLoadingTime());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_SHOW_LOADING_TIME, params.getContainerOnShowLoadingTime());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_START_TIME, params.getContainerPerfStartTime());
                String startupType = getStartType(params.getSplashOnCreateTime(), params.getContainerOnCreateTime(), params.getContainerPerfStartTime());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_START_TYPE, startupType);
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_T1_DURATION, params.getContainerOnHideLoadingTime() - (params.getContainerOnShowLoadingTime() > 0 ? params.getContainerOnShowLoadingTime() : params.getContainerOnCreateTime()));
                long launchTime = getContainerLaunchTime(startupType, params.getJumpScene(), params.getContainerPerfStartTime(), params.getSplashOnCreateTime(), params.getContainerOnCreateTime(), params.getContainerOnHideLoadingTime());
                String lch = HadesUtils.getLchFromTargetUrl(params.getTargetUrl());
                long containerBackgroundTime = getBackGroundStartTime(startupType, params, lch);
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_PUSH_REQUEST_TIME, StorageHelper.getQtitansPushRequestDuration());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_BACKGROUND_TIME, containerBackgroundTime);
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_BACKGROUND_ENTER_TIME, StorageHelper.getQtitansBackgroundTime(lch));
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_LAUNCH_TIME, launchTime);
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_BACKGROUND_STATUS, StorageHelper.getQtitansBackgroundFlag(lch));
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_IS_MAIN_PROCESS_WARM_UP, params.isWarmUpMainProcess());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_RESOURCE_PRELOAD_FLAG, params.isResourceDownload());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_CONTAINER_PRELOAD_FLAG, params.isContainerPreload());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_USER_LOGIN_STATUS, params.isLogin());
                addQtitansContainerParams(valLabMap, params);
                LxReportUtils.pageEvent(CID_CONTAINER_INNER_PV, valLabMap);
                reportLog(valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    public static void reportContainerT0Time(String paramsStr) {
        if (TextUtils.isEmpty(paramsStr)) return;
        HadesUtils.runOnWorkThread(() -> {
            try {
                QtitansContainerParams params = HadesUtils.fromJson(paramsStr, QtitansContainerParams.class);
                if (params == null) {
                    return;
                }
                Map<String, Object> valLabMap = new HashMap<>();
                String lch = HadesUtils.getLchFromTargetUrl(params.getTargetUrl());
                valLabMap.put("container_provider", "local");
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, CONTAINER_T0_REPORT);
                valLabMap.put(ReportParamsKey.CONTAINER.SPLASH_CREATE_TIME, params.getSplashOnCreateTime());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_CREATE_TIME, params.getContainerOnCreateTime());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_SHOW_LOADING_TIME, params.getContainerOnShowLoadingTime());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_START_TIME, params.getContainerPerfStartTime());
                String startupType = getStartType(params.getSplashOnCreateTime(), params.getContainerOnCreateTime(), params.getContainerPerfStartTime());
                long t0Time = getT0Time(startupType, params.getJumpScene(), params.getContainerPerfStartTime(), params.getSplashOnCreateTime(), params.getContainerOnCreateTime(), params.getContainerOnShowLoadingTime());
                long containerBackgroundTime = getBackGroundStartTime(startupType, params, lch);
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_BACKGROUND_ENTER_TIME, StorageHelper.getQtitansBackgroundTime(lch));
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_PUSH_REQUEST_TIME, StorageHelper.getQtitansPushRequestDuration());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_BACKGROUND_TIME, containerBackgroundTime);
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_T0_DURATION, t0Time);
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_START_TYPE, startupType);
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_BACKGROUND_STATUS, StorageHelper.getQtitansBackgroundFlag(lch));
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_IS_MAIN_PROCESS_WARM_UP, params.isWarmUpMainProcess());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_RESOURCE_PRELOAD_FLAG, params.isResourceDownload());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_CONTAINER_PRELOAD_FLAG, params.isContainerPreload());
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PERF_APP_USER_LOGIN_STATUS, params.isLogin());
                addQtitansContainerParams(valLabMap, params);
                LxReportUtils.pageEvent(CID_CONTAINER_INNER_PV, valLabMap);
                reportLog(valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    public static void reportPreRenderStage(String url, String stage, long pushClickTime, boolean isMainProcessAlive, long callLocationTime) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                Map<String, Object> valLabMap = new HashMap<>();
                String lch = HadesUtils.getLchFromTargetUrl(url);
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, PRE_RENDER_CONTAINER_REPORT);
                valLabMap.put(ReportParamsKey.CONTAINER.LCH, lch);
                valLabMap.put(ReportParamsKey.CONTAINER.TARGET_URL, url);
                valLabMap.put(ReportParamsKey.CONTAINER.PRE_RENDER_STAGE, stage);
                valLabMap.put(ReportParamsKey.CONTAINER.PRE_RENDER_PUSH_CLICK_TIME, pushClickTime);
                valLabMap.put(ReportParamsKey.CONTAINER.PRE_RENDER_LOCATION_CALL_TIME, callLocationTime);
                valLabMap.put(ReportParamsKey.CONTAINER.PRE_RENDER_CLICK_EXPOSURE_INTERVAL, System.currentTimeMillis() - pushClickTime);
                valLabMap.put(ReportParamsKey.CONTAINER.PRE_RENDER_MAIN_PROCESS_ALIVE, isMainProcessAlive);
                LxReportUtils.pageEvent(CID_CONTAINER_INNER_PV, valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }

        });
    }

    public static void reportContainerOnStop(String page) {
        long time = System.currentTimeMillis();
        HadesUtils.runOnWorkThread(() -> {
            try {
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, CONTAINER_STOP);
                valLabMap.put(ReportParamsKey.CONTAINER.TIME, time);
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_PAGE_NAME, page);
                LxReportUtils.pageEvent(CID_CONTAINER_INNER_PV, valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    private static long getBackGroundStartTime(String startupType, QtitansContainerParams params, String lch) {
        long t0Start = params.getContainerPerfStartTime() > 0 ? params.getContainerPerfStartTime() : params.getSplashOnCreateTime();
        if (StartTypeEnum.hot.name().equals(startupType)) {
            if (ContainerJumpSceneEnum.WIDGET.name().equals(params.getJumpScene()) || ContainerJumpSceneEnum.SHORTCUT.name().equals(params.getJumpScene())) {
                t0Start = params.getSplashOnCreateTime() > 0 ? params.getSplashOnCreateTime() : params.getContainerOnCreateTime();
            }
        }
        return StorageHelper.getQtitansBackgroundTime(lch) - t0Start;
    }

    private static long getT0Time(String startupType, String jumpScene, long containerPerfStartTime, long splashOnCreateTime, long containerOnCreateTime, long containerOnShowLoadingTime) {
        long t0End = containerOnShowLoadingTime > 0 ? containerOnShowLoadingTime : containerOnCreateTime;
        long t0Start = containerPerfStartTime;
        if (StartTypeEnum.hot.name().equals(startupType)) {
            if (ContainerJumpSceneEnum.WIDGET.name().equals(jumpScene) || ContainerJumpSceneEnum.SHORTCUT.name().equals(jumpScene)) {
                t0Start = splashOnCreateTime > 0 ? splashOnCreateTime : containerOnCreateTime;
            }
        }
        return t0End - t0Start;
    }

    private static long getContainerLaunchTime(String startupType, String jumpScene, long appStartTime, long splashOnCreateTime, long containerOnCreateTime, long containerOnHideLoadingTime) {
        if (StartTypeEnum.hot.name().equals(startupType)) {
            if (ContainerJumpSceneEnum.WIDGET.name().equals(jumpScene) || ContainerJumpSceneEnum.SHORTCUT.name().equals(jumpScene)) {
                if (splashOnCreateTime > 0) {
                    return containerOnHideLoadingTime - splashOnCreateTime;
                } else {
                    return containerOnHideLoadingTime - containerOnCreateTime;
                }
            } else {
                //push 使用点击时间
                return containerOnHideLoadingTime - appStartTime;
            }

        } else if (StartTypeEnum.cold.name().equals(startupType)) {
            return containerOnHideLoadingTime - appStartTime;
        }
        return 0;
    }

    private static String getStartType(long splashOnCreateTime, long containerOnCreateTime, long containerPerfStartTime) {
        if (splashOnCreateTime > 0) {
            return splashOnCreateTime - containerPerfStartTime > 5000 ? StartTypeEnum.hot.name() : StartTypeEnum.cold.name();
        } else {
            if (containerOnCreateTime > 0) {
                return containerOnCreateTime - containerPerfStartTime > 5000 ? StartTypeEnum.hot.name() : StartTypeEnum.cold.name();
            } else {
                return StartTypeEnum.unknown.name();
            }
        }
    }

    public static void reportContainerDestroy(QtitansContainerParams params, long time, long uptime) {
        if (params == null) {
            return;
        }
        long onDestroyTime = System.currentTimeMillis();
        long onDestroyUptime = SystemClock.uptimeMillis();
        HadesUtils.runOnWorkThread(() -> {
            try {
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put("container_provider", "local");
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, CONTAINER_DESTROY);
                valLabMap.put(ReportParamsKey.CONTAINER.TIME, time);
                valLabMap.put(ReportParamsKey.CONTAINER.TIME_DUR, onDestroyTime - time);
                valLabMap.put(ReportParamsKey.CONTAINER.UPTIME, uptime);
                valLabMap.put(ReportParamsKey.CONTAINER.UPTIME_DUR, onDestroyUptime - uptime);
                addQtitansContainerParams(valLabMap, params);
                reportLog(valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    /**
     * 复访去完成-引导弹窗
     */
    public static void reportRepeatVisitDialog(boolean isMC, String checkSource, String businessType, boolean isRecordPopup) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                //灵犀
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_BUSINESS, businessType);
                valLabMap.put(ReportParamsKey.CONTAINER.CONTAINER_SOURCE, checkSource);
                valLabMap.put(ReportParamsKey.CONTAINER.POPUP_TYPE, PopupType.GUIDE_POPUP.getType());
                valLabMap.put("isMC", isMC);
                if (isMC) {
                    LxReportUtils.clickEvent(CID_CONTAINER_INNER_PV, BID_CONTAINER_REVISIT_DIALOG_MC, valLabMap);
                } else {
                    LxReportUtils.viewEvent(CID_CONTAINER_INNER_PV, BID_CONTAINER_REVISIT_DIALOG_MV, valLabMap);
                }
                //上报服务端
                Map<String, Object> queryMap = new HashMap<>();
                queryMap.put("popupType", PopupType.GUIDE_POPUP.getType());
                queryMap.put("isMC", isMC);
                if (!TextUtils.isEmpty(businessType)) {
                    queryMap.put("businessType", businessType);
                }
                if (!TextUtils.isEmpty(checkSource)) {
                    queryMap.put("checkSource", checkSource);
                }
                if (isRecordPopup) {
                    HadesRetrofit.getInstance(HadesUtils.getContext())
                            .recordPopup(queryMap).enqueue(new Callback<BaseResponse<String>>() {
                                @Override
                                public void onResponse(Call<BaseResponse<String>> call, Response<BaseResponse<String>> response) {

                                }

                                @Override
                                public void onFailure(Call<BaseResponse<String>> call, Throwable t) {

                                }
                            });
                }
                reportLog(valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    private static boolean isGameSource(QtitansContainerParams qtitansContainerParams) {
        if (qtitansContainerParams != null
                && qtitansContainerParams.getLoadingViewParams() != null) {
            return qtitansContainerParams.getLoadingViewParams().isGameSource();
        }
        return false;
    }

    private static String getContainerVisitType(QtitansContainerParams qtitansContainerParams) {
        if (qtitansContainerParams != null
                && qtitansContainerParams.getLoadingViewParams() != null
                && !TextUtils.isEmpty(qtitansContainerParams.getLoadingViewParams().getVisitType())) {
            return qtitansContainerParams.getLoadingViewParams().getVisitType();
        }
        return "";
    }

    private static String getContainerFunctionType(QtitansContainerParams qtitansContainerParams) {
        if (qtitansContainerParams != null
                && qtitansContainerParams.getLoadingViewParams() != null
                && !TextUtils.isEmpty(qtitansContainerParams.getLoadingViewParams().getFunctionType())) {
            return qtitansContainerParams.getLoadingViewParams().getFunctionType();
        }
        return "";
    }

    public static String getPushTypeContainer(QtitansContainerParams qtitansContainerParams) {
        if (qtitansContainerParams != null
                && qtitansContainerParams.getLoadingViewParams() != null
                && !TextUtils.isEmpty(qtitansContainerParams.getLoadingViewParams().getPushTypeContainer())) {
            return qtitansContainerParams.getLoadingViewParams().getPushTypeContainer();
        }
        return "";
    }

    public static String getContainerType(QtitansContainerParams qtitansContainerParams) {
        return qtitansContainerParams != null
                && qtitansContainerParams.getContainerType() != null
                ? qtitansContainerParams.getContainerType().name() : ContainerType.UNKNOWN.name();
    }

    public static String getCheckSource(QtitansContainerParams qtitansContainerParams) {
        return qtitansContainerParams != null
                && qtitansContainerParams.getLoadingViewParams() != null
                ? qtitansContainerParams.getLoadingViewParams().getCheckSource() : "";
    }

    public static String getBusinessType(QtitansContainerParams qtitansContainerParams) {
        return qtitansContainerParams != null
                && qtitansContainerParams.getLoadingViewParams() != null
                ? qtitansContainerParams.getLoadingViewParams().getBusinessType() : "";
    }

    public static String getCardStatus(QtitansContainerParams qtitansContainerParams) {
        return qtitansContainerParams != null
                && qtitansContainerParams.getCardStatus() != null
                ? qtitansContainerParams.getCardStatus() : "-1";
    }

    public static String getBrandId(QtitansContainerParams qtitansContainerParams) {
        return qtitansContainerParams != null
                && qtitansContainerParams.getBrandId() != null
                ? qtitansContainerParams.getBrandId() : "";
    }

    /**
     * 容器只上报日志
     *
     * @param custom
     */
    public static void reportLog(Map<String, Object> custom) {
        reportLog("", custom);
    }

    public static void reportLog(String stage, Map<String, Object> custom) {
        if (custom == null) {
            return;
        }
        //重复
        if (BuildConfig.LOG_ENABLE) {
            if (custom.containsKey("channel")
                    || custom.containsKey("source")
                    || custom.containsKey("modelName")
                    || custom.containsKey("eventType")
                    || custom.containsKey(MT_QQ_CONTAINER)) {
                QtitansLog.log("reportLog: 上报日志 关键字重复 ");
            }
        }
        HadesUtils.runOnWorkThread(() -> {
            try {
                if (!TextUtils.isEmpty(stage)) {
                    custom.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, stage);
                }
                custom.put("reportLog", TAG);
                if (BuildConfig.LOG_ENABLE) {
                    QtitansLog.log(custom.toString());
                }
                BabelHelper.log(MT_QQ_CONTAINER, custom);
                Logger.d(MT_QQ_CONTAINER, custom);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    public static void reportVisitDialogLog(boolean isMV, String checkSource, String popupType, String taskStatus, String buttonName) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                HashMap<String, Object> map = new HashMap<>();
                map.put(ReportParamsKey.CONTAINER.CONTAINER_SOURCE, checkSource);
                map.put(ReportParamsKey.CONTAINER.POPUP_TYPE, popupType);
                map.put(ReportParamsKey.CONTAINER.TASK_STATUS, taskStatus);
                map.put(ReportParamsKey.CONTAINER.BUTTON_NAME, buttonName);
                if (isMV) {
                    LxReportUtils.viewEvent(CID_CONTAINER_INNER_PV, BID_CONTAINER_VISIT_REWARD_DIALOG_MV, map);
                } else {
                    LxReportUtils.clickEvent(CID_CONTAINER_INNER_PV, BID_CONTAINER_VISIT_REWARD_DIALOG_MC, map);
                }

            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });

    }

    /**
     * @param isMC        true 点击， false曝光
     * @param checkSource 业务标志
     * @param popupType   0=未安装引导资源位、1=已安装未首访资源位
     * @param source      入口来源："push"、"business“
     */
    public static void reportBottomFloatWinLog(boolean isMC, int checkSource, int popupType, String source) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                HashMap<String, Object> map = new HashMap<>();
                map.put(ReportParamsKey.CONTAINER.CONTAINER_SOURCE, checkSource);
                map.put(ReportParamsKey.CONTAINER.POPUP_TYPE, popupType);
                map.put(ReportParamsKey.CONTAINER.SOURCE, source);
                if (isMC) {
                    LxReportUtils.clickEvent(CID_CONTAINER_INNER_PV, BID_CONTAINER_BOTTOM_FLOAT_MC, map);
                } else {
                    LxReportUtils.viewEvent(CID_CONTAINER_INNER_PV, BID_CONTAINER_BOTTOM_FLOAT_MV, map);
                }
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });

    }

    /**
     * @param checkSource 业务标志
     * @param popupType   0=未安装引导资源位、1=已安装未首访资源位
     * @param source      入口来源："push"、"business“
     */
    public static void reportBottomFloatWinCloseLog(int checkSource, int popupType, String source) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                HashMap<String, Object> map = new HashMap<>();
                map.put(ReportParamsKey.CONTAINER.CONTAINER_SOURCE, checkSource);
                map.put(ReportParamsKey.CONTAINER.POPUP_TYPE, popupType);
                map.put(ReportParamsKey.CONTAINER.SOURCE, source);
                LxReportUtils.clickEvent(CID_CONTAINER_INNER_PV, BID_CONTAINER_BOTTOM_FLOAT_CLOSE_MC, map);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });

    }

    public static void reportVisitCloseDialogLog(String checkSource, String popupType, String taskStatus) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                HashMap<String, Object> map = new HashMap<>();
                map.put(ReportParamsKey.CONTAINER.CONTAINER_SOURCE, checkSource);
                map.put(ReportParamsKey.CONTAINER.POPUP_TYPE, popupType);
                map.put(ReportParamsKey.CONTAINER.TASK_STATUS, taskStatus);
                LxReportUtils.clickEvent(CID_CONTAINER_INNER_PV, BID_CONTAINER_VISIT_CLOSE_MC, map);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });

    }

    public static void logOpenIntent(String tag, Intent intent, String params) {
        if (TextUtils.isEmpty(params)) return;
        QtitansContainerParams containerParams = HadesUtils.fromJson(params, QtitansContainerParams.class);
        logOpenIntent(tag, intent, containerParams);
    }

    public static void logOpenIntent(String tag, Intent intent, QtitansContainerParams params) {
        logOpenIntent(tag, intent, params, "");
    }

    public static void logOpenIntent(String tag, Intent intent, String paramsStr, String errorMsg) {
        if (TextUtils.isEmpty(paramsStr)) return;
        logOpenIntent(tag, intent, HadesUtils.fromJson(paramsStr, QtitansContainerParams.class), errorMsg);
    }

    public static void logOpenIntent(String tag, Intent intent, QtitansContainerParams params, String errorMsg) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                if (params == null) return;
                BabelHelper.log(ReportParamsKey.CONTAINER.QQ_INTENT_CREATE, new HashMap<String, Object>() {
                    {

                        put("tag", tag);
                        put("checkSource", getCheckSourceFromContainerParams(params));
                        put("visitType", getVisitTypeFromContainerParams(params));
                        put("targetUrl", getUrlFromContainerParams(params));
                        put("intentUrl", getUrlFromIntent(intent));
                        put("errorMsg", errorMsg);
                        put("container_provider", "local");
                    }
                });
            } catch (Throwable throwable) {
                QQThrowableReporter.report("ContainerReporter", throwable);
            }
        });
    }

    public static void reportLuckinSaveMoneyPagePv(Context context, QtitansContainerParams containerParams) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                String lch = "";
                String brandId = "";
                String targetUrl = "";
                if (containerParams != null) {
                    targetUrl = containerParams.getTargetUrl();
                    lch = HadesUtils.getLchFromTargetUrl(containerParams.getTargetUrl());
                    brandId = containerParams.getBrandId();
                }
                int fineLocationPermission = ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION);
                int coarseLocationPermission = ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION);
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put("targetUrl", targetUrl);
                valLabMap.put("container_provider", "local");
                valLabMap.put("utm_qq_widget_lch_2025", QtitansLuckinPreRequestManager.getInstance().getUtmWidgetLchVal());
                valLabMap.put("isNative", true);
                valLabMap.put("isLogin", UserCenter.getInstance(context).isLogin());
                valLabMap.put("lch", lch);
                valLabMap.put("brandId", brandId);
                valLabMap.put("locationPermission", fineLocationPermission == PackageManager.PERMISSION_GRANTED ||
                        coarseLocationPermission == PackageManager.PERMISSION_GRANTED);
                valLabMap.put("hasNetwork", DeviceInfo.getNetworkType());
                LxReportUtils.pageEvent(CID_LUCKIN_SAVE_MONEY_PAGE, valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    public static void reportLuckinFragmentPv(String pageName, String targetUrl, boolean isCache) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put("isNative", true);
                valLabMap.put("title", pageName);
                valLabMap.put("container_provider", "local");
                valLabMap.put("isCache", isCache);
                valLabMap.put("targetUrl", targetUrl);
                valLabMap.put("utm_qq_widget_lch_2025", QtitansLuckinPreRequestManager.getInstance().getUtmWidgetLchVal());
                LxReportUtils.viewEvent(CID_LUCKIN_SAVE_MONEY_PAGE, BID_LUCKIN_PAGE_DEAL, valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    public static void reportLuckinClickDealBuy(JSONObject data, String title) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                Map<String, Object> valLabMap = new HashMap<>();
                valLabMap.put("utm_qq_widget_lch_2025", QtitansLuckinPreRequestManager.getInstance().getUtmWidgetLchVal());
                valLabMap.put("type", "flex");
                valLabMap.put("title", title);
                valLabMap.put("container_provider", "local");
                if (data != null) {
                    valLabMap.putAll(JsonHelper.jsonObjectToMap(data));
                }
                LxReportUtils.viewEvent(CID_LUCKIN_SAVE_MONEY_PAGE, BID_LUCKIN_DEAL_CLICK_BUY_STATUS, valLabMap);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    public static void logCreateIntent(String tag, Intent intent, String targetUrl,
                                       String visitType, int checkSource) {
        logCreateIntent(tag, intent, targetUrl, checkSource, visitType, "");
    }

    public static void logCreateIntent(String tag, Intent intent, String targetUrl,
                                       int checkSource, String visitType, String errorMsg) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                BabelHelper.log(ReportParamsKey.CONTAINER.QQ_INTENT_CREATE, new HashMap<String, Object>() {
                    {
                        put("tag", tag);
                        put("checkSource", (checkSource < 0 ? getCheckSourceFromIntent(intent) : checkSource));
                        put("visitType", TextUtils.isEmpty(visitType) ? getVisitTypeFromIntent(intent) : visitType);
                        put("targetUrl", targetUrl);
                        put("intentUrl", getUrlFromIntent(intent));
                        put("errorMsg", errorMsg);
                        put("container_provider", "local");
                    }
                });
            } catch (Throwable throwable) {
                QQThrowableReporter.report("ContainerReporter", throwable);
            }
        });
    }

    public static void reportDeskLog(String tag, Map<String, Object> valLabMap, String checkSource, String businessType) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                Map<String, Object> map = valLabMap == null ? new HashMap<>() : valLabMap;
                map.put("checkSource", checkSource);
                map.put("businessType", businessType);
                map.put("container_provider", "local");
                BabelHelper.log(tag, map);
            } catch (Throwable e) {
                QQThrowableReporter.report("ContainerReporter", e);
            }
        });
    }

    private static String getCheckSourceFromContainerParams(QtitansContainerParams params) {
        if (params == null) {
            return "-1";
        }
        LoadingViewParams loadingViewParams = params.getLoadingViewParams();
        if (loadingViewParams == null) {
            return "-1";
        }
        return loadingViewParams.getCheckSource();
    }

    private static String getUrlFromContainerParams(QtitansContainerParams params) {
        if (params == null) {
            return "";
        }
        return params.getTargetUrl();
    }

    private static String getVisitTypeFromContainerParams(QtitansContainerParams params) {
        if (params == null) {
            return "";
        }
        LoadingViewParams loadingViewParams = params.getLoadingViewParams();
        if (loadingViewParams == null) {
            return "";
        }
        return loadingViewParams.getVisitType();
    }

    private static String getCheckSourceFromIntent(Intent intent) {
        if (intent == null) {
            return "-1";
        }
        String paramsStr = intent.getStringExtra(ContainerConst.Qtitans_CONTAINER_PARAMS);
        QtitansContainerParams qtitansContainerParams = HadesUtils.fromJson(paramsStr, QtitansContainerParams.class);
        if (qtitansContainerParams == null) {
            return "-1";
        }
        LoadingViewParams loadingViewParams = qtitansContainerParams.getLoadingViewParams();
        if (loadingViewParams == null) {
            return "-1";
        }
        return loadingViewParams.getCheckSource();
    }

    private static String getUrlFromIntent(Intent intent) {
        if (intent == null) {
            return "";
        }
        return intent.getDataString();
    }

    private static String getVisitTypeFromIntent(Intent intent) {
        if (intent == null) {
            return "";
        }
        String paramsStr = intent.getStringExtra(ContainerConst.Qtitans_CONTAINER_PARAMS);
        QtitansContainerParams qtitansContainerParams = HadesUtils.fromJson(paramsStr, QtitansContainerParams.class);
        if (qtitansContainerParams == null) {
            return "";
        }
        LoadingViewParams loadingViewParams = qtitansContainerParams.getLoadingViewParams();
        if (loadingViewParams == null) {
            return "";
        }
        return loadingViewParams.getVisitType();
    }
}
