package com.meituan.android.qtitans;

import static com.meituan.android.hades.BaseRouterActivity.EXTRA_KEY_BUSINESS_TYPE;
import static com.meituan.android.hades.BaseRouterActivity.EXTRA_KEY_FW_SOURCE;
import static com.meituan.android.hades.BaseRouterActivity.EXTRA_KEY_RED_INFO;
import static com.meituan.android.hades.BaseRouterActivity.EXTRA_KEY_WIDGET_ENUM;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;

import com.meituan.android.hades.HadesWidgetEnum;
import com.meituan.android.hades.bean.SubscribeType;
import com.meituan.android.hades.dyadater.container.ContainerConst;
import com.meituan.android.hades.dyadater.container.ContainerType;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.report.ReportParamsKey;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.HadesWidgetUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.impl.utils.StorageHelper;
import com.meituan.android.hades.impl.utils.SubscribeLoggerUtils;
import com.meituan.android.hades.dyadater.container.QtitansLoadingPageContent;
import com.meituan.android.hades.dyadater.container.QtitansContainerVisitType;
import com.meituan.android.qtitans.container.reporter.ContainerReporter;
import com.sankuai.meituan.mbc.dsp.core.Dsp;

import java.util.HashMap;
import java.util.Map;

import static com.meituan.android.hades.router.RouterManager.DEFAULT_LCH_WIDGET;
import static com.meituan.android.qtitans.QtitansVideoSplashMinProgramActivity.QTITANS_SPLASH_VIDEO_MIN_PROGRAM_SCHEME_BASE;

import org.json.JSONObject;

public class QTitansVideoSplashActivity extends QTitansSplashActivity {

    public final static String QTITANS_SPLASH_VIDEO_SCHEME_BASE = "imeituan://www.meituan.com/qtitans/splash/video";

    public static Intent obtainWidgetSplashIntent(Context context,
                                                  String targetUrl,
                                                  int checkSource,
                                                  QtitansLoadingPageContent loadingPageContent,
                                                  HadesWidgetEnum widgetEnum,
                                                  String resourceId,
                                                  String redInfo,
                                                  int source,
                                                  @NonNull QtitansContainerVisitType visitType) {
        try {
            if (!canIUse(context, targetUrl)) {
                Logger.d(TAG, "canIUse=false " + "url=" + targetUrl + " from=widget");
                ContainerReporter.logCreateIntent("QTitansVideoSplashActivity obtainWidgetSplashIntent " +
                        "intent null 1", null, targetUrl, visitType.getType(), checkSource);
                return null;
            }

            if (TextUtils.isEmpty(targetUrl)) {
                ContainerReporter.logCreateIntent("QTitansVideoSplashActivity obtainWidgetSplashIntent " +
                        "intent null 2", null, targetUrl, visitType.getType(), checkSource);
                return null;
            }

            if (loadingPageContent == null) {
                return null;
            }

            Uri uri = getSplashUri(targetUrl, resourceId);
            targetUrl = targetUrl + "&" + "enable_new_router=1";
            if (uri == null) {
                ContainerReporter.logCreateIntent("QTitansVideoSplashActivity obtainWidgetSplashIntent " +
                        "intent null 3", null, targetUrl, visitType.getType(), checkSource);
                return null;
            }

            if (StorageHelper.getKeyQcSModel(context)) {
                Uri.Builder dataBuilder = uri.buildUpon();
                dataBuilder.appendQueryParameter("_speed_mode", "1");
                uri = dataBuilder.build();
            }

            Intent targetIntent = new Intent();
            targetIntent.setData(uri);
            targetIntent.putExtra(EXTRA_TARGET, targetUrl);
            int businessType = QtitansContainerVisitType.VisitVideoWidget22.getType().equals(visitType.getType()) ? SubscribeType.FW :  SubscribeType.DESK_APP;
            JSONObject loadingViewParams = obtainLoadingViewParams(String.valueOf(checkSource), String.valueOf(businessType),
                    loadingPageContent, visitType);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("loadingViewParams", loadingViewParams);
            jsonObject.put("targetUrl", targetUrl);
            jsonObject.put("jumpScene", "WIDGET");
            jsonObject.put("sceneCode", widgetEnum.getWidgetNumCode());
            jsonObject.put("resourceId", resourceId);
            jsonObject.put("containerType", ContainerType.containType(uri.getPath()).name());
            targetIntent.putExtra(ContainerConst.Qtitans_CONTAINER_PARAMS, jsonObject.toString());
            targetIntent.putExtra(EXTRA_KEY_RED_INFO, redInfo);
            targetIntent.putExtra(EXTRA_KEY_FW_SOURCE, String.valueOf(source));
            targetIntent.putExtra(EXTRA_KEY_WIDGET_ENUM, widgetEnum.getWidgetNumCode());
            targetIntent.putExtra(Dsp.INTENT_EXTRA_IS_RETURN_HANDLED, true);
            targetIntent.putExtra("_source_from_qq", true); // QQ 冷启链路标识
            if (visitType == QtitansContainerVisitType.VisitVideoWidget22) {
                targetIntent.putExtra(EXTRA_KEY_BUSINESS_TYPE, SubscribeType.FW);
            } else {
                targetIntent.putExtra(EXTRA_KEY_BUSINESS_TYPE, SubscribeType.DESK_APP);
            }
            targetIntent.putExtra(Dsp.INTENT_EXTRA_IS_DSP_COLD_START, true);
            ContainerReporter.logCreateIntent("QTitansVideoSplashActivity obtainWidgetSplashIntent",
                    targetIntent, targetUrl, visitType.getType(), checkSource);
            return targetIntent;
        } catch (Throwable e) {
            ContainerReporter.logCreateIntent("QTitansVideoSplashActivity obtainWidgetSplashIntent intent null 4",
                    null, targetUrl, checkSource, visitType.getType(), Log.getStackTraceString(e));
            QQThrowableReporter.report("QTitansVideoSplashActivity", e);
        }
        return null;
    }

    private static Uri getSplashUri(String targetUrl, String resourceId) {
        Uri uri = Uri.parse(targetUrl);
        if (uri == null) {
            return null;
        }
        String exitType = uri.getQueryParameter("exitType");
        String link = "1".equals(exitType) ? QTITANS_SPLASH_VIDEO_MIN_PROGRAM_SCHEME_BASE :  QTITANS_SPLASH_VIDEO_SCHEME_BASE;
        Uri.Builder dataBuilder = Uri.parse(link).buildUpon();
        String lch = uri.getQueryParameter(QTitansSplashActivity.QUERY_KEY_LCH);
        dataBuilder.appendQueryParameter("_page_new", "1"); //不跳MT小助手闪屏
        if (!TextUtils.isEmpty(lch)) {
            dataBuilder.appendQueryParameter(QUERY_KEY_LCH, lch);
        } else {
            dataBuilder.appendQueryParameter(QUERY_KEY_LCH, DEFAULT_LCH_WIDGET);
            HadesWidgetUtils.reportIllegal(resourceId, ReportParamsKey.ILLEGAL_TYPE.EMPTY_LCH, targetUrl);
        }
        return dataBuilder.build();
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        updateIntent();
        if (getIntent() != null) {
            // 获取从 Intent 中传递过来的目标 URL
            String targetUrl = getIntent().getStringExtra(EXTRA_TARGET);
            if (targetUrl != null && !targetUrl.isEmpty()) {
                // 生成新的查询参数
                String newParam = WIDGET_TRANS_ID + "=" + SubscribeLoggerUtils.productRandom();
                String newUrl = targetUrl + "&" + newParam;
                getIntent().putExtra(EXTRA_TARGET, newUrl);
                Uri uri = Uri.parse(newUrl);
                SubscribeLoggerUtils.getInstance().reportJumpVideoLogger(
                        this,
                        uri.getQueryParameter("channel_source"),
                        getIntent().getIntExtra(EXTRA_KEY_BUSINESS_TYPE, -1) == SubscribeType.FW ? "2" : "1",
                        newUrl,
                        uri.getQueryParameter("kkWidgetLinkType"),
                        uri.getQueryParameter(WIDGET_TRANS_ID),
                        getIntent().getBooleanExtra(Dsp.INTENT_EXTRA_IS_DSP_COLD_START, false) ? "1" : "0");
            }
        }
        super.onCreate(savedInstanceState);
    }

    public void updateIntent() {
        try {
            if (!HadesUtils.enableMtVisitQC()) {
                return;
            }
            Intent intent = getIntent();
            if (intent == null) {
                return;
            }
            String paramsStr = intent.getStringExtra(ContainerConst.Qtitans_CONTAINER_PARAMS);
            if (TextUtils.isEmpty(paramsStr)) return;
            JSONObject qtitansContainerParams = new JSONObject(paramsStr);
            JSONObject loadingViewParams = qtitansContainerParams.getJSONObject("loadingViewParams");
            if ("30001".equals(loadingViewParams.optString("checkSource")) && QtitansContainerVisitType.VisitWidget.getType().equals(loadingViewParams.optString("visitType"))) {
                StorageHelper.setVideoWidgetRect(intent.getSourceBounds());
            }
            if (!TextUtils.isEmpty(intent.getStringExtra(ContainerConst.Qtitans_CONTAINER_PARAMS))) {
                return;
            }
            Uri uri = intent.getData();
            if (uri == null) {
                return;
            }
            String page = uri.getQueryParameter("page");
            if (!"videokk".equals(page)) {
                return;
            }
            Uri.Builder builder = uri.buildUpon();
            builder.path("/msv/home");
            String newUrl = builder.toString();
            intent.putExtra(ContainerConst.Qtitans_CONTAINER_PARAMS, HadesUtils.toJson(constructContainerParamsFromUrl(newUrl)));

            Map<String, Object> log = new HashMap<>();
            log.put("newUrl", newUrl);
            log.put("page", page);
            ContainerReporter.reportLog("VideoSplash", log);
        } catch (Throwable e) {
            QQThrowableReporter.report("QTitansVideoSplashActivity", e);
        }
    }

    public static int count = 0;

    public JSONObject constructContainerParamsFromUrl(String newUrl) {
        JSONObject containerParams = new JSONObject();
        try {
            Uri data = Uri.parse(newUrl);
            String path = data.getPath();
            containerParams.put("targetUrl", newUrl);
            containerParams.put("containerType", ContainerType.containType(path));
            containerParams.put("jumpScene", "MTAPP");
            containerParams.put("time", System.currentTimeMillis());
            containerParams.put("uptime", SystemClock.uptimeMillis());
            JSONObject loadingViewParams = constructLoadingParamsFromUrl(data);
            containerParams.put("loadingViewParams", loadingViewParams);
        } catch (Throwable e) {

        }

        return containerParams;
    }

    public JSONObject constructLoadingParamsFromUrl(Uri data) {
        JSONObject loadingViewParams = new JSONObject();
        try {
            loadingViewParams.put("checkSource", "30001");
            loadingViewParams.put("businessType", "201");
            loadingViewParams.put("useMiniProgram", true);
            loadingViewParams.put("visitType", QtitansContainerVisitType.VisitMTAPP.getType());
        } catch (Throwable e) {

        }
        return loadingViewParams;
    }


}
