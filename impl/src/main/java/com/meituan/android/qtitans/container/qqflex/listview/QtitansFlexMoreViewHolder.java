package com.meituan.android.qtitans.container.qqflex.listview;

import android.support.annotation.NonNull;
import android.support.v7.widget.StaggeredGridLayoutManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.meituan.android.hades.impl.R;
import com.meituan.android.qtitans.container.qqflex.listview.QtitansBaseViewHolder.BaseViewHolder;


public class QtitansFlexMoreViewHolder extends QtitansBaseViewHolder<QtitansFlexMoreViewHolder.MoreViewHolder, QtitansBaseData> {

    @Override
    protected MoreViewHolder createViewHolder(ViewGroup parent, LayoutInflater inflater) {
        View itemView = inflater.inflate(R.layout.qtitans_item_loadmore, parent, false);
        MoreViewHolder holder = new MoreViewHolder(itemView, parent, this);
        return holder;
    }

    @Override
    public void onBindViewHolder(MoreViewHolder viewHolder, QtitansBaseData data, int position) {

    }

    @Override
    public void onViewAttachedToWindow(@NonNull MoreViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        if (holder.itemView != null && holder.itemView.getLayoutParams() instanceof StaggeredGridLayoutManager.LayoutParams) {
            ((StaggeredGridLayoutManager.LayoutParams) holder.itemView.getLayoutParams()).setFullSpan(true);
        }
    }

    @Override
    public void onViewDetachedFromWindow(@NonNull MoreViewHolder holder) {
        super.onViewDetachedFromWindow(holder);
    }

    public static class MoreViewHolder extends BaseViewHolder<QtitansBaseData>{

        private ImageView loadingImg;
        public MoreViewHolder(View itemView, ViewGroup parent, QtitansBaseViewHolder viewHolder) {
            super(itemView, parent, viewHolder);
        }
    }
}
