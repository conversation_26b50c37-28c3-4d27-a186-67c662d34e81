package com.meituan.android.qtitans.container.qqflex.listview;


import static com.meituan.android.qtitans.container.qqflex.listview.ItemViewType.DYNAMIC_FLEX;
import static com.meituan.android.qtitans.container.qqflex.listview.ItemViewType.DYNAMIC_LOAD_MORE;

public class QtitansViewHolderFactory {
    private static final QtitansViewHolderFactory viewHolderFactory = new QtitansViewHolderFactory();

    public static QtitansViewHolderFactory getInstance() {
        return viewHolderFactory;
    }

    public QtitansBaseViewHolder createViewHolderByType(int type) {

        if (type == DYNAMIC_FLEX.getType()) {
            return new QtitansFlexViewHolder();
        } else if (type == DYNAMIC_LOAD_MORE.getType()) {
            return new QtitansFlexMoreViewHolder();
        } else {
            return null;
        }
    }
}
