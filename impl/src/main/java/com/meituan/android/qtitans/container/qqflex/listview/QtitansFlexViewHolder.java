package com.meituan.android.qtitans.container.qqflex.listview;

import android.support.annotation.NonNull;
import android.support.v7.widget.RecyclerView;
import android.support.v7.widget.StaggeredGridLayoutManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.facebook.litho.LithoView;
import com.facebook.litho.LithoViewPools;
import com.meituan.android.dynamiclayout.controller.LayoutController;
import com.meituan.android.dynamiclayout.controller.event.EventListener;
import com.meituan.android.dynamiclayout.utils.CommonUtil;
import com.meituan.android.qbase.BuildConfig;
import java.util.ArrayList;
import java.util.List;
import com.meituan.android.qtitans.container.qqflex.listview.QtitansBaseViewHolder.BaseViewHolder;


public class QtitansFlexViewHolder extends QtitansBaseViewHolder<QtitansFlexViewHolder.DynamicViewHolder, QtitansFlexData> {

    private DynamicViewHolder dynamicViewHolder;
    private QtitansFlexDataHolder dataHolder;
    private LayoutController layoutController;

    public FlexListViewItemClickInterceptor getClickInterceptor() {
        return clickInterceptor;
    }

    public void setClickInterceptor(FlexListViewItemClickInterceptor clickInterceptor) {
        this.clickInterceptor = clickInterceptor;
    }

    public List<EventListener> getListeners() {
        return listeners;
    }

    public void addEventListeners(List<EventListener> listener) {
        if (listener != null && !listener.isEmpty()) {
            listeners.addAll(listener);
        }
    }



    private List<EventListener>  listeners = new ArrayList<>();

    private FlexListViewItemClickInterceptor clickInterceptor;
    @Override
    protected DynamicViewHolder createViewHolder(ViewGroup parent, LayoutInflater inflater) {
        FrameLayout frameLayoutContainer = new FrameLayout(parent.getContext());
        frameLayoutContainer.setLayoutParams(new RecyclerView.LayoutParams(
                RecyclerView.LayoutParams.MATCH_PARENT,
                RecyclerView.LayoutParams.WRAP_CONTENT));
        LithoView lithoView = LithoViewPools.acquire(context);
        frameLayoutContainer.addView(lithoView);
        lithoView.setLayoutParams(new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        dynamicViewHolder = new DynamicViewHolder(frameLayoutContainer, lithoView, parent, this);
        return dynamicViewHolder;
    }

    @Override
    public void onBindViewHolder(DynamicViewHolder viewHolder, QtitansFlexData data, int position) {
        dataHolder = (QtitansFlexDataHolder) data.getDataHolder();
        if (data.jsonObject != null) {
            try {
                data.jsonObject.put("flexItemIndex", position);
            } catch (Throwable e) {
                if (BuildConfig.LOG_ENABLE) {
                    e.printStackTrace();
                }
            }
        }
        layoutController = dataHolder.getLayoutController(context);
        layoutController.setRootView(viewHolder.lithoView);
        layoutController.setReporter(QtitansFlexReporter.getInstance());
        if (clickInterceptor != null) {
            layoutController.addViewClickInterceptor((view, baseTag, s) -> clickInterceptor.handleClick(view, baseTag, s, data.jsonObject));
        }
        if (!listeners.isEmpty()) {
            for(EventListener listener: listeners) {
                layoutController.addEventListener(listener);
            }
        }
        viewHolder.itemView.setTag(layoutController);
        int width = CommonUtil.getDisplayWidth(context);
        viewHolder.lithoView.setComponentTree(null);
        dataHolder.getComponentTree(context, width, componentTree -> {
            viewHolder.lithoView.setComponentTree(componentTree);
        });

    }

    @Override
    public void onViewAttachedToWindow(@NonNull DynamicViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        if (holder.itemView != null && holder.itemView.getLayoutParams() instanceof StaggeredGridLayoutManager.LayoutParams) {
            if (holder.itemView.getParent() instanceof RecyclerView) {
                RecyclerView recyclerView = (RecyclerView) holder.itemView.getParent();
                RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
                if (layoutManager != null) {
                   int position = layoutManager.getPosition(holder.itemView);
                   if (recyclerView.getAdapter() instanceof QtitansFlexListAdapter && ((QtitansFlexListAdapter) recyclerView.getAdapter()).getData().get(position) != null) {
                       QtitansBaseData baseData = ((QtitansFlexListAdapter) recyclerView.getAdapter()).getData().get(position);
                       if (baseData != null && baseData.style != null) {
                           ((StaggeredGridLayoutManager.LayoutParams) holder.itemView.getLayoutParams()).setFullSpan(baseData.style.span == 1);
                       }
                   }
                }

            }
        }
        if (layoutController != null) {
            layoutController.notifyExposureChanged(holder.lithoView);
        }
    }

    public static class DynamicViewHolder extends BaseViewHolder<QtitansBaseData> {
        public LithoView lithoView;
        public DynamicViewHolder(View itemView, LithoView lithoView, ViewGroup parent, QtitansBaseViewHolder baseItemView) {
            super(itemView, parent, baseItemView);
            this.lithoView = lithoView;
        }
    }
}
