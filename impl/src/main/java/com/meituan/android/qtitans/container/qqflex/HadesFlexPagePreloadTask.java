package com.meituan.android.qtitans.container.qqflex;

import static com.meituan.android.qtitans.container.qqflex.FlexPreloadType.DOWNLOAD_AND_PARSE;

import android.net.Uri;
import android.support.annotation.Keep;
import android.text.TextUtils;

import com.meituan.android.hades.AbsPreloadTask;
import com.meituan.android.hades.dyadater.utils.HadesUtilsAdapter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.dyadater.container.PreloadState;
import com.meituan.android.qtitans.container.qqflex.model.QtitansFlexPageData;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@Keep
@ServiceLoaderInterface(interfaceClass = AbsPreloadTask.class, key = HadesFlexPagePreloadTask.KEY)
public class HadesFlexPagePreloadTask extends AbsPreloadTask {
    private static final String TAG = "HadesFlexPagePreloadTask";
    public static final String KEY = "hades_flex_page_preload";
    @Override
    public String getKey() {
        return KEY;
    }

    @Override
    public boolean mainThread() {
        return false;
    }

    @Override
    protected void safeRun() {
        QtitansFlexTemplatePreloader.preloadTemplates(DOWNLOAD_AND_PARSE.getType(), QtitansFlexContainerActivity.class.getName());
        QtitansFlexTemplatePreloader.preCreateFlexView(HadesUtils.getContext(), 7);
        QtitansFlexPageRequestManager.getInstance().setPreloadState(PreloadState.PRELOAD_START);
        if (!TextUtils.isEmpty(QtitansFlexPageRequestManager.getInstance().getTargetUrl())) {
            double[] coordinate = HadesUtilsAdapter.getXy("pt-604734193ad1da2b");
            double lat = 0;
            double lng = 0;
            if (coordinate != null) {
                lat = coordinate[0];
                lng = coordinate[1];
            }
            QtitansFlexPageRequestManager.getInstance().requestSupplyDetailPageData(HadesUtils.getContext(), Uri.parse(QtitansFlexPageRequestManager.getInstance().getTargetUrl()), lat, lng, new IFlexPageRequestCallback() {
                @Override
                public void onSuccess(QtitansFlexPageData data) {
                    QtitansFlexPageRequestManager.getInstance().setPreloadState(PreloadState.PRELOAD_COMPLETE);
                    Logger.d(TAG, "preload data success");
                }

                @Override
                public void onFailed(String message) {
                    Logger.e(TAG, "preload data success");
                    QtitansFlexPageRequestManager.getInstance().setPreloadState(PreloadState.PRELOAD_FAILED);
                }
            });
        }


    }
}
