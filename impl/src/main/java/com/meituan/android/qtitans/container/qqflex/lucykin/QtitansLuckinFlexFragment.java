package com.meituan.android.qtitans.container.qqflex.lucykin;

import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.support.v7.widget.StaggeredGridLayoutManager;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.dynamiclayout.controller.event.EventListener;
import com.meituan.android.hades.impl.R;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.qtitans.container.common.QtitansUtils;
import com.meituan.android.qtitans.container.qqflex.listview.QtitansFlexData;
import com.meituan.android.qtitans.container.qqflex.listview.QtitansFlexItemDecoration;
import com.meituan.android.qtitans.container.qqflex.listview.QtitansFlexListAdapter;
import com.meituan.android.qtitans.container.reporter.ContainerReporter;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;

public class QtitansLuckinFlexFragment extends Fragment {
    public static final String TAG = "QtitansLuckinMainFragment";
    public View mRootVIew;
    private RecyclerView mListIVew;
    private final static int DEFAULT_SPAN = 2;
    private QtitansFlexListAdapter mDynamicAdapter;

    public void setData(List<QtitansFlexData> data) {
        this.data = data;
    }

    protected List<QtitansFlexData> data;
    protected String feedBackgroundColor;
    protected int feedTopRadius ;
    public void setCache(boolean cache) {
        isCache = cache;
    }

    private boolean isCache;

    protected String mTargetUrl;

    public void setTargetUrl(String mTargetUrl) {
        this.mTargetUrl = mTargetUrl;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        reportPV();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mRootVIew = inflater.inflate(R.layout.fragment_flex_luckin_main, container, false);
        mListIVew = mRootVIew.findViewById(R.id.lukin_flex_list);
        updateFeedBackgroundColor(feedBackgroundColor, 0);
       return mRootVIew;

    }

    protected void jumpToBizActivity(String url) {
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            this.startActivity(intent);
        } catch (Throwable e) {
            QQThrowableReporter.report(TAG, e, true);
        }

    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initFlexList();
    }

    protected void reportPV() {
        ContainerReporter.reportLuckinFragmentPv("SKUList", mTargetUrl, isCache);
    }

    private void initFlexList() {
        mDynamicAdapter = new QtitansFlexListAdapter(mListIVew);
        mDynamicAdapter.setEnableLoadMore(false);
        StaggeredGridLayoutManager staggeredGridLayoutManager = new StaggeredGridLayoutManager(DEFAULT_SPAN, LinearLayoutManager.VERTICAL);
        mListIVew.setAdapter(mDynamicAdapter);
        mListIVew.addItemDecoration(new QtitansFlexItemDecoration());
        mListIVew.setLayoutManager(staggeredGridLayoutManager);
        if (data != null) {
            mDynamicAdapter.setData(data);
        }
    }

    public void updateItemData(int position, HashMap<String, Object> map) {
        if (mDynamicAdapter != null && mDynamicAdapter.getData() != null) {

            HadesUtils.runOnMainThread(() -> {
                try {
                    QtitansFlexData data = (QtitansFlexData) mDynamicAdapter.getData().get(position);
                    if (data == null || data.jsonObject == null) return;
                    if (map != null) {
                        for (String key : map.keySet()) {
                            data.jsonObject.put(key, map.get(key));
                        }
                    }
                    QtitansFlexData newData = new QtitansFlexData(data.jsonObject);
                    mDynamicAdapter.getData().set(position, newData);
                    mDynamicAdapter.notifyItemChanged(position);
                } catch (Throwable e) {
                    QQThrowableReporter.report(TAG, e, true);
                }
            });
        }
    }

    public void updateListData(List<QtitansFlexData> data) {
        HadesUtils.runOnMainThread(()->{
            this.data = data;
            if (data != null && mDynamicAdapter != null) {
                mDynamicAdapter.setData(data);
            }
        });
    }
    public void updateShopTitle(String shopName) {
        if (mDynamicAdapter != null && mDynamicAdapter.getData().size() > 0 && mDynamicAdapter.getData().get(0) instanceof QtitansFlexData) {
            try {
                JSONObject jsonObject =((QtitansFlexData) mDynamicAdapter.getData().get(0)).jsonObject;
                QtitansFlexData data = new QtitansFlexData (jsonObject);
                data.jsonObject.put("shopName", shopName);
                this.data.set(0, data);
                mDynamicAdapter.getData().set(0, data);
                mDynamicAdapter.notifyItemChanged(0);
            } catch (Throwable e) {

            }

        }
    }

    public void updateFeedBackgroundColor(String color, int radius) {
        if (TextUtils.isEmpty(color)) {
            this.feedBackgroundColor = "#ffffff";
        } else {
            this.feedBackgroundColor = color;
        }
       if (radius <= 0) {
           this.feedTopRadius = 3;
       } else {
           this.feedTopRadius = radius;
       }

        if (mListIVew!= null) {
            try {
                int radiusInt = QtitansUtils.dip2px(this.getContext(), feedTopRadius);
                GradientDrawable gradientDrawable = new GradientDrawable();
                radiusInt = QtitansUtils.dip2px(this.getContext(), radiusInt);
                float[] radii = new float[]{
                        radiusInt, radiusInt,   // 左上角圆角半径
                        radiusInt, radiusInt,   // 右上角圆角半径
                        0f, 0f,     // 右下角圆角半径
                        0f, 0f      // 左下角圆角半径
                };
                gradientDrawable.setCornerRadii(radii);
                if (!TextUtils.isEmpty(color)) {
                    gradientDrawable.setColor(Color.parseColor(color));
                }
                mListIVew.setBackground(gradientDrawable);
            } catch (Throwable e) {
                QQThrowableReporter.report(TAG, e, true);
            }


        }
    }
    public void addEvent(EventListener eventListener) {
        if (mDynamicAdapter != null) {
            mDynamicAdapter.addEventListener(eventListener);
        }
    }

}
