package com.meituan.android.qtitans;

import com.meituan.android.hades.dyadater.container.interfaces.IContainerConfig;
import com.meituan.android.hades.dyadater.container.interfaces.IContainerProvider;
import com.meituan.android.hades.dyadater.container.interfaces.IContainerService;
import com.meituan.android.hades.dyadater.container.interfaces.IQtitansContainerDelegate;
import com.meituan.android.hades.dyadater.container.interfaces.IQtitansNativeConfigStrategy;
import com.meituan.android.qtitans.container.QtitansContainerManager;
import com.meituan.android.qtitans.container.config.ContainerConfigManager;
import com.meituan.android.qtitans.container.nativ.QtitansNativeConfigStrategy;

import java.lang.ref.WeakReference;
/**
 * 本地容器实现类
 *
 */
public class LocalContainerProviderImpl implements IContainerProvider {
    private static volatile LocalContainerProviderImpl sInstance;
    private LocalContainerProviderImpl() {
        // 私有构造函数
    }
    public static LocalContainerProviderImpl getInstance() {
        if (sInstance == null) {
            synchronized (LocalContainerProviderImpl.class) {
                if (sInstance == null) {
                    sInstance = new LocalContainerProviderImpl();
                }
            }
        }
        return sInstance;
    }
    @Override
    public IContainerService getContainerService() {
        return QtitansContainerManager.getInstance();
    }
    @Override
    public IContainerConfig getContainerConfig() {
        return ContainerConfigManager.getInstance();
    }
    @Override
    public IQtitansNativeConfigStrategy getQtitansNativeConfigStrategy() {
        return new QtitansNativeConfigStrategy();
    }

    @Override
    public IQtitansContainerDelegate getContainerDelegate(String scene) {
        if ("luckin".equals(scene)) {
            return new QtitansLuckinContainerDelegateImpl();
        } else {
            return new QtitansContainerDelegateImpl();
        }
    }

    @Override
    public void reset() {
        sInstance = null;
        ContainerConfigManager.getInstance().clear("");
        QtitansContainerManager.getInstance().clear();
    }
}