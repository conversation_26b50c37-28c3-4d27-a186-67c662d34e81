package com.meituan.android.qtitans.container.qqflex.lucykin.model;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;

import java.lang.reflect.Type;

public class LuckinQuerySpuDetail implements JsonDeserializer<LuckinQuerySpuDetail> {
    public boolean isSoldOut = false;

    @Override
    public LuckinQuerySpuDetail deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        JsonObject rootJsonObject;
        if (json == null || (rootJsonObject = json.getAsJsonObject())== null) return null;
        LuckinQuerySpuDetail luckinQuerySpuDetail = new LuckinQuerySpuDetail();
        if (rootJsonObject.has("soldOut")) {
            luckinQuerySpuDetail.isSoldOut = rootJsonObject.get("soldOut").getAsBoolean();
        }
        return luckinQuerySpuDetail;
    }
}
