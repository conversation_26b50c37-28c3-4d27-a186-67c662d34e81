package com.meituan.android.qtitans;

import com.meituan.android.hades.dyadater.container.ContainerServiceFactory;
import com.meituan.android.hades.dyadater.container.adapter.ContainerAdapterManager;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.pin.dydx.IDexCallBack;
import com.meituan.android.walmai.dex.DexMgr;

import java.util.HashMap;
import java.util.Map;

public class DexContainerEnterHelper {
    private static final String TAG = "DexContainerEnterHelper";
    public static final String dexName = "dexcontainer";
    public static final String CNAME = "]\\<ZaQ/JYI>;5Y<:\\R<:]H@JaM=&]T>:\\R<ZaRA*)M?J9V/H5]5:]X@J)R<Z8|BW";

    public static void preloadDexContainer() {
        DexMgr.getInstance().preload(HadesUtils.getContext(), dexName, true, new IDexCallBack() {
            @Override
            public void onSuccess(Object res) {
                Logger.d(TAG, "preloadDexContainer onSuccess");
            }

            @Override
            public void onFail(int code, String msg) {
                Logger.d(TAG, "preloadDexContainer onFail" + msg);
            }
        });
    }

    public static void enterDexContainer() {
        try {
            HashMap<String, Object> map = new HashMap<>();
            map.put("cName", CNAME);
            map.put("dexName", dexName);
            map.put("bizType", "container_impl");
            Logger.d(TAG, "enterDexContainer start");
            ContainerAdapterManager.reportContainerReasonDexBabel("start load dex");
            long start = System.currentTimeMillis();
            DexMgr.getInstance().dynamicFunExecutor(dexName, map, dexName, true, true, new IDexCallBack() {
                @Override
                public void onSuccess(Object res) {
                    long costTime = System.currentTimeMillis() - start;
                    Logger.d(TAG, "enterDexContainer onSuccess, time"+costTime);
                    ContainerAdapterManager.reportContainerDexBabel(start, costTime);
                    ContainerAdapterManager.reportContainerReasonDexBabel("dex load success");

                }

                @Override
                public void onFail(int code, String msg) {
                    Logger.d(TAG, "enterDexContainer onFail" + msg);
                    ContainerServiceFactory.getInstance().notifyProviderFailed(msg);
                    ContainerAdapterManager.reportContainerReasonDexBabel(msg);
                }
            });

        } catch (Exception e) {
            Logger.d(TAG, "enterDexContainer failed");
        }
    }



}
