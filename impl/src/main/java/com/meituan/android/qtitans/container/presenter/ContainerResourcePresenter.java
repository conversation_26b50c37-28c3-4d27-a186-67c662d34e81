package com.meituan.android.qtitans.container.presenter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import com.meituan.android.hades.BuildConfig;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.report.ReportParamsKey;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.qtitans.container.bean.ContainerResourceData;
import com.meituan.android.hades.dyadater.container.interfaces.IContainerView;
import com.meituan.android.qtitans.container.model.ContainerResourceDataImpl;
import com.meituan.android.qtitans.container.reporter.ContainerReporter;

import java.util.HashMap;
import java.util.Map;

public class ContainerResourcePresenter implements IContainerResource, IPresenter<IContainerView> {
    private static final String TAG = "ContainerResourcePresenter";
    ContainerResourceDataImpl mContainerResourceDataImpl;
    ContainerResourceData mResourceData;
    IContainerView mContainerView;
    private Activity mActivity;
    private IContainerResource mListener;

    public void setPresenterAction(PresenterAction mPresenterAction) {
        this.mPresenterAction = mPresenterAction;
    }

    PresenterAction mPresenterAction;

    public ContainerResourcePresenter(Activity activity, IContainerResource listener) {
        mActivity = activity;
        mListener = listener;
        mContainerResourceDataImpl = new ContainerResourceDataImpl(this);
    }

    public void getContainerResources(Context context, String businessType,
                                      String checkSource, String visitType, PresenterAction action) {
        if (BuildConfig.LOG_ENABLE) {
            Logger.d(TAG, "getContainerResources: businessType = " +
                    businessType + " checkSource =" + checkSource + " PresenterAction =" + action);
        }
        if (TextUtils.isEmpty(checkSource) || TextUtils.isEmpty(businessType)) {
            onGetResourceFail();
            return;
        }
        mPresenterAction = action;
        mContainerResourceDataImpl.getContainerResources(context, businessType, checkSource, visitType);
    }

    @Override
    public void onGetResourceSuccess(ContainerResourceData data) {
        try {
            mResourceData = data;
            if (mListener != null) {
                mListener.onGetResourceSuccess(data);
            }
            if (isViewAttached()) {
                switch (mPresenterAction) {
                    case HIDE_LOADING:
                        mContainerView.onAfterHideLoadingViewRequest();
                        break;
                    default:

                }
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("ContainerResourcePresenter", e);
        }
    }

    @Override
    public void onGetResourceFail() {
        Map<String, Object> custom = new HashMap<>();
        custom.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "onGetResourceFail");
        custom.put("mPresenterAction", mPresenterAction == null ? "" : mPresenterAction.name());
        ContainerReporter.reportLog(custom);
    }

    @Override
    public void attachView(IContainerView view) {
        mContainerView = view;
    }

    @Override
    public void detachView() {
        mContainerView = null;
    }

    @Override
    public boolean isViewAttached() {
        return mContainerView != null;
    }
}
