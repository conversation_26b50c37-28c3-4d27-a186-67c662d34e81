package com.meituan.android.qtitans.container.qqflex.listview;

import android.support.annotation.NonNull;

import com.meituan.android.dynamiclayout.controller.presenter.TemplateData;
import com.meituan.android.hades.impl.BuildConfig;
import com.meituan.android.hades.impl.utils.Logger;
import com.sankuai.litho.recycler.DataHolder;
import com.sankuai.litho.recycler.DataHolderGetter;
import com.sankuai.litho.recycler.TemplateDataGatter;

import org.json.JSONObject;

import java.util.ArrayList;

public class QtitansFlexData extends QtitansBaseData implements TemplateDataGatter, DataHolderGetter<QtitansFlexData> {

    private static final String TAG = "QtitansDynamicData";
   private QtitansFlexDataHolder dataHolder;
    public QtitansFlexData(JSONObject jsonObject) {
        this.jsonObject = jsonObject;
        viewType = ItemViewType.DYNAMIC_FLEX;
        parseStyle();
    }

    private void parseStyle() {
        try {
            if (jsonObject != null) {
                this.templateName = jsonObject.getString("templateName");
                this.templateUrl = jsonObject.getString("templateUrl");
                ItemStyle itemStyle = new ItemStyle();
                if (jsonObject.has("style")) {
                    JSONObject styleJson = jsonObject.getJSONObject("style");
                    itemStyle.bottom = styleJson.getInt("bottom");
                    itemStyle.left = styleJson.getInt("left");
                    itemStyle.right = styleJson.getInt("right");
                    itemStyle.top = styleJson.getInt("top");
                    itemStyle.span = styleJson.getInt("span");
                    this.style = itemStyle;
                }

            }
        } catch (Throwable e) {
            if (BuildConfig.LOG_ENABLE) {
                Logger.e(TAG,"parse style fialed:" + e.getMessage());
            }

        }

    }

    @NonNull
    @Override
    public DataHolder<QtitansFlexData> getDataHolder() {
        if (this.dataHolder == null) {
            this.dataHolder = new QtitansFlexDataHolder(this);
        }
        return this.dataHolder;
    }

    @NonNull
    @Override
    public TemplateData getTemplateData() {
         TemplateData templateData = new TemplateData();
        templateData.templates = new ArrayList<>(1);
        templateData.templates.add(templateUrl);
        templateData.jsonData = jsonObject;
        return templateData;
    }
}
