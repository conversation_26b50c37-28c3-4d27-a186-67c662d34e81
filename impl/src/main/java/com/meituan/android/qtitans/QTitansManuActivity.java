package com.meituan.android.qtitans;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v4.app.FragmentActivity;
import android.text.TextUtils;

import com.meituan.android.hades.delivery.DeliveryEnterHelper;
import com.meituan.android.hades.dyadater.desk.DeliveryDataManager;
import com.meituan.android.hades.dyadater.desk.DeskResourceData;
import com.meituan.android.hades.dyadater.desk.DeskSceneEnum;
import com.meituan.android.hades.dyadater.desk.DeskSourceEnum;
import com.meituan.android.hades.dyadater.desk.PushProcessParams;
import com.meituan.android.hades.impl.ad.HadesAdReporter;
import com.meituan.android.hades.impl.report.BabelHelper;
import com.meituan.android.hades.impl.report.PushReporter;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.router.RouterManager;
import com.sankuai.meituan.mbc.dsp.core.Dsp;

import java.util.HashMap;

public class QTitansManuActivity extends FragmentActivity {
    private static final String TAG = "QTitansManuActivity";
    private static String sTargetUrl;

    public static void setTargetUrl(String url) {
        sTargetUrl = url;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Logger.d(TAG, "onCreate");
        Intent intent = getIntent();
        if (TextUtils.isEmpty(sTargetUrl)) {
            try {
                Bundle bundle = new Bundle();
                bundle.putInt("source", 2);
                bundle.putParcelable("intent", intent);
                DeliveryEnterHelper.deliveryStart(getApplicationContext(),
                        new PushProcessParams.Builder()
                                .setSource(DeskSourceEnum.MANU)
                                .setScene(DeskSceneEnum.MANU_ACT.getMessage())
                                .setPushTime(DeskResourceData.PUSH_TIMING_FENCE)
                                .setStartProcess(true)
                                .setStartProcessScene(true)
                                .setCanUseDex(DeliveryEnterHelper.useDex())
                                .build(), bundle);
            } catch (Throwable throwable) {
                throwable.printStackTrace();
            }
            finish();
            return;
        }
        reportStartManu(sTargetUrl, 1, "open");
        PushReporter.reportPushExposure(getApplicationContext(), HadesAdReporter.STAGE_EXPOSURE, DeskSourceEnum.XMI, DeliveryDataManager.getDeskResource(), "");
        startTargetUrl();
        finish();
    }

    private void startTargetUrl() {
        try {
            boolean hasPrivacy = HadesUtils.hasPrivacy();
            if (hasPrivacy && !TextUtils.isEmpty(sTargetUrl)) {
                Uri targetUri = Uri.parse(sTargetUrl);
                Intent targetIntent = new Intent(Intent.ACTION_VIEW, targetUri);
                targetIntent.putExtra(Dsp.INTENT_EXTRA_IS_DSP_COLD_START, true);
                targetIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                RouterManager.appendLightBoxParamsIfNeed(targetIntent);
                HadesUtils.getContext().startActivity(targetIntent);
            }
        } catch (Throwable e) {
            Logger.d(TAG, "err: " + e.getMessage());
            QQThrowableReporter.report("QTitansManuActivity", e);
        }
    }

    public static void reportStartManu(String url, int code, String message) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("url", url);
        map.put("code", code);
        map.put("msg", message);
        BabelHelper.log("qq_start_manu", map);
    }

    @Override
    protected void onPause() {
        super.onPause();
        handleDsp();
    }

    @Override
    public void finish() {
        sTargetUrl = null;
        handleDsp();
        Dsp.activityReturn(this);
        super.finish();
    }

    private void handleDsp() {
        Intent intent = getIntent();
        if (intent != null) {
            intent.putExtra(Dsp.INTENT_EXTRA_IS_RETURN_HANDLED, true);
        }
    }
}
