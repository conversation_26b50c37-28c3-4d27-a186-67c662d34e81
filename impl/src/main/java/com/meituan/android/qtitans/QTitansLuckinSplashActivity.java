package com.meituan.android.qtitans;

import static com.meituan.android.hades.BaseRouterActivity.EXTRA_KEY_BUSINESS_TYPE;
import static com.meituan.android.hades.BaseRouterActivity.EXTRA_KEY_FW_SOURCE;
import static com.meituan.android.hades.BaseRouterActivity.EXTRA_KEY_RED_INFO;
import static com.meituan.android.hades.BaseRouterActivity.EXTRA_KEY_WIDGET_ENUM;
import static com.meituan.android.hades.router.RouterManager.DEFAULT_LCH_WIDGET;
import static com.meituan.android.hades.router.RouterManager.EXTRA_HADES_ROUTER_JUMP;
import static com.meituan.android.qtitans.container.qqflex.FlexPreloadType.DOWNLOAD_AND_PARSE;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.hades.HadesWidgetEnum;
import com.meituan.android.hades.bean.SubscribeType;
import com.meituan.android.hades.dyadater.container.ContainerConst;
import com.meituan.android.hades.dyadater.container.PreloadState;
import com.meituan.android.hades.dyadater.container.QtitansContainerVisitType;
import com.meituan.android.hades.dyadater.container.QtitansLoadingPageContent;
import com.meituan.android.hades.dyadater.desk.DeskResourceData;
import com.meituan.android.hades.dyadater.desk.DeskSourceEnum;
import com.meituan.android.hades.impl.model.DeskAppResourceData;
import com.meituan.android.hades.impl.report.PushReporter;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.report.ReportParamsKey;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.HadesWidgetUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.router.RouterManager;
import com.meituan.android.hades.router.SkyEyes;
import com.meituan.android.hades.router.tt.TTRedirectHandler;
import com.meituan.android.qtitans.container.qqflex.QtitansFlexTemplatePreloader;
import com.meituan.android.qtitans.container.qqflex.lucykin.QtitansLuckinPreRequestManager;
import com.meituan.android.walmai.widget.AbsCoopFeatureWidget;
import com.sankuai.meituan.arbiter.hook.ArbiterHook;
import com.sankuai.meituan.mbc.dsp.core.CurrentActivityUtil;
import com.sankuai.meituan.mbc.dsp.core.Dsp;

import org.json.JSONObject;


public class QTitansLuckinSplashActivity extends QTitansSplashActivity {
    public final static String QTITANS_SPLASH_LUCKIN_MIN_PROGRAM_SCHEME_BASE = "imeituan://www.meituan.com/qtitans/splash/luckin/min/program";
    public final static String EXTRA_KEY_LUCKIN_SOURCE = "extra_from_luckin_source";
    public final static String EXTRA_KEY_LUCKIN_REOPEN = "EXTRA_KEY_LUCKIN_PAGE_REOPEN";

    public static Intent obtainIntent(Context context,
                               String targetUrl,
                               int checkSource,
                               QtitansLoadingPageContent loadingPageContent,
                               HadesWidgetEnum widgetEnum,
                               String resourceId,
                               String cardStatus,
                               String brandId,
                               int source,
                               @NonNull QtitansContainerVisitType visitType) {
        if (loadingPageContent == null) {
            return null;
        }
        if (TextUtils.isEmpty(targetUrl)) {
            return null;
        }
        Uri uri = getSplashUri(targetUrl, resourceId);
        if (uri == null) {
            return null;
        }
        Intent targetIntent = new Intent();
        targetIntent.setData(uri);
        targetIntent.putExtra(EXTRA_TARGET, targetUrl);
        targetIntent.putExtra(EXTRA_FROM, FROM_WIDGET);
        JSONObject loadingViewParams =
                obtainLoadingViewParams(String.valueOf(checkSource), String.valueOf(SubscribeType.FW),
                loadingPageContent, visitType);
        JSONObject containerJsonObject = new JSONObject();
        try {
            containerJsonObject.put("loadingViewParams", loadingViewParams);
            containerJsonObject.put("targetUrl", targetUrl);
            containerJsonObject.put("jumpScene", FROM_WIDGET);
            containerJsonObject.put("sceneCode", widgetEnum.getWidgetNumCode());
            containerJsonObject.put("resourceId", resourceId);
            containerJsonObject.put("cardStatus", cardStatus);
            containerJsonObject.put("brandId", brandId);

        } catch (Throwable e) {

        }
        targetIntent.putExtra(ContainerConst.Qtitans_CONTAINER_PARAMS, containerJsonObject.toString());
        targetIntent.putExtra(EXTRA_KEY_RED_INFO, "");
        targetIntent.putExtra(EXTRA_KEY_FW_SOURCE, String.valueOf(source));
        targetIntent.putExtra(EXTRA_KEY_WIDGET_ENUM, widgetEnum.getWidgetNumCode());
        targetIntent.putExtra(EXTRA_KEY_BUSINESS_TYPE, SubscribeType.FW);
        targetIntent.putExtra(EXTRA_KEY_LUCKIN_SOURCE, true);
        targetIntent.putExtra(EXTRA_HADES_ROUTER_JUMP, true);
        targetIntent.putExtra(Dsp.INTENT_EXTRA_IS_RETURN_HANDLED, true); //返回不需要 dsp 处理
        targetIntent.putExtra("_source_from_qq", true); // QQ 冷启链路标识
        QTitansSplashActivity.updateWidgetAlias(context, targetIntent, containerJsonObject);
        return targetIntent;
    }

    public static Intent obtainPushSplashIntent(Context context,
                                                DeskSourceEnum deskSourceEnum,
                                                String targetUrl,
                                                DeskResourceData deskResourceData) {
        try {
            if (!canIUse(context, targetUrl)) {
                Logger.d(TAG, "canIUse=false " + "url=" + targetUrl + " from=push");
                return null;
            }

            if (TextUtils.isEmpty(targetUrl) || deskResourceData == null || deskSourceEnum == null) {
                return null;
            }

            if (deskResourceData.loadingPageContent == null) {
                return null;
            }

            Uri uri = Uri.parse(targetUrl);
            if (uri == null) {
                return null;
            }
            Intent routerIntent = new Intent(Intent.ACTION_VIEW);
            JSONObject params = new JSONObject();
            JSONObject loadingViewParams = new JSONObject();
            loadingViewParams.put("checkSource", String.valueOf(deskResourceData.checkSource));
            loadingViewParams.put("businessType", String.valueOf(SubscribeType.FW));
            loadingViewParams.put("iconName", deskResourceData.loadingPageContent.iconName);
            loadingViewParams.put("iconUrl", deskResourceData.loadingPageContent.icon);
            loadingViewParams.put("bottomText", deskResourceData.loadingPageContent.bottomText);
            loadingViewParams.put("useMiniProgram", deskResourceData.loadingPageContent.useMiniProgram);
            loadingViewParams.put("useScreenPage", deskResourceData.loadingPageContent.useScreenPage);
            loadingViewParams.put("visitType", QtitansContainerVisitType.WidgetExternalLink.getType());
            params.put("loadingViewParams", loadingViewParams);
            params.put("jumpScene", FROM_PUSH);
            params.put("source", deskSourceEnum.name());
            params.put("sceneCode", deskSourceEnum.getCode());
            params.put("resourceId", deskResourceData.resourceId);
            params.put("scene", deskResourceData.scene);
            params.put("sessionId", deskResourceData.sessionId);
            params.put("pushType", deskResourceData.pushType);
            params.put("popupType", deskResourceData.popupType);
            params.put("remindMode", PushReporter.getRemindMode(deskResourceData));
            params.put("awType", PushReporter.getAwType(deskResourceData));
            params.put("targetUrl", targetUrl);
            params.put("containerPerfStartTime", deskResourceData.pushClickElapsedTime);
            routerIntent.putExtra(EXTRA_TARGET, targetUrl);
            routerIntent.putExtra(EXTRA_FROM, FROM_PUSH);
            routerIntent.putExtra(ContainerConst.Qtitans_CONTAINER_PARAMS, HadesUtils.toJson(params));
            routerIntent.putExtra(EXTRA_QTITANS_DESK_RESOURCE_DATA, HadesUtils.toJson(deskResourceData));
            routerIntent.putExtra(EXTRA_DESK_ENUM_CODE, deskSourceEnum.getCode());
            Uri targetUri = getSplashUri(targetUrl, deskResourceData.resourceId);
            if (targetUri == null) {
                return null;
            }
            routerIntent.setData(targetUri);

            routerIntent.putExtra(EXTRA_KEY_LUCKIN_SOURCE, true);
            routerIntent.putExtra(EXTRA_HADES_ROUTER_JUMP, true);
            routerIntent.putExtra(Dsp.INTENT_EXTRA_IS_RETURN_HANDLED, true); //返回不需要 dsp 处理
            routerIntent.putExtra("_source_from_qq", true); // QQ 冷启链路标识
            return routerIntent;
        } catch (Throwable e) {
            QQThrowableReporter.report("QTitansLuckinSplashActivity", e);
        }
        return null;
    }

    private static Uri getSplashUri(String targetUrl, String resourceId) {
        Uri uri = Uri.parse(targetUrl);
        if (uri == null) {
            return null;
        }
        Uri.Builder dataBuilder = Uri.parse(QTITANS_SPLASH_LUCKIN_MIN_PROGRAM_SCHEME_BASE).buildUpon();
        String lch = uri.getQueryParameter(QUERY_KEY_LCH);
        dataBuilder.appendQueryParameter("_page_new", "1"); //动线加速
        dataBuilder.appendQueryParameter("_speed_mode", "1");
        if (!TextUtils.isEmpty(lch)) {
            dataBuilder.appendQueryParameter(QUERY_KEY_LCH, lch);
        } else {
            dataBuilder.appendQueryParameter(QUERY_KEY_LCH, DEFAULT_LCH_WIDGET);
            HadesWidgetUtils.reportIllegal(resourceId, ReportParamsKey.ILLEGAL_TYPE.EMPTY_LCH, targetUrl);
        }
        return dataBuilder.build();
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        try {
            Intent intent = getIntent();
            if (intent.getData() != null && intent.getData() != null) {
                Uri uri = intent.getData();
                String harmonyUrl = uri.getQueryParameter("harmonyUrl");
                if (!TextUtils.isEmpty(harmonyUrl)) { // 适配鸿蒙卡入口
                    if (CurrentActivityUtil.isContainsTargetActivity(QTitansLuckinContainerActivity.class.getName())) {
                        //鸿蒙服务卡片，已打开省钱卡页面后退后台重新点击桌面icon跳h5页面修复
                        intent.putExtra(EXTRA_KEY_LUCKIN_REOPEN, true);
                        super.onCreate(savedInstanceState);
                        return;
                    }

                    harmonyUrl = RouterManager.handleSSRUrlIfNeed(harmonyUrl);
                    String cardStatus = uri.getQueryParameter("cardStatus");
                    String brandId = uri.getQueryParameter("brandId");
                    String resourceId = uri.getQueryParameter("resourceId");
                    int widgetNumCode = -1;
                    try {
                        widgetNumCode = Integer.parseInt(uri.getQueryParameter("widgetNumCode"));
                    } catch (Throwable e) {
                        QQThrowableReporter.report("QTitansLuckinSplashActivity", e);
                    }
                    DeskAppResourceData appResourceData = AbsCoopFeatureWidget.getDefaultDeskAppResourceData(harmonyUrl);
                    JSONObject loadingViewParams =
                            obtainLoadingViewParams(String.valueOf(appResourceData.checkSource), String.valueOf(SubscribeType.FW),
                                    appResourceData.loadingPageContent, QtitansContainerVisitType.VisitWidget);
                    JSONObject containerParams = new JSONObject();
                    containerParams.put("targetUrl", harmonyUrl);
                    containerParams.put("jumpScene", FROM_WIDGET);
                    containerParams.put("sceneCode", widgetNumCode);
                    containerParams.put("resourceId", resourceId);
                    containerParams.put("cardStatus", cardStatus);
                    containerParams.put("brandId", brandId);
                    containerParams.put("loadingViewParams", loadingViewParams);
                    intent.putExtra(EXTRA_TARGET, harmonyUrl);
                    intent.putExtra(ContainerConst.Qtitans_CONTAINER_PARAMS, containerParams.toString());
                    intent.putExtra(EXTRA_KEY_BUSINESS_TYPE, SubscribeType.FW);
                    intent.putExtra(EXTRA_KEY_LUCKIN_SOURCE, true);
                    intent.putExtra(EXTRA_HADES_ROUTER_JUMP, true);
                    intent.putExtra(Dsp.INTENT_EXTRA_IS_RETURN_HANDLED, true); //返回不需要 dsp 处理
                    intent.putExtra("_source_from_qq", true); // QQ 冷启链路标识
                }
            }
            String targetUrl = intent.getStringExtra(EXTRA_TARGET);
            if (HadesUtils.isEnableLuckinNativePage()) {
                QtitansLuckinPreRequestManager.getInstance().readLuckinPageCacheData();
                QtitansFlexTemplatePreloader.preloadSaveMoneyTemplates(DOWNLOAD_AND_PARSE.getType(), "luckinsavemoney");
                QtitansLuckinPreRequestManager.getInstance().setPreloadState(PreloadState.PRELOAD_START);
                QtitansLuckinPreRequestManager.getInstance().requestSaveMoneyDealQuery(this, targetUrl, null);
            }
            //跳转前，注册 start activity hook
            registerStartActivityHook();
            try {
                //特团业务
                TTRedirectHandler.registerIfNeed(targetUrl, null, intent.getBooleanExtra(QTitansSplashActivity.EXTRA_FROM, false));
            } catch (Throwable t) {
                QQThrowableReporter.report("QTitansLuckinSplashActivity", t);
            }
        } catch (Throwable t) {
            finishSelf("onCreate error");
            QQThrowableReporter.report("QTitansLuckinSplashActivity", t);
        }
        super.onCreate(savedInstanceState);
    }

    private void registerStartActivityHook() {
        try {
            if (HadesUtils.enableAddRouterHook()) {
                if (!ArbiterHook.isExistsInstrumentation(SkyEyes.class, false)) {
                    ArbiterHook.addMTInstrumentation(new SkyEyes());
                }
            }
        } catch (Throwable t) {
            QQThrowableReporter.report("QTitansLuckinSplashActivity", t);
        }
    }
}
