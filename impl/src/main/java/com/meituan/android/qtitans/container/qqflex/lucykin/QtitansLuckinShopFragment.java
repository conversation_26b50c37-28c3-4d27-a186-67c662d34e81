package com.meituan.android.qtitans.container.qqflex.lucykin;

import static com.meituan.android.hades.dyadater.container.ContainerConst.BABEL_TYPE_SMC_REPORT;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.View;

import com.meituan.android.hades.impl.report.BabelHelper;
import com.meituan.android.qtitans.container.qqflex.lucykin.model.LuckinMainPage;
import com.meituan.android.qtitans.container.reporter.ContainerReporter;

import java.util.HashMap;

public class QtitansLuckinShopFragment extends QtitansLuckinFlexFragment {

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        try {
            updateFeedBackgroundColor("#ffffff",0);
            LuckinMainPage shop = QtitansLuckinPreRequestManager.getInstance().getShopPage();
            if (shop != null && shop.shopList != null && !shop.shopList.isEmpty()) {
                updateListData(shop.shopList);
            } else {
                if (QtitansLuckinPreRequestManager.getInstance().getMainPage() != null) {
                    QtitansLuckinPreRequestManager.getInstance().requestNearbyShops(this.getContext(), QtitansLuckinPreRequestManager.getInstance().getMainPage(), new IFlexLuckinPageRequestCallback() {
                        @Override
                        public void onSuccess(LuckinMainPage data) {
                            if (data != null && data.shopList != null && !data.shopList.isEmpty()) {
                                updateListData(data.shopList);
                            }
                        }

                        @Override
                        public void onFailed(String message) {
                            HashMap<String, Object> info = new HashMap<>();
                            info.put("reason", "QtitansLuckinShopFragment: requestNearbyShops failed: " + message);
                            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
                        }
                    });
                }
            }

        } catch (Throwable e) {
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", "QtitansLuckinShopFragment onViewCreated failed: " + e.getMessage());
            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
        }

    }

    @Override
    protected void reportPV() {
        ContainerReporter.reportLuckinFragmentPv("shopList", mTargetUrl, false);
    }
}
