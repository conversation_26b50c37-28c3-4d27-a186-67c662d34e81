package com.meituan.android.qtitans;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Process;
import android.os.SystemClock;
import android.support.annotation.IdRes;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentActivity;
import android.support.v4.app.FragmentTransaction;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meituan.android.hades.BuildConfig;
import com.meituan.android.hades.dyadater.container.ContainerConst;
import com.meituan.android.hades.dyadater.container.ContainerServiceFactory;
import com.meituan.android.hades.dyadater.container.interfaces.IQtitansContainerDelegate;
import com.meituan.android.hades.dyadater.container.interfaces.IQtitansNativeConfigStrategy;
import com.meituan.android.hades.dyadater.desk.DeskResourceData;
import com.meituan.android.hades.dyadater.desk.DeskSourceEnum;
import com.meituan.android.hades.dyadater.desk.feedback.FeedbackExtensions;
import com.meituan.android.hades.dyadater.utils.HadesUtilsAdapter;
import com.meituan.android.hades.impl.R;
import com.meituan.android.hades.impl.desk.ScreenShotManager;
import com.meituan.android.hades.impl.desk.feedback.FeedbackManager;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.report.ReportParamsKey;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Lifecycle;
import com.meituan.android.hades.impl.utils.StorageHelper;
import com.meituan.android.hades.router.RouterManager;
import com.meituan.android.qtitans.bridge.MSISubscribeBridge;
import com.meituan.android.qtitans.container.QtitansContainerFragment;
import com.meituan.android.qtitans.container.QtitansContainerManager;
import com.meituan.android.qtitans.container.bean.ContainerResourceData;
import com.meituan.android.qtitans.container.bean.LoadingViewParams;
import com.meituan.android.qtitans.container.bean.PopupScene;
import com.meituan.android.qtitans.container.bean.PopupType;
import com.meituan.android.qtitans.container.bean.QtitansContainerConfig;
import com.meituan.android.qtitans.container.bean.QtitansContainerParams;
import com.meituan.android.qtitans.container.bean.QtitansToolBar;
import com.meituan.android.hades.dyadater.container.ContainerType;
import com.meituan.android.qtitans.container.common.QtitansContainerFunctionType;
import com.meituan.android.hades.dyadater.container.QtitansContainerVisitType;
import com.meituan.android.qtitans.container.common.QtitansLog;
import com.meituan.android.qtitans.container.common.QtitansUtils;
import com.meituan.android.hades.dyadater.container.interfaces.IContainerView;
import com.meituan.android.qtitans.container.config.ContainerConfigManager;
import com.meituan.android.qtitans.container.config.Page;
import com.meituan.android.qtitans.container.config.PageConfig;
import com.meituan.android.qtitans.container.config.QtitansStartTypeType;
import com.meituan.android.qtitans.container.msc.QtitansMscConfigStrategy;
import com.meituan.android.qtitans.container.nativ.QtitansNativeConfigStrategy;
import com.meituan.android.hades.dyadater.container.interfaces.ContainerConfigCallback;
import com.meituan.android.qtitans.container.presenter.ContainerResourcePresenter;
import com.meituan.android.qtitans.container.presenter.IContainerResource;
import com.meituan.android.qtitans.container.presenter.PresenterAction;
import com.meituan.android.qtitans.container.qqflex.lucykin.QtitansLuckinContainerFragment;
import com.meituan.android.qtitans.container.reporter.ContainerReporter;
import com.meituan.android.qtitans.container.reporter.QtitansMonitor;
import com.meituan.android.qtitans.container.ui.loading.QtitansLoadingView;
import com.meituan.android.qtitans.container.ui.view.QtitansContainerHomeCapsule;
import com.meituan.metrics.speedmeter.MetricsLaunchFunnelTask;
import com.meituan.metrics.util.TimeUtil;
import com.meituan.msc.common.utils.StatusBarUtils;
import com.sankuai.meituan.mbc.dsp.core.Dsp;
import com.sankuai.titans.base.TitansFragment;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.Target;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

public class QtitansContainerDelegateImpl implements IQtitansContainerDelegate, IContainerView {
    private static final String TAG = "QtitansContainer";
    public static final String QTITANS_TARGET = "qtitans_target";

    //KNB容器需要特殊处理
    protected Fragment mTitansFragment;
    protected QtitansContainerParams mQtitansContainerParams;
    protected String paramsStr = "";
    private String mUriStr = "";
    private LinearLayout mQtitansLoadingView;
    private FrameLayout mContainerView;
    private TextView mContainerStatus;
    private long mOnCreateStartTime = -1L;
    private long mOnCreateStartUptime = -1L;
    private ContainerResourcePresenter mContainerResourcePresenter;
    private boolean isHideLoadingView;
    private MSISubscribeBridge mMSISubscribe;
    private QtitansContainerVisitType mPinContainerVisitType;
    private QtitansContainerFunctionType mPinContainerFunctionType;
    private Runnable mConfigHideLoadingRunnable;
    private Runnable mFinishActivityRunnable;
    public Page mHomeConfig;
    public boolean hasStartTarget = false;
    private boolean mHasBlockBack = false;
    private boolean mBlockBacking = false;
    private QtitansMscConfigStrategy mQtitansMscConfigStrategy;
    private IQtitansNativeConfigStrategy mQtitansNativeConfigStrategy;
    private QtitansToolBar mQtitansToolBar;
    private QtitansMonitor mQtitansMonitor;
    private long onHideLoadingTime;
    private long onCreateElapsedTime;
    private boolean hasOnResume = false;
    public boolean hasPause = false;
    private WeakReference<Activity> mActivityWeakReference;
    private WeakReference<ViewGroup> mContainerHomeCapsuleWeakReference;
    private final String QITTANS_TAG_URL_KEY = "from";
    private final String QTITANS_TAG_URL_VALUE = "desktopPushEntrance";
    private Runnable mTipsRunnable;
    protected QtitansLuckinContainerFragment luckinContainerFragment;
    private Activity activity;
    private String resourceDataStr;
    private ContainerResourceData resourceData;

    @Override
    public void beforeOnCreate(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        this.activity = activity;
        Map<String, Object> log = new HashMap<>();
        log.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "start onCreate");
        log.put("tag", TAG);
        ContainerReporter.reportLog(log);
        hasOnResume = false;
        hasPause = false;
        isHideLoadingView = false;
        if (!isTaskAvailable(activity)) {
            return;
        }
        mContainerResourcePresenter = new ContainerResourcePresenter(activity, new IContainerResource() {
            @Override
            public void onGetResourceSuccess(ContainerResourceData data) {
                if (mQtitansNativeConfigStrategy != null && data != null) {
                    resourceData = data;
                    resourceDataStr = HadesUtils.toJson(data);
                    mQtitansNativeConfigStrategy.setContainerResourceData(resourceDataStr);
                }
            }

            @Override
            public void onGetResourceFail() {

            }
        });
        activity.overridePendingTransition(0, 0);
    }

    @Override
    public void onCreate(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        try {
            activity.setContentView(R.layout.qtitans_container);
            mOnCreateStartTime = System.currentTimeMillis();
            mOnCreateStartUptime = SystemClock.uptimeMillis();
            onCreateElapsedTime = TimeUtil.elapsedTimeMillis();
            Intent intent = activity.getIntent();
            if (intent == null) {
                finishSelf(activity, "onCreate null");
                return;
            }

            if (intent.hasExtra(ContainerConst.Qtitans_CONTAINER_PARAMS)) {
                paramsStr = intent.getStringExtra(ContainerConst.Qtitans_CONTAINER_PARAMS);
                mQtitansContainerParams = HadesUtils.fromJson(paramsStr, QtitansContainerParams.class);
            }
            if (mQtitansContainerParams == null
                    || TextUtils.isEmpty(mQtitansContainerParams.getTargetUrl())) {
                finishSelf(activity, "onCreate null");
                return;
            }
            if (isLuckinFlexPage()) {
                mQtitansContainerParams.setContainerType(ContainerType.FLEX);
            }
            mQtitansContainerParams.setContainerPreload(StorageHelper.getQtitansMimiContainerPreload(HadesUtils.getLchFromTargetUrl(mQtitansContainerParams.getTargetUrl())));
            mQtitansContainerParams.setResourceDownload(StorageHelper.getKeyQtitansMiniResourcePreload(HadesUtils.getLchFromTargetUrl(mQtitansContainerParams.getTargetUrl())));
            ContainerConfigManager.getInstance().updateQtitansContainerParams(paramsStr);
            mUriStr = mQtitansContainerParams.getTargetUrl();
            init(activity);
            ContainerReporter.reportContainerCreate(mQtitansContainerParams, mOnCreateStartTime, mOnCreateStartUptime);
        } catch (Throwable e) {
            Map<String, Object> logError = new HashMap<>();
            logError.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "onCreateError");
            logError.put("tag", TAG);
            ContainerReporter.reportLog(logError);
            if (BuildConfig.LOG_ENABLE) {
                e.printStackTrace();
            }
            defaultStart(activity, "onCreate error");
        }

    }

    private void init(@NonNull Activity activity) {
        try {
            mContainerView = activity.findViewById(R.id.container_qq);
            initMonitor();
            initImersive(activity);
            initContainerParams();
            initLoadingView(activity);
            addLifeCycleListener();
            initQtitansLoadingStrategy(activity);
            initMSISubscribeBridge(activity);
            requestQContainerConfig(activity);
            Map<String, Object> log = new HashMap<>();
            log.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "init");
            log.put("tag", TAG);
            ContainerReporter.reportLog(log);
        } catch (Throwable e) {
            defaultStart(activity, "init error");
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    private void requestQContainerConfig(Activity activity) {
        String configType = ContainerConfigManager.KEY_CONTAINER;
        String checkSource = ContainerConfigManager.getInstance().getCurrentCheckSource();
        String businessType = ContainerConfigManager.getInstance().getCurrentBusinessType();
        long startRequestTime = TimeUtil.elapsedTimeMillis();
        requestQContainerConfig(activity, configType, businessType, checkSource, "", new ContainerConfigCallback() {
            @Override
            public void onGetConfigSuccess(String containerConfig, String pageConfig) {
                if (pageConfig == null) {
                    if (BuildConfig.LOG_ENABLE) {
                        QtitansLog.log("pageConfig is null");
                    }
                    onGetConfigFail("pageConfig is null");
                    return;
                }
                long endTime = TimeUtil.elapsedTimeMillis();
                QtitansContainerConfig containerConfig1 = HadesUtils.fromJson(containerConfig, QtitansContainerConfig.class);
                PageConfig pageConfig1 = HadesUtils.fromJson(pageConfig, PageConfig.class);
                onConfigUpdate(activity, containerConfig1, pageConfig1);
                Map<String, Object> log = new HashMap<>();
                log.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "onGetConfigSuccess");
                log.put("tag", TAG);
                log.put("isFromCache", pageConfig1.isFromCache);
                log.put("configVersion", pageConfig1.configVersion);
                log.put("checkSource", checkSource);
                log.put("businessType", businessType);
                log.put("containerType", getContainerType());
                log.put("requestTimeDiff", endTime - startRequestTime);
                log.put("isActivityFinish", QtitansUtils.isActivityFinish(activity));
                ContainerReporter.reportLog(log);
            }

            @Override
            public void onGetConfigFail(String errorMsg) {
                if (BuildConfig.LOG_ENABLE) {
                    QtitansLog.log("initTitleBar onGetConfigFail");
                }
                defaultStart(activity, "onGetConfigFail msg = " + errorMsg);
            }
        });
    }

    private void requestQContainerConfig(Activity activity, String configType, String businessType, String checkSource,
                                         String uriId,
                                         ContainerConfigCallback callback) {
        ContainerConfigManager.getInstance().requestContainerConfig(
                activity,
                businessType,
                checkSource,
                configType,
                uriId,
                callback);
    }

    private void onConfigUpdate(Activity activity, QtitansContainerConfig containerConfig, PageConfig pageConfig) {
        try {
            if (containerConfig != null) {
                if (QtitansContainerManager.getInstance().isContainerDegraded(containerConfig)
                        || (getPinContainerVisitType() == QtitansContainerVisitType.WidgetShortcut && !TextUtils.isEmpty(containerConfig.latestUnderTakeUrl) && !QtitansContainerManager.getInstance().isStartUrlSameAsLatest(containerConfig, mUriStr))) {
                    if (!TextUtils.isEmpty(containerConfig.latestUnderTakeUrl)) {
                        mUriStr = containerConfig.latestUnderTakeUrl;
                        defaultStart(activity, "start new url");
                        QtitansContainerParams params = ContainerConfigManager.getInstance().getQtitansContainerParams();
                        if (params != null) {
                            params.setTargetUrl(mUriStr);
                            long time = System.currentTimeMillis();
                            long uptime = SystemClock.uptimeMillis();
                            ContainerReporter.reportContainerJump(HadesUtils.toJson(params), time, uptime);
                        }
                    } else {
                        finishSelf(activity, "start new url");
                        QtitansUtils.gotoMTHomePage(activity);
                    }
                    return;
                }
            }
            if (!QtitansUtils.isActivityFinish(activity) && pageConfig != null) {
                checkConfigHideLoading(activity);
                Page homeConfig = pageConfig.getHomePageData();
                mHomeConfig = homeConfig;
                if (homeConfig != null && homeConfig.isHome) {
                    if (!enableScreenPage() && !homeConfig.showTitlebar && !homeConfig.showCapsule) {
                        defaultStart(activity, "only open");
                        return;
                    }
                    //目标业务Activity MSC Native MMP
                    if (QtitansStartTypeType.containType(homeConfig.startType) == QtitansStartTypeType.Activity) {
                        hasStartTarget = false;
                        if (getContainerType() == ContainerType.UNKNOWN || getContainerType() == ContainerType.MMP) {
                            String paramsStr = HadesUtils.toJson(ContainerConfigManager.getInstance().getQtitansContainerParams());
                            if (mQtitansNativeConfigStrategy != null) {
                                mQtitansNativeConfigStrategy.setQtitasnContainerParams(paramsStr);
                                mQtitansNativeConfigStrategy.setPageConfig(HadesUtils.toJson(pageConfig));
                            }
                            if (((mQtitansContainerParams != null && mQtitansContainerParams.useCapsule()) || ContainerConfigManager.getInstance().isFormMtAPPVisit(paramsStr))
                                    && ContainerConfigManager.getInstance().isVideo(paramsStr)) {
                                Uri.Builder builder = Uri.parse(mUriStr).buildUpon();
                                builder.appendQueryParameter("useCapsule", "1");
                                mUriStr = builder.build().toString();
                            }
                            startActivity(activity, pageConfig);
                        } else if (getContainerType() == ContainerType.MSC) {
                            //msc
                            createQtitansLoadingStrategy(homeConfig);
                            startActivity(activity, pageConfig);
                        } else {
                            defaultStart(activity, "native error");
                        }
                    } else {
                        //QQ Fragment  KNB MRN
//                        if (!isLuckinFlexPage()) {
                        if (activity instanceof FragmentActivity) {
                            showContainerFragment((FragmentActivity) activity);
                        }

//                        }
                        if (!homeConfig.isImersive) {
                            if (mContainerStatus != null) {
                                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                                        LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                                if (mContainerStatus.getLayoutParams() != null) {
                                    layoutParams = (LinearLayout.LayoutParams) mContainerStatus.getLayoutParams();
                                }
                                layoutParams.height = QtitansUtils.getStatusBarHeight();
                                mContainerStatus.setLayoutParams(layoutParams);
                                mContainerStatus.setVisibility(View.VISIBLE);
                            }
                        }
                        //首页只有胶囊 无标题栏场景
                        if (!homeConfig.showTitlebar && homeConfig.showCapsule) {
                            new QtitansContainerHomeCapsule(activity,
                                    activity.findViewById(R.id.container_qq), homeConfig, mQtitansToolBar, mPinContainerVisitType.getType()).updateView();
                        }
                    }
                } else {
                    defaultStart(activity, "homeConfig null");
                }
            } else {
                defaultStart(activity, "onConfigUpdate");
            }
            if (mQtitansContainerParams != null && "PUSH".equals(mQtitansContainerParams.getJumpScene())) {
                if (activity.getIntent() != null) {
                    if (!TextUtils.isEmpty(mQtitansContainerParams.getDeskResourceDataStr())) {
                        if (!mQtitansContainerParams.isDSPPlusFlag()) {
                            if (FeedbackManager.getInstance().registerListenerWhenRouterActivityStart()) {
                                FeedbackManager.getInstance().registerActivityLifecycle(activity.getApplicationContext());
                            }
                            addFeedbackButton(activity, mQtitansContainerParams.getDeskResourceDataStr());
                        } else if (mQtitansContainerParams.isDSPPlusFlag()) {
                            HadesUtils.runOnWorkThread(() -> {
                                DeskResourceData deskResourceData = HadesUtils.fromJson(mQtitansContainerParams.getDeskResourceDataStr(), DeskResourceData.class);
                                int code = mQtitansContainerParams == null ? -1 : mQtitansContainerParams.getSceneCode();
                                ScreenShotManager.getInstance(activity).registerScreenshotInLifecycle(ScreenShotManager.ScreenShotEnum.LANDING,
                                        deskResourceData, code, FeedbackManager.SCENE_DAW, null);
                            });
                        }
                    }
                }
            }
        } catch (Throwable e) {
            defaultStart(activity, "onConfigUpdate error");
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }

    }

    private void showContainerFragment(FragmentActivity activity) {
        String jumUrlStr = mUriStr;
        if (HadesUtils.enableURLTag()) {
            if (getContainerType() == ContainerType.MMP || getContainerType() == ContainerType.MSC) {
                jumUrlStr = addURLQtitansTag(mUriStr);
            }
        }
        if (getContainerType() == ContainerType.MRN) {
            if (ContainerConst.pinContainerVisit.equals(getPinContainerVisitType().getType())) {
              jumUrlStr =  QtitansContainerManager.getInstance().appendPinContainerVisitKey(jumUrlStr, QtitansContainerVisitType.VisitPush.getType());
            }
        }
        if (getContainerType() == ContainerType.WEB) {
            if (ContainerConst.VisitPush.equals(getPinContainerVisitType().getType())) {
                jumUrlStr = QtitansContainerManager.getInstance().appendPinContainerVisitKey(jumUrlStr, ContainerConst.VisitPush);
            }
        }
        FragmentTransaction fragmentTransaction = activity.getSupportFragmentManager().beginTransaction();
        Fragment fragment = QtitansContainerFragment.createFragment(activity, getContainerType(), jumUrlStr, getPinContainerVisitType().getType(), this);
        if (fragment == null) {
            finishSelf(activity, "showContainerFragment");
            return;
        }
        updateTaskDescription(activity);
        if (getContainerType() == ContainerType.WEB) {
            mTitansFragment = (TitansFragment) fragment;
        }
        if (getContainerType() == ContainerType.FLEX && fragment instanceof QtitansLuckinContainerFragment) {
            luckinContainerFragment = (QtitansLuckinContainerFragment) fragment;
        }
        fragmentTransaction.replace(getContainerId(), fragment, "QtitansFragment" + getContainerTypeName());
        fragmentTransaction.commitAllowingStateLoss();
    }

    private String addURLQtitansTag(String mUriStr) {
        String urlStr = mUriStr;
        try {
            Uri targetPathUri = Uri.parse(mUriStr);
            if (!TextUtils.isEmpty(mUriStr) && mQtitansContainerParams != null) {
                String originTargetPath = targetPathUri.getQueryParameter("targetPath");
                if (!TextUtils.isEmpty(originTargetPath) && !originTargetPath.contains(QITTANS_TAG_URL_KEY)) {
                    StringBuilder targetPathBuilder = new StringBuilder(originTargetPath);
                    if (!originTargetPath.contains("?")) {
                        targetPathBuilder.append("?");
                    }
                    targetPathBuilder.append("&").append(QITTANS_TAG_URL_KEY).append("=").append(QTITANS_TAG_URL_VALUE);
                    Uri newUri = QtitansUtils.replaceUriParameter(targetPathUri, "targetPath", targetPathBuilder.toString());
                    urlStr = newUri.toString();
                }
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
        return urlStr;
    }

    public String getContainerTypeName() {
        return getContainerType() == null ? ContainerType.UNKNOWN.name() : getContainerType().name();
    }

    private void createQtitansLoadingStrategy(Page homeConfig) {
        if (getContainerType() == ContainerType.MSC) {
            if (mQtitansMscConfigStrategy != null) {
                mQtitansMscConfigStrategy.setHomeConfig(homeConfig);
            }
        }
    }

    private void addFeedbackButton(Activity activity, String strDeskResourceData) {
        try {
            HadesUtils.runOnMainThreadWithDelay(() -> {
                HadesUtils.runOnWorkThread(() -> {
                    int code = mQtitansContainerParams == null ? -1 : mQtitansContainerParams.getSceneCode();

                    DeskResourceData deskResourceData = HadesUtils.fromJson(strDeskResourceData, DeskResourceData.class);
                    if (deskResourceData == null) {
                        return;
                    }
                    FeedbackManager.getInstance().showFeedbackIcon(
                            activity.getApplicationContext(), deskResourceData,
                            DeskSourceEnum.getBycode(code), FeedbackManager.SCENE_DESK_PUSH);
                    if (FeedbackExtensions.isRiskAbTestGroup(deskResourceData)
                            && FeedbackExtensions.isRiskUser(deskResourceData)
                            && !TextUtils.isEmpty(resourceDataStr)) {
                        mQtitansNativeConfigStrategy.showTips(activity, resourceDataStr);
                    } else {
                        ScreenShotManager.getInstance(activity).registerScreenshotInLifecycle(ScreenShotManager.ScreenShotEnum.LANDING,
                                deskResourceData, code, FeedbackManager.SCENE_SUBSCRIBE_PUSH,
                                () -> {
                                    if (!TextUtils.isEmpty(resourceDataStr) && mQtitansNativeConfigStrategy != null) {
                                        mQtitansNativeConfigStrategy.showTips(activity, resourceDataStr);
                                    }
                                });
                    }
                });

            }, 1000);
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }


    }

    private void checkConfigHideLoading(Activity activity) {
        if (shouldHideLoading()) {
            mConfigHideLoadingRunnable = ()->this.onHideLoadingView();
            HadesUtils.runOnMainThreadWithDelay(mConfigHideLoadingRunnable, 1000);
        }
    }

    private boolean shouldHideLoading() {
        if (getContainerType() == ContainerType.MMP || getContainerType() == ContainerType.UNKNOWN) {
            return true;
        }
        return !enableScreenPage() && mQtitansContainerParams != null && !mQtitansContainerParams.isFromExternalLink();
    }

    private void startActivity(Activity activity,PageConfig homeConfig) {
        updateTaskDescription(activity);
        String jumUrlStr = mUriStr;
        if (HadesUtils.enableURLTag()) {
            if (getContainerType() == ContainerType.MMP || getContainerType() == ContainerType.MSC) {
                jumUrlStr = addURLQtitansTag(mUriStr);
            }
        }
        Intent targetIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(jumUrlStr));
        targetIntent.putExtra(QTITANS_TARGET, QTITANS_TARGET);
        targetIntent.putExtra(Dsp.INTENT_EXTRA_IS_DSP_COLD_START, true);
        RouterManager.appendLightBoxParamsIfNeed(targetIntent);
        if (homeConfig != null && homeConfig.isFromCache && mContainerView != null) {
            mContainerView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    mContainerView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    hasStartTarget = true;
                    activity.startActivity(targetIntent);
                    activity.overridePendingTransition(0, 0);
                }
            });
        } else {
            hasStartTarget = true;
            activity.startActivity(targetIntent);
            activity.overridePendingTransition(0, 0);
        }
    }

    private void updateTaskDescription(Activity activity) {
        try {
            if (QtitansUtils.isActivityFinish(activity)) {
                return;
            }
            if (StorageHelper.getQcUpdateAlias(activity)) {
                updateTaskDescriptionNew(activity);
                return;
            }

            QtitansContainerConfig containerConfig = ContainerConfigManager.getInstance().getContainerConfig();
            if (containerConfig != null && containerConfig.updateTaskIcon) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    String checkSource = ContainerReporter.getCheckSource(mQtitansContainerParams);
                    int icon = ContainerConfigManager.getInstance().getDeskAppDefaultIcon(checkSource);
                    String name = ContainerConfigManager.getInstance().getDeskAppDefaultName(checkSource);
                    if (icon > 0 && !TextUtils.isEmpty(name)) {
                        activity.setTaskDescription(new ActivityManager.TaskDescription(name,
                                BitmapFactory.decodeResource(activity.getResources(), icon)));
                    }
                }
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    private void updateTaskDescriptionNew(Activity activity) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                String checkSource = ContainerReporter.getCheckSource(mQtitansContainerParams);
                int icon = ContainerConfigManager.getInstance().getDeskAppDefaultIcon(checkSource);
                String name = TextUtils.equals("30005", checkSource) ? "看剧赚钱" : ContainerConfigManager.getInstance().getDeskAppDefaultName(checkSource);
                if (getLoadingViewParams() != null && !TextUtils.isEmpty(getLoadingViewParams().getIconUrl())) {
                    Picasso.with(activity).load(getLoadingViewParams().getIconUrl()).into(new Target() {
                        @Override
                        public void onBitmapLoaded(Bitmap bitmap, Picasso.LoadedFrom from) {
                            if (QtitansUtils.isActivityFinish(activity)) {
                                return;
                            }
                            if (bitmap != null && !TextUtils.isEmpty(name)) {
                                activity.setTaskDescription(new ActivityManager.TaskDescription(name, bitmap));
                            }
                        }

                        @Override
                        public void onBitmapFailed(Drawable errorDrawable) {
                            if (QtitansUtils.isActivityFinish(activity)) {
                                return;
                            }
                            if (icon > 0 && !TextUtils.isEmpty(name)) {
                                activity.setTaskDescription(new ActivityManager.TaskDescription(name,
                                        BitmapFactory.decodeResource(activity.getResources(), icon)));
                            }
                        }

                        @Override
                        public void onPrepareLoad(Drawable placeHolderDrawable) {

                        }
                    });

                } else {
                    if (icon > 0 && !TextUtils.isEmpty(name)) {
                        activity.setTaskDescription(new ActivityManager.TaskDescription(name,
                                BitmapFactory.decodeResource(activity.getResources(), icon)));
                    }
                }
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    private QtitansContainerVisitType getPinContainerVisitType() {
        return mPinContainerVisitType == null ? QtitansContainerVisitType.UNKNOWN : mPinContainerVisitType;
    }

    private void initMSISubscribeBridge(Activity activity) {
        mMSISubscribe = new MSISubscribeBridge();
        mMSISubscribe.subscribe(activity);
    }

    private void initQtitansLoadingStrategy(Activity activity) {
        hasStartTarget = false;
        if (getContainerType() == ContainerType.MSC) {
            mQtitansMscConfigStrategy = new QtitansMscConfigStrategy();
            mQtitansMscConfigStrategy.init(activity, getLoadingViewParams(), this, mQtitansToolBar);
        } else if (getContainerType() == ContainerType.UNKNOWN || getContainerType() == ContainerType.MMP) {
            mQtitansNativeConfigStrategy = new QtitansNativeConfigStrategy();
            mQtitansNativeConfigStrategy.init(activity, this);
        }
    }

    private void initLoadingView(Activity activity) {
        if (enableScreenPage()) {
            mQtitansLoadingView = activity.findViewById(R.id.container_loading_view);
            QtitansLoadingView qtitansLoadingView = new QtitansLoadingView(activity);
            qtitansLoadingView.init(activity, getLoadingViewParams());

            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            qtitansLoadingView.setLayoutParams(layoutParams);
            mQtitansLoadingView.addView(qtitansLoadingView);
            mContainerStatus = activity.findViewById(R.id.qtitans_container_status);
            onShowLoadingView();
        } else {
            try {
                FrameLayout view = activity.findViewById(getContainerId());
                if (view != null) {
                    view.setBackgroundColor(Color.TRANSPARENT);
                }
                if (ContainerConfigManager.getInstance().isVideo(paramsStr)) {
                    activity.findViewById(R.id.container_qq).setBackgroundResource(R.drawable.hades_splash_video);
                }
            } catch (Throwable e) {
                QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
            }
        }
    }

    public @IdRes
    int getContainerId() {
        return R.id.fragment_container;
    }

    private boolean enableScreenPage() {
        return getLoadingViewParams() != null && getLoadingViewParams().isUseScreenPageAll();
    }

    public LoadingViewParams getLoadingViewParams() {
        return mQtitansContainerParams != null ? mQtitansContainerParams.getLoadingViewParams() : null;
    }

    public ContainerType getContainerType() {
        return mQtitansContainerParams == null ? ContainerType.UNKNOWN : mQtitansContainerParams.getContainerType();
    }

    private void initMonitor() {
        mQtitansMonitor = new QtitansMonitor();
    }

    private void initImersive(@NonNull Activity activity) {
        try {
            StatusBarUtils.compatStatusBar(activity);
            StatusBarUtils.setStatusBarTextColor(activity, true);
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    private void addLifeCycleListener() {
        ContainerConfigManager.getInstance().init();
        ContainerConfigManager.getInstance().setQtitansToolBar(mQtitansToolBar);
    }

    private void initContainerParams() {
        mPinContainerVisitType = getLoadingViewParams() == null ? QtitansContainerVisitType.UNKNOWN : QtitansContainerVisitType.containType(getLoadingViewParams().getVisitType());
        mPinContainerFunctionType = getLoadingViewParams() == null ? QtitansContainerFunctionType.UNKNOWN : QtitansContainerFunctionType.containType(getLoadingViewParams().getFunctionType());
        mContainerResourcePresenter.attachView(this);
    }

    @Override
    public void onStart(@NonNull Activity activity) {

    }

    @Override
    public void onResume(@NonNull Activity activity) {

    }

    @Override
    public boolean beforeOnResume(@NonNull Activity activity) {
        hasOnResume = true;
        activity.overridePendingTransition(0, 0);
        if (mQtitansContainerParams != null && mQtitansContainerParams.getContainerOnShowLoadingTime() <= 0) {
            mQtitansContainerParams.setContainerOnCreateTime(onCreateElapsedTime);
            if ("WIDGET".equals(mQtitansContainerParams.getJumpScene()) || "SHORTCUT".equals(mQtitansContainerParams.getJumpScene())) {
                mQtitansContainerParams.setContainerPerfStartTime(MetricsLaunchFunnelTask.getInstance().getStartTime());
            }
            mQtitansContainerParams.setContainerOnShowLoadingTime(TimeUtil.elapsedTimeMillis());
            paramsStr = HadesUtilsAdapter.toJson(mQtitansContainerParams);
            ContainerConfigManager.getInstance().updateQtitansContainerParams(paramsStr);
            ContainerReporter.reportContainerT0Time(paramsStr);
        }
        if (mTitansFragment != null) {
            if (!isTaskAvailable(activity)) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void onPause(@NonNull Activity activity) {

    }

    @Override
    public void beforeOnPause(@NonNull Activity activity) {
        hasPause = true;
        activity.overridePendingTransition(0, 0);
    }

    @Override
    public void onStop(@NonNull Activity activity) {
        activity.overridePendingTransition(0, 0);
    }

    @Override
    public void beforeOnStop(@NonNull Activity activity) {
        try {
            if (hasStartTarget && (getContainerType() == ContainerType.UNKNOWN
                    || getContainerType() == ContainerType.MMP)) {
                onHideLoadingView();
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    @Override
    public void onDestroy(@NonNull Activity activity) {
        ContainerServiceFactory.getInstance().reset();
    }

    @Override
    public void beforeOnDestroy(@NonNull Activity activity) {
        if (BuildConfig.LOG_ENABLE) {
            QtitansLog.log("onDestroy " + toString());
        }
        try {
            reset(false,"onDestroy");
            resetTips(true);
            if (mActivityWeakReference != null) {
                mActivityWeakReference.clear();
                mActivityWeakReference = null;
            }

            if (mContainerHomeCapsuleWeakReference != null) {
                mContainerHomeCapsuleWeakReference.clear();
                mContainerHomeCapsuleWeakReference = null;
            }
            if (mFinishActivityRunnable != null) {
                HadesUtils.removeFromMainThread(mFinishActivityRunnable);
            }
            if (mMSISubscribe != null) {
                mMSISubscribe.unsubscribe();
            }
            if (mConfigHideLoadingRunnable != null) {
                HadesUtils.removeFromMainThread(mConfigHideLoadingRunnable);
                mConfigHideLoadingRunnable = null;
            }
            mContainerResourcePresenter.detachView();
            ContainerReporter.reportContainerDestroy(mQtitansContainerParams,
                    mOnCreateStartTime, mOnCreateStartUptime);

        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    @Override
    public void onNewIntent(@NonNull Activity activity, @NonNull Intent intent) {
        if (BuildConfig.LOG_ENABLE) {
            QtitansLog.log("onNewIntent");
            QtitansLog.logDebugIntent(intent);
        }
        newIntent(activity, intent);

    }
    protected void newIntent(Activity activity, Intent newIntent) {
        try {
            if (newIntent == null) {
                return;
            }
            if (newIntent.hasExtra(ContainerConst.Qtitans_CONTAINER_PARAMS)) {
                String paramsStr = newIntent.getStringExtra(ContainerConst.Qtitans_CONTAINER_PARAMS);
                QtitansContainerParams params = HadesUtils.fromJson(paramsStr, QtitansContainerParams.class);
                if (params != null) {
                    if (params.isGoToMTHome()) {
                        finishSelf(activity, "goToMTHome");
                        QtitansUtils.gotoMTHomePage(activity);
                    } else {
                        finishSelf(activity, "reopen");
                        QtitansContainerManager.getInstance().openQtitansContainer(activity, paramsStr, true);
                    }
                    ContainerReporter.reportContainerNewIntent(params);
                }
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    private boolean isVisitDialogPopupSceneExit() {
        try {
            return mBlockBacking ||
                    (mHomeConfig != null &&
                            QtitansStartTypeType.containType(mHomeConfig.startType) == QtitansStartTypeType.Fragment)
                            && !mHasBlockBack
                            && ContainerConfigManager.getInstance().getVisitPopupScene() == PopupScene.EXIT_POPUP;
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
        return false;
    }

    @Override
    public void onActivityResult(@NonNull Activity activity, int requestCode, int resultCode, @Nullable Intent data) {
        if (mTitansFragment != null) {
            mTitansFragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public boolean onBackPressed(@NonNull Activity activity) {
        handleBackPress(activity);
        return false;
    }

    @Override
    public void onPostCreate(Activity activity, Bundle savedInstanceState) {
        try {
            if (BuildConfig.LOG_ENABLE) {
                QtitansLog.log("onPostCreate " + toString());
            }
            if (mTitansFragment != null) {
                if (!isTaskAvailable(activity)) {
                    return;
                }
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    @Override
    public boolean isLuckinFlexPage() {
        return false;
    }

    protected void handleBackPress(Activity activity) {
        try {
            ContainerConfigManager.getInstance().removeLifeCycleListener();
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    @Override
    public void finishSelf(Activity activity, String from) {
        try {
            reset(false, from);
            ContainerConfigManager.getInstance().clear("finishSelf");
            ContainerConfigManager.getInstance().removeLifeCycleListener();
            activity.finish();
            activity.overridePendingTransition(0, 0);
            Map<String, Object> log = new HashMap<>();
            log.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "finishSelf");
            log.put("tag", TAG);
            log.put("form", from);
            ContainerReporter.reportLog(log);
        } catch (Throwable throwable) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", throwable);
        }
    }

    @Override
    public void onReStart(Activity activity) {

    }

    @Override
    public Activity getActivity() {
        return activity;
    }


    @Override
    public void onShowLoadingView() {
        try {
            if (QtitansUtils.isActivityFinish(getActivity())) {
                return;
            }
            if (mQtitansLoadingView != null) {
                mQtitansLoadingView.setVisibility(View.VISIBLE);
            }

            Map<String, Object> log = new HashMap<>();
            log.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "onShowLoadingView");
            log.put("tag", TAG);
            ContainerReporter.reportLog(log);
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    @Override
    public void onHideLoadingView() {
        onHideLoadingTime = TimeUtil.elapsedTimeMillis();
        HadesUtils.runOnMainThread(() -> {
            try {
                if (QtitansUtils.isActivityFinish(activity)) {
                    return;
                }
                //防止多次回调
                if (isHideLoadingView) {
                    return;
                }
                isHideLoadingView = true;
                if (mQtitansLoadingView != null) {
                    mQtitansLoadingView.setVisibility(View.GONE);
                }
                if (mQtitansContainerParams != null) {
                    mQtitansContainerParams.setContainerOnCreateTime(onCreateElapsedTime);
                    mQtitansContainerParams.setContainerOnHideLoadingTime(onHideLoadingTime);
                    if ("WIDGET".equals(mQtitansContainerParams.getJumpScene()) || "SHORTCUT".equals(mQtitansContainerParams.getJumpScene())) {
                        mQtitansContainerParams.setContainerPerfStartTime(MetricsLaunchFunnelTask.getInstance().getStartTime());
                    }
                    if (ContainerType.MMP != mQtitansContainerParams.getContainerType() && ContainerType.UNKNOWN != mQtitansContainerParams.getContainerType()) {
                        ContainerReporter.reportContainerT1Time(paramsStr);
                    }
                }
                requestContainerResources(activity, PresenterAction.HIDE_LOADING);
                adaptTitleBar(activity);
                Map<String, Object> log = new HashMap<>();
                log.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "onHideLoadingView");
                log.put("tag", TAG);
                ContainerReporter.reportLog(log);
                ContainerConfigManager.getInstance().removeBackGroundLifeCycleListener();
            } catch (Throwable e) {
                QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
            }
        });


    }

    private void requestContainerResources(Activity activity, PresenterAction action) {
        if (getLoadingViewParams() != null) {
            mContainerResourcePresenter.getContainerResources(activity,
                    getLoadingViewParams().getBusinessType(),
                    getLoadingViewParams().getCheckSource(),
                    mPinContainerVisitType.getType(),
                    action);
        }

    }

    @Override
    public void onAfterHideLoadingViewRequest() {
        try {
            if (BuildConfig.LOG_ENABLE) {
                QtitansLog.log("onAfterHideLoadingViewRequest ");
            }
            parseURLAction(activity);
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    private void parseURLAction(Activity activity) {
        try {
          if (mQtitansNativeConfigStrategy != null && !TextUtils.isEmpty(resourceDataStr)) {
              mQtitansNativeConfigStrategy.showTips(activity, resourceDataStr);
          }

            PageConfig pageConfig = ContainerConfigManager.getInstance().getPageConfig();
            if (pageConfig != null && pageConfig.getHomePageData() != null && pageConfig.getHomePageData().startType == 1) {
                activity = Lifecycle.getTopActivity();
            }
            String task = "";
            if (!TextUtils.isEmpty(task)) {
                Map<String, Object> log = new HashMap<>();
                log.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "parseURLAction");
                log.put("task", task);
                log.put("checkSource", ContainerConfigManager.getInstance().getCurrentCheckSource());
                log.put("containerType", getContainerType());
                log.put("isActivityFinishA", QtitansUtils.isActivityFinish(activity));
                log.put("isActivityFinishT", QtitansUtils.isActivityFinish(activity));
                ContainerReporter.reportLog(log);
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    private boolean isVisitDialogPopupSceneEnter() {
        try {
            return ContainerConfigManager.getInstance().getVisitPopupScene() == PopupScene.ENTER_POPUP
                    && resourceData != null && (resourceData.popupType == PopupType.FIRST_POPUP.getType()
                    || resourceData.popupType == PopupType.REVISIT_POPUP.getType());
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
        return false;
    }

    private void adaptTitleBar(Activity activity) {
        PageConfig pageConfig = ContainerConfigManager.getInstance().getPageConfig();
        if (pageConfig != null) {
            Page homePage = pageConfig.getHomePageData();
            if (homePage == null) {
                if (BuildConfig.LOG_ENABLE) {
                    QtitansLog.log("homePage is null");
                }
                return;
            }
            if (QtitansStartTypeType.containType(homePage.startType) != QtitansStartTypeType.Activity) {
                ContainerConfigManager.getInstance().realHandleConfigView(activity, pageConfig, homePage);
            }
        }
    }

    private boolean isTaskAvailable(Activity activity) {
        try {
            if (activity == null) {
                return false;
            }
            if (activity.getTaskId() == -1) {
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M) {
                    Process.killProcess(Process.myPid());
                } else {
                    finishSelf(activity, "isTaskAvailable ");
                }
                return false;
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
        return true;
    }

    private void resetTips(boolean isRemove) {
        try {
            if (mQtitansContainerParams == null || !"PUSH".equals(mQtitansContainerParams.getJumpScene())) {
                if (mActivityWeakReference != null) {
                    mActivityWeakReference.clear();
                    mActivityWeakReference = null;
                }

                if (mContainerHomeCapsuleWeakReference != null) {
                    mContainerHomeCapsuleWeakReference.clear();
                    mContainerHomeCapsuleWeakReference = null;
                }
            }
            if (isRemove && mTipsRunnable != null) {
                HadesUtils.removeFromMainThread(mTipsRunnable);
                mTipsRunnable = null;
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    private void reset(boolean defaultStart, String from) {

        try {
            if (BuildConfig.LOG_ENABLE) {
                QtitansLog.log(" reset " + from);
            }
            hasStartTarget = false;
            if (mQtitansNativeConfigStrategy != null) {
                mQtitansNativeConfigStrategy.reset();
                mQtitansNativeConfigStrategy = null;
            }

            if (mQtitansMscConfigStrategy != null) {
                mQtitansMscConfigStrategy.reset();
                mQtitansMscConfigStrategy = null;
            }
            if (mQtitansMonitor != null) {
                boolean isReportContainerDuration = !defaultStart && hasOnResume;
                mQtitansMonitor.reset(isReportContainerDuration);
                mQtitansMonitor = null;
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }
    private void defaultStart(@NonNull Activity activity, String from) {
        try {
            if (TextUtils.isEmpty(mUriStr)) {
                return;
            }
            reportLogDefaultStart(activity, from);
            reset(true, "defaultStart");
            ContainerConfigManager.getInstance().clear("defaultStart");
            ContainerConfigManager.getInstance().removeLifeCycleListener();
            ContainerConfigManager.getInstance().removeBackGroundLifeCycleListener();
            Intent targetIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(mUriStr));
            targetIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
            targetIntent.putExtra(Dsp.INTENT_EXTRA_IS_DSP_COLD_START, true);
            RouterManager.appendLightBoxParamsIfNeed(targetIntent);
            activity.startActivity(targetIntent);
            activity.overridePendingTransition(0, 0);
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    private void reportLogDefaultStart(Activity activity, String from) {
        try {
            Map<String, Object> log = new HashMap<>();
            log.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "defaultStart");
            log.put("tag", TAG);
            log.put("from", from);
            log.put("visitType", getPinContainerVisitType().getType());
            log.put("checkSource", ContainerConfigManager.getInstance().getCurrentCheckSource());
            log.put("isActivityFinish", QtitansUtils.isActivityFinish(activity));
            ContainerReporter.reportLog(log);
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerDelegateImpl", e);
        }
    }

    @Override
    public Fragment getTitansFragment() {
        return mTitansFragment;
    }

    @Override
    public View getLoadingView(Context context) {
        if ( getLoadingViewParams() != null && getLoadingViewParams().isUseScreenPage()) {
            QtitansLoadingView loadingView = new QtitansLoadingView(context);
            loadingView.init(context, getLoadingViewParams());
            return loadingView;
        }
        return null;
    }
}
