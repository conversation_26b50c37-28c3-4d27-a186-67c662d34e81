package com.meituan.android.qtitans.container.reporter;

import com.meituan.android.aurora.ActivitySwitchCallbacks;
import com.meituan.android.hades.impl.BuildConfig;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.qtitans.container.common.QtitansLog;
import com.meituan.android.qtitans.container.config.ContainerConfigManager;
import com.meituan.android.singleton.ApplicationSingleton;

public class QtitansMonitor {
    private static final String TAG = "QtitansMonitor";
    private ActivitySwitchCallbacks mSwitchCallbacks;
    private final long mStartTime;
    private long onBackgroundTime = 0;
    private long onBackgroundTimeAll;
    private String onBackgroundTimeAllStr = "";

    public QtitansMonitor() {
        mStartTime = System.currentTimeMillis();
        registerQtitansMonitor();
    }

    private void registerQtitansMonitor() {
        try {
            if (mSwitchCallbacks == null) {
                mSwitchCallbacks = new ActivitySwitchCallbacks() {

                    @Override
                    public void onForeground() {
                        if (BuildConfig.LOG_ENABLE) {
                            QtitansLog.log(TAG + " onForeground onBackgroundTime = " + onBackgroundTime
                                    + " , onBackgroundTimeAllStr =" + onBackgroundTimeAllStr);
                        }
                        if (onBackgroundTime > 0) {
                            long lastBackgroundTime = System.currentTimeMillis() - onBackgroundTime;
                            onBackgroundTimeAll = onBackgroundTimeAll + lastBackgroundTime;
                            onBackgroundTimeAllStr = onBackgroundTimeAllStr + "," + lastBackgroundTime;
                        }
                        onBackgroundTime = 0;
                    }

                    @Override
                    public void onBackground() {
                        onBackgroundTime = System.currentTimeMillis();
                        if (BuildConfig.LOG_ENABLE) {
                            QtitansLog.log(TAG + " onBackground ");
                        }
                    }
                };
            }
            ApplicationSingleton.getInstance().registerActivityLifecycleCallbacks(mSwitchCallbacks);
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansMonitor", e);
        }
    }

    public void reset(boolean isReportContainerDuration) {
        try {
            if (mSwitchCallbacks != null) {
                ApplicationSingleton.getInstance().unregisterActivityLifecycleCallbacks(mSwitchCallbacks);
                mSwitchCallbacks = null;
                if (isReportContainerDuration) {
                    ContainerReporter.reportContainerDuration(ContainerConfigManager.getInstance().getQtitansContainerParams(),
                            System.currentTimeMillis() - mStartTime - onBackgroundTimeAll,
                            onBackgroundTimeAll, onBackgroundTimeAllStr, false);
                }
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansMonitor", e);
        }
    }
}
