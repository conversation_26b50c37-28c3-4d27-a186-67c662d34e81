package com.meituan.android.qtitans.container.qqflex;

import android.content.Context;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.meituan.android.base.BaseConfig;
import com.meituan.android.hades.impl.BuildConfig;
import com.meituan.android.hades.impl.config.HadesConfigMgr;
import com.meituan.android.hades.impl.model.BaseResponse;
import com.meituan.android.hades.impl.net.HadesCallFactory;
import com.meituan.android.hades.impl.net.HadesNetParamMockInterceptor;
import com.meituan.android.hades.impl.net.HadesNetProcessInterceptor;
import com.meituan.android.hades.impl.net.HadesRetrofit;
import com.meituan.android.hades.impl.net.HadesRetrofitService;
import com.meituan.android.hades.impl.net.HadesUrlControlInterceptor;
import com.meituan.android.hades.impl.net.OnlineLoggingInterceptor;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.UUIDUtils;
import com.meituan.android.qtitans.container.qqflex.lucykin.QtitansLuckinPreRequestManager;
import com.meituan.android.qtitans.container.qqflex.lucykin.model.LuckinMainPage;
import com.meituan.android.qtitans.container.qqflex.lucykin.model.LuckinQuerySpuDetail;
import com.meituan.android.qtitans.container.qqflex.model.QtitansFlexPageData;
import com.meituan.android.singleton.CityControllerSingleton;
import com.meituan.passport.UserCenter;
import com.sankuai.meituan.retrofit2.Call;
import com.sankuai.meituan.retrofit2.Converter;
import com.sankuai.meituan.retrofit2.Retrofit;
import com.sankuai.meituan.retrofit2.converter.gson.GsonConverterFactory;

import java.util.HashMap;
import java.util.Map;

public class QtitansFlexRetrofit {
    private volatile static QtitansFlexRetrofit sInstance;
    private Context context;
    private Retrofit retrofit;
    private String packageName;
    private Converter.Factory factory = GsonConverterFactory.create(getJsonBuilder());

    private Gson getJsonBuilder() {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(QtitansFlexPageData.class, new QtitansFlexPageData());
        if (HadesUtils.isEnableLuckinNativePage()) {
            gsonBuilder.registerTypeAdapter(LuckinMainPage.class, new LuckinMainPage());
            gsonBuilder.registerTypeAdapter(LuckinQuerySpuDetail.class, new LuckinQuerySpuDetail());
        }
        return gsonBuilder.create();
    }
    private QtitansFlexRetrofit(Context context, String url) {
        initRetrofit(context, url);
    }

    private QtitansFlexRetrofit(Context context) {
        initRetrofit(context, "");
    }

    private void initRetrofit(Context context, String url) {
        if (context == null) {
            context = HadesUtils.getContext();
        }
        this.context = context.getApplicationContext();
        Retrofit.Builder builder = new Retrofit.Builder()
                .callFactory(HadesCallFactory.getInstance())
                .addConverterFactory(factory)
                .addInterceptor(new HadesNetProcessInterceptor())
                .addInterceptor(new HadesNetParamMockInterceptor())
                .addInterceptor(HadesUrlControlInterceptor.getInstance());
        String requestUrl = url;
        if (TextUtils.isEmpty(url)) {
            requestUrl = HadesRetrofit.getInstance(context).getUrl() + "/";
        }
        builder.baseUrl(requestUrl);
        if (HadesConfigMgr.getInstance(context).isNetLoganSwitchOn()) {
            builder.addInterceptor(OnlineLoggingInterceptor.getInstance());
        }
        retrofit = builder.build();
        packageName = this.context.getPackageName();
    }

    public static QtitansFlexRetrofit getInstance(Context context) {
        if (null == sInstance) {
            synchronized (HadesRetrofit.class) {
                if (null == sInstance) {
                    sInstance = new QtitansFlexRetrofit(context);
                }
            }
        }
        return sInstance;
    }

    public static QtitansFlexRetrofit getInstance(@NonNull Context context, String url) {
        return new QtitansFlexRetrofit(context, url);
    }

    public Call<BaseResponse<QtitansFlexPageData>> getSupplyDetails(@NonNull HashMap<String, Object> map) {
        Map<String, String> queryParams = HadesRetrofit.getInstance(context).obtainCommonQueryParams();
        queryParams.put("userId", String.valueOf(UserCenter.getInstance(context).getUserId()));
        queryParams.put("cityId", String.valueOf(CityControllerSingleton.getInstance().getCityId()));
        queryParams.put("bizName", "daocan");
        queryParams.put("client", "android");
        queryParams.put("version", BaseConfig.versionName);
        queryParams.put("versionName", BaseConfig.versionName);
        queryParams.put("token", UserCenter.getInstance(context).getToken());
        map.putAll(queryParams);
        return retrofit.create(HadesRetrofitService.class).getSupplyDetails(map);

    }

    public Call<BaseResponse<LuckinMainPage>> getSaveMoneyDealQuery(@NonNull HashMap<String, Object> map) {
        Map<String, String> queryParams = HadesRetrofit.getInstance(context).obtainCommonQueryParams();
        queryParams.put("userId", String.valueOf(UserCenter.getInstance(context).getUserId()));
        queryParams.put("cityId", String.valueOf(CityControllerSingleton.getInstance().getCityId()));
        queryParams.put("bizName", "daocan");
        queryParams.put("client", "android");
        queryParams.put("versionName", BaseConfig.versionName);
        queryParams.put("token", UserCenter.getInstance(context).getToken());
        if (BuildConfig.LOG_ENABLE) {
            queryParams.put("cityId", "1");
        }
        map.put("isNative", true);
        map.putAll(queryParams);
        return retrofit.create(HadesRetrofitService.class).getSaveMoneyDealQuery(map);
    }

    public Call<BaseResponse<LuckinMainPage>> getSaveMoneyOrderQuery(String brandId) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", String.valueOf(UserCenter.getInstance(context).getUserId()));
        queryParams.put("cityId", String.valueOf(CityControllerSingleton.getInstance().getCityId()));
        queryParams.put("offset", 0);
        queryParams.put("limit", 100);
        queryParams.put("brandId", brandId);
        queryParams.put("isNative", true);
        JsonArray jsonArray = new JsonArray();
        jsonArray.add(1);
        jsonArray.add(65);
        queryParams.put("partnerIdList", jsonArray);
        return retrofit.create(HadesRetrofitService.class).getSaveMoneyOrderQuery(queryParams);

    }

    public Call<BaseResponse<LuckinMainPage>> getNearByShops(@NonNull LuckinMainPage data, double lat, double lng) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", String.valueOf(UserCenter.getInstance(context).getUserId()));
        queryParams.put("pageSize", 50);
        queryParams.put("token", UserCenter.getInstance(context).getToken());
        queryParams.put("appVersion", BaseConfig.versionName);
        queryParams.put("locationCityId", CityControllerSingleton.getInstance().getCityId());
        queryParams.put("selectCityId", CityControllerSingleton.getInstance().getCityId());
        queryParams.put("os", 1);
        queryParams.put("platform", 10);
        queryParams.put("dishSource", 10);
        queryParams.put("pageNo", 1);
        queryParams.put("kkfrom", "kk");
        queryParams.put("shopIdEncrypt", data.shopIdEncrypt);
        queryParams.put("brandId", Integer.parseInt(QtitansLuckinPreRequestManager.getInstance().getBrandId()));
        queryParams.put("uuid", UUIDUtils.getUUID(context));
        queryParams.put("shopId", data.shopId);
        queryParams.put("latitude", lat);
        queryParams.put("longitude", lng);
        return retrofit.create(HadesRetrofitService.class).getNearedByShops(queryParams);

    }

    public Call<BaseResponse<LuckinQuerySpuDetail>> querySpuDetail(@NonNull HashMap<String, Object> map) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", String.valueOf(UserCenter.getInstance(context).getUserId()));
        queryParams.put("userid", String.valueOf(UserCenter.getInstance(context).getUserId()));
        queryParams.put("token", UserCenter.getInstance(context).getToken());
        queryParams.put("appVersion", BaseConfig.versionName);
        queryParams.put("locationCityId", CityControllerSingleton.getInstance().getCityId());
        queryParams.put("selectCityId", CityControllerSingleton.getInstance().getCityId());
        queryParams.put("os", 1);
        queryParams.put("uuid", UUIDUtils.getUUID(context));
        queryParams.put("platform", 10);
        queryParams.put("dishSource", 10);
        queryParams.put("kkfrom", "kk");
        queryParams.put("categoryType",-99);
        queryParams.putAll(map);
        return retrofit.create(HadesRetrofitService.class).querySpuDetail(queryParams);
    }

    public Call<BaseResponse> reportToolApiLimit(String url) {
        Map<String, Object> map = new HashMap<>();
        map.put("url", url);
        return retrofit.create(HadesRetrofitService.class).reportToolLimit(map);
    }

}
