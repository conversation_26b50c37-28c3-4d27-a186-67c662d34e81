package com.meituan.android.qtitans.container.qqflex.lucykin;


import static com.meituan.android.hades.dyadater.container.ContainerConst.BABEL_TYPE_SMC_REPORT;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.View;

import com.meituan.android.hades.impl.report.BabelHelper;
import com.meituan.android.qtitans.container.qqflex.lucykin.model.LuckinMainPage;
import com.meituan.android.qtitans.container.reporter.ContainerReporter;

import java.util.HashMap;

public class QtitansLuckinHistoryOrderFragment extends QtitansLuckinFlexFragment {

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        updateFeedBackgroundColor(feedBackgroundColor, feedTopRadius);
        requestOrder();

    }

    private void requestOrder() {
        try {
            QtitansLuckinPreRequestManager.getInstance().requestLuckinHostoryOrderQuery(this.getContext(), new IFlexLuckinPageRequestCallback() {
                @Override
                public void onSuccess(LuckinMainPage data) {
                    if (data == null) return;
                    QtitansLuckinHistoryOrderFragment.this.data = data.myHisOrderList;
                    updateListData(data.myHisOrderList);
                    updateFeedBackgroundColor(data.feedBackgroundColor, feedTopRadius);
                }

                @Override
                public void onFailed(String message) {
                    //
                }
            });

        } catch (Throwable e) {
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", "QtitansLuckinHistoryOrderFragment" + ": onViewCreated failed: " + e.getMessage());
            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
        }
    }

    @Override
    protected void reportPV() {
        ContainerReporter.reportLuckinFragmentPv("OrderList", mTargetUrl, false);
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        if (!hidden) {
            requestOrder();
        }
    }
}
