package com.meituan.android.qtitans.container;

import android.content.Context;
import android.net.Uri;
import android.support.v4.app.Fragment;
import android.text.TextUtils;
import android.util.Log;

import com.meituan.android.hades.BuildConfig;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.dyadater.container.ContainerType;
import com.meituan.android.hades.dyadater.container.interfaces.IContainerView;
import com.meituan.android.qtitans.container.mrn.QtitansMrnFragment;
import com.meituan.android.qtitans.container.msc.QtitansMscFragment;
import com.meituan.android.qtitans.container.qqflex.lucykin.QtitansLuckinContainerFragment;
import com.meituan.android.qtitans.container.web.QtitansWebFragment;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.router.MMPRouterManager;

public class QtitansContainerFragment {

    private static final String TAG = "HadesContainerFragment";

    public static Fragment createFragment(Context context, ContainerType containerType, String uriStr, String visitType,
                                           IContainerView containerView) {
        try {
            if (context == null || containerType == null || TextUtils.isEmpty(uriStr)) {
                return null;
            }
            if (BuildConfig.LOG_ENABLE) {
                Log.d(TAG, "createFragment: containerType " + containerType + " uriStr =" + uriStr + " ");
            }
            switch (containerType) {
                case MSC:
                case MMP:
                    String targetUrl = uriStr;
                    if (containerType == ContainerType.MMP) {
                        targetUrl = mmpToMscUri(uriStr);
                        Logger.d("MMP2MSC"+"mmp path:"+uriStr+" replace url "+ targetUrl);
                    }
                    QtitansMscFragment fragmentMsc = QtitansMscFragment.createInstance(targetUrl, visitType);
                    fragmentMsc.setLoadingViewListener(containerView);
                    return fragmentMsc;
                case MRN:
                    QtitansMrnFragment fragmentMrn = QtitansMrnFragment.createInstance(uriStr);
                    fragmentMrn.setLoadingViewListener(containerView);
                    return fragmentMrn;
                case WEB:
                    return QtitansWebFragment.createInstance(context, uriStr, containerView, visitType);
                case FLEX:
                    QtitansLuckinContainerFragment luckinContainerFragment = new QtitansLuckinContainerFragment();
                    luckinContainerFragment.setmTargetUrl(uriStr);
                    luckinContainerFragment.setiContainerView(containerView);
                    return luckinContainerFragment;
                default:
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QtitansContainerFragment", e);
        }
        return null;
    }

    public static String mmpToMscUri(String url) {
        Uri uri = Uri.parse(url);
        String appId = uri.getQueryParameter(MSCParams.APP_ID);
        String targetAppId = MMPRouterManager.getMSCAppIdRouteByMMP(appId);
        if (TextUtils.isEmpty(targetAppId)) {
            targetAppId = appId;
            Logger.d("MMP2MSC", "appId replace failed");
        }
        final Uri processedUri = Uri.parse(url.replace("appId="+appId, "appId="+targetAppId));
        processedUri.buildUpon()
                .path("msc");
       return processedUri.toString();
    }
}
