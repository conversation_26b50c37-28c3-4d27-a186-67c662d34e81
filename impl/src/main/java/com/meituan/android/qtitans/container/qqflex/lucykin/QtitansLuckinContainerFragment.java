package com.meituan.android.qtitans.container.qqflex.lucykin;

//import static com.meituan.android.hades.router.tt.TTRedirectHandler.BABEL_TYPE_SMC_REPORT;

import static com.meituan.android.hades.dyadater.container.ContainerConst.BABEL_TYPE_SMC_REPORT;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.support.annotation.Keep;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.constraint.ConstraintLayout;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentManager;
import android.support.v4.app.FragmentTransaction;
import android.support.v4.content.Loader;
import android.text.TextUtils;
import android.text.format.DateUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.meituan.android.common.locate.LoadConfig;
import com.meituan.android.common.locate.LocationLoaderFactory;
import com.meituan.android.common.locate.MasterLocator;
import com.meituan.android.common.locate.MasterLocatorFactory;
import com.meituan.android.common.locate.MasterLocatorFactoryImpl;
import com.meituan.android.common.locate.MtLocation;
import com.meituan.android.common.locate.loader.LoadConfigImpl;
import com.meituan.android.common.locate.util.LocationUtils;
import com.meituan.android.dynamiclayout.controller.LayoutController;
import com.meituan.android.dynamiclayout.controller.event.Event;
import com.meituan.android.dynamiclayout.controller.event.EventListener;
import com.meituan.android.dynamiclayout.controller.event.EventScope;
import com.meituan.android.hades.dyadater.BabelHelperAdapter;
import com.meituan.android.hades.dyadater.container.interfaces.OnDialogClickListener;
import com.meituan.android.hades.dyadater.infrastruct.utils.UUIDUtils;
import com.meituan.android.hades.impl.BuildConfig;
import com.meituan.android.hades.impl.R;
import com.meituan.android.hades.impl.report.BabelHelper;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.impl.utils.StorageHelper;
import com.meituan.android.hades.router.RouterManager;
import com.meituan.android.pin.Pin;
import com.meituan.android.pin.PinCallback;
import com.meituan.android.privacy.interfaces.PermissionGuard;
import com.meituan.android.privacy.locate.MtLocationLoaderWrapper;
import com.meituan.android.hades.dyadater.container.interfaces.IContainerView;
import com.meituan.android.qtitans.container.qqflex.QFlexDialog;
import com.meituan.android.qtitans.container.qqflex.QtitansFlexCardView;
import com.meituan.android.qtitans.container.qqflex.listview.QtitansFlexData;
import com.meituan.android.qtitans.container.qqflex.lucykin.model.LuckinMainPage;
import com.meituan.android.qtitans.container.qqflex.lucykin.model.LuckinQuerySpuDetail;
import com.meituan.android.hades.dyadater.container.PreloadState;
import com.meituan.android.qtitans.container.reporter.ContainerReporter;
import com.meituan.android.singleton.ContextSingleton;
import com.meituan.passport.UserCenter;
import com.sankuai.titans.result.TitansPermissionUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.HashMap;

import okhttp3.OkHttpClient;
import rx.Subscription;

@Keep
public class QtitansLuckinContainerFragment extends Fragment {
    private static final String TAG = "QtitansLuckinContainerFragment";
    private String topSpu;
    private boolean refreshTopSpu = false;
    private Subscription loginSubscription;
    private FrameLayout fragmentContainer;
    private ConstraintLayout rootView;
    private LinearLayout titleBarView;
    private QtitansFlexCardView titleFlexView;
    private LinearLayout bottomNavBarView;
    private QtitansFlexCardView bottomFlexNavBarView;

    private LuckinFragmentEnum fragmentEnum = LuckinFragmentEnum.DEAL;
    private EventListener switchTabEventListener;
    private QtitansLuckinFlexFragment mainFragment;
    private QtitansLuckinHistoryOrderFragment orderFragment;
    private QtitansLuckinShopFragment shopFragment;
    private EventListener onLoginStatusChangedEvent;
    private EventListener jumpLoginEvent;
    private EventListener openWeiChat;
    private EventListener luckinEventClickListener;
    private boolean isRefreshLocationPermission;
    private boolean isShowLogin = false;
    private QFlexDialog changeShopDialog;
    private QFlexDialog bizOffDialog;
    private QFlexDialog luckinGuideDialog;

    public IContainerView getiContainerView() {
        return iContainerView;
    }

    public void setiContainerView(IContainerView iContainerView) {
        this.iContainerView = iContainerView;
    }

    private IContainerView iContainerView;

    public void setmTargetUrl(String mTargetUrl) {
        this.mTargetUrl = mTargetUrl;
    }

    private String mTargetUrl;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Logger.d(TAG, "onCreateView");
       View  view =  inflater.inflate(R.layout.activity_qitans_flex_lukin_page, container, false);
       try {
           mainFragment = new QtitansLuckinFlexFragment();
           orderFragment = new QtitansLuckinHistoryOrderFragment();
           shopFragment = new QtitansLuckinShopFragment();
           initLuckinView(view);
       } catch (Throwable e) {
           HashMap<String, Object> info = new HashMap<>();
           info.put("reason", TAG+": onCreateView failed: " + e.getMessage());
           BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
       }
        return view;
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Logger.d(TAG, "onViewCreated");
        try {
            initData();
        } catch (Throwable e) {
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", TAG+": onViewCreated failed: " + e.getMessage());
            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        try {
            if (!UserCenter.getInstance(this.getContext()).isLogin() && loginSubscription == null) {
                loginSubscription = UserCenter.getInstance(this.getContext()).loginEventObservable().subscribe(loginEvent -> {
                    Logger.d(TAG, "login status changed:" + loginEvent.type);
                    sendLoginEvent(loginEvent.type == UserCenter.LoginEventType.login);
                    if (loginEvent.type == UserCenter.LoginEventType.login) {
                        handleRereshIfNeed(this.getContext(), "",-1, -1);
                    }
                });
            }
            if (!isRefreshLocationPermission) {
                requestLocationPermission();
                isRefreshLocationPermission = true;
            }
        } catch (Throwable e) {
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", TAG+": onResume failed: " + e.getMessage());
            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
        }

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        try {
            if (loginSubscription != null && !loginSubscription.isUnsubscribed()) {
                Logger.d(TAG, "unsubscribe login");
                loginSubscription.unsubscribe();
                loginSubscription = null;
            }
        } catch (Throwable e) {
            QQThrowableReporter.report(TAG, e, true);
        }

    }

    public boolean onBackPressed() {
        if (getFragmentEnum() == LuckinFragmentEnum.SHOP) {
            //门店页返回，先回到首页
            switchFragment(LuckinFragmentEnum.DEAL.name(), "");
            return true;
        }
        return false;
    }

    private void requestLocationPermission() {
        TitansPermissionUtil.requestPermission(this.getActivity(), PermissionGuard.PERMISSION_LOCATION_ONCE, "pt-6db9656d437e0ec1", false, (granted, code) -> {
            if (granted) {
                LocationUtils.setChannel(LocationUtils.CHANNEL.MEITUAN);
                LocationUtils.setUuid(UUIDUtils.getUUID(QtitansLuckinContainerFragment.this.getContext()));
                Context context = new WeakReference<Context>(QtitansLuckinContainerFragment.this.getContext()).get();
                MasterLocator masterLocator = new MasterLocatorFactoryImpl().createMasterLocator(context, new OkHttpClient(), null, null, "", MasterLocatorFactory.REQUEST_MEITUAN_CITYID, MasterLocatorFactory.PROCESS_MAIN);
                MtLocationLoaderWrapper locationLoaderWrapper = MtLocationLoaderWrapper.with(this, "pt-6db9656d437e0ec1", masterLocator);
                if (locationLoaderWrapper != null) {
                    LoadConfig loadConfig = new LoadConfigImpl();
                    Loader<MtLocation> locationLoader = locationLoaderWrapper.createMtLocationLoader(context, LocationLoaderFactory.LoadStrategy.normal, loadConfig);
                    if (locationLoader != null) {
                        Loader.OnLoadCompleteListener<MtLocation> onLoadCompleteListener = new Loader.OnLoadCompleteListener<MtLocation>() {
                            @Override
                            public void onLoadComplete(@NonNull Loader<MtLocation> loader, @Nullable MtLocation location) {
                                if (location != null && location.getStatusCode() == 0) {
                                    double lat = location.getLatitude();
                                    double lng = location.getLongitude();
                                    handleRereshIfNeed(QtitansLuckinContainerFragment.this.getContext(), "", lat, lng);
                                    try {
                                        locationLoader.unregisterListener(this);
                                    } catch (Throwable e) {
                                        Logger.e(TAG, "location unregister failed: "+e.getMessage());
                                    }

                                } else {
                                    Logger.d(TAG, "location failed");
                                }
                            }
                        };
                        locationLoader.registerListener(0, onLoadCompleteListener);
                        locationLoader.startLoading();
                    }
                }
            }
        });
    }

    private void tryShowSubscribe() {
        if (TextUtils.isEmpty(QtitansLuckinContainerFragment.this.mTargetUrl)) return;
        try {
            Uri uri = Uri.parse(QtitansLuckinContainerFragment.this.mTargetUrl);
            String webUrl = uri.getQueryParameter("url");
            if (TextUtils.isEmpty(webUrl)) return;
            String subscribe = Uri.parse(webUrl).getQueryParameter("subscribe");
            if (!TextUtils.isEmpty(subscribe) && "1".equals(subscribe)) {

                Pin.checkFW(ContextSingleton.getInstance(), 90001, "1", 60, new PinCallback() {
                    @Override
                    public void onSuccess(JSONObject jsonObject) {
                        Pin.process(new WeakReference<>(QtitansLuckinContainerFragment.this.getActivity()), 90001, "1", 60, new PinCallback() {
                            @Override
                            public void onSuccess(JSONObject jsonObject) {
                                Logger.d(TAG, "show subscribe dialog success!");
                            }

                            @Override
                            public void onError(int errorCode, String errMsg) {
                                HashMap<String, Object> info = new HashMap<>();
                                info.put("reason", TAG + ":Pin process failed: " + errorCode + ":" + errMsg);
                                BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
                            }
                        });

                    }

                    @Override
                    public void onError(int errorCode, String errMsg) {
                        HashMap<String, Object> info = new HashMap<>();
                        info.put("reason", TAG + ":Pin checkFW failed: " + errorCode + ":" + errMsg);
                        BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
                    }
                });

            }

        } catch (Throwable e) {
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", TAG + ": push show subscribe failed: " + e.getMessage());
            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
        }
    }

    private void initLuckinView(View view) {
        fragmentContainer = view.findViewById(R.id.luckin_fragment_container);
        rootView = view.findViewById(R.id.lukyin_root_view);
        titleBarView = view.findViewById(R.id.luckin_title_bar_container);
        bottomNavBarView = view.findViewById(R.id.bottom_nav_container);
        titleFlexView = new QtitansFlexCardView(view.getContext());
        ViewGroup.LayoutParams titleLayoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        titleFlexView.setLayoutParams(titleLayoutParams);
        View statusView = view.findViewById(R.id.luckin_status_bar_view);
        ViewGroup.LayoutParams layoutParams = statusView.getLayoutParams();
        if (layoutParams != null) {
            layoutParams.height = HadesUtils.getStatusBarHeight(view.getContext());
            statusView.setLayoutParams(layoutParams);
        }
        switchTabEventListener = new EventListener("switchTab", EventScope.GLOBAL, null) {
            @Override
            public void handleEvent(Event event, LayoutController layoutController) {
                try {
                    JSONObject data = event.getData();
                    if (data != null && data.has("action")){
                        String action = data.getString("action");
                        Logger.d(TAG, "action:"+action+" enum:"+getFragmentEnum().name());
                        String shopId = "";
                        try {
                            shopId = data.getString("shopIdStr");
                        } catch (JSONException e) {

                        }
                        switchFragment(action, shopId);
                    }

                } catch (Throwable e) {
                    QQThrowableReporter.report(TAG, e, true);
                }
            }
        };
        openWeiChat = new EventListener("openMiniProgram", EventScope.GLOBAL, null) {
            @Override
            public void handleEvent(Event event, LayoutController layoutController) {
                try {
                    JSONObject data = event.getData();
                    if (!data.has("miniProgramId")) return;
                    String miniProgramId = data.getString("miniProgramId");
                    String wmpPath = "";
                    if (data.has("wmpPath")) {
                        wmpPath = data.getString("wmpPath");
                    }
                    HadesUtils.jumpToWmp(QtitansLuckinContainerFragment.this.getContext(), miniProgramId, wmpPath, "release");
                } catch (Throwable e) {

                }

            }
        };
        jumpLoginEvent = new EventListener("jumpLogin", EventScope.GLOBAL, null) {
            @Override
            public void handleEvent(Event event, LayoutController layoutController) {
                JSONObject data = event.getData();
                Logger.d(TAG, "jumpLogin"+data.toString());
                if (data.has("login") && !UserCenter.getInstance(QtitansLuckinContainerFragment.this.getContext()).isLogin()) {
                    jumpLogin(QtitansLuckinContainerFragment.this.getContext());
                }
            }
        };
        luckinEventClickListener = new EventListener("luckinclickevent", EventScope.GLOBAL, null) {
            @Override
            public void handleEvent(Event event, LayoutController layoutController) {
                try {
                    JSONObject data = event.getData();
                    if (data == null) return;
                    String spuId = data.getString("spuId");
                    String skuId = data.getString("skuId");
                    String circledSkuIds = data.getString("circledSkuIds");
                    String poiId = data.getString("poiId");
                    int position = data.getInt("position");
                    String jumpUrl = data.getString("jumpUrl");
                    if (UserCenter.getInstance(QtitansLuckinContainerFragment.this.getContext()).isLogin()) {

                        QtitansLuckinPreRequestManager.getInstance().querySpuDetail(QtitansLuckinContainerFragment.this.getContext(), spuId, skuId, poiId, circledSkuIds, new IFlexLuckinSpuDetailCallback() {
                            @Override
                            public void onSuccess(LuckinQuerySpuDetail data) {
                                if (data != null && data.isSoldOut) {
                                    if (mainFragment != null && position >= 0) {
                                        HashMap<String, Object> map = new HashMap<>();
                                        map.put("isSoldOut", data.isSoldOut);
                                        mainFragment.updateItemData(position, map);
                                        reportBuyButtonMc(position, "已售罄");

                                    }
                                } else {
                                    if (!TextUtils.isEmpty(jumpUrl)) {
                                        RouterManager.jumpToBizActivity(QtitansLuckinContainerFragment.this.getContext(), jumpUrl);
                                        reportBuyButtonMc(position, "通过校验");
                                    }
                                }
                            }

                            @Override
                            public void onFailed(String message) {
                                if (mainFragment != null && position >= 0) {
                                    HashMap<String, Object> map = new HashMap<>();
                                    map.put("isSoldOut", true);
                                    mainFragment.updateItemData(position, map);
                                    reportBuyButtonMc(position, "已下架");
                                }
                            }
                        });


                    } else {
                        jumpLogin(QtitansLuckinContainerFragment.this.getContext());
                        reportBuyButtonMc(position, "点击下单-未登录");
                    }
                } catch (Throwable e) {
                    QQThrowableReporter.report(TAG, e, true);
                }
            }
        };
        bottomFlexNavBarView = new QtitansFlexCardView(view.getContext());
        ViewGroup.LayoutParams navLayoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        bottomFlexNavBarView.setLayoutParams(navLayoutParams);

        changeShopDialog = new QFlexDialog(view.getContext(), new OnDialogClickListener() {
            @Override
            public void onPositiveClick(JSONObject data) {
                //确认门店
                if (data != null) {
                    String shopId = data.optString("shopId");
                    Logger.d(TAG, "确认门店点击: shopId=" + shopId);
                    if (!TextUtils.isEmpty(shopId)) {
                        handleRereshIfNeed(view.getContext(), shopId, -1, -1);
                    }
                }
                changeShopDialog.dismiss();
            }

            @Override
            public void onNegativeClick(JSONObject data) {
                //切换门店
                Logger.d(TAG, "切换门店点击");
                switchFragment("SHOP", "");
                changeShopDialog.dismiss();
            }
        });
        bizOffDialog = new QFlexDialog(view.getContext(), new OnDialogClickListener() {
            @Override
            public void onPositiveClick(JSONObject data) {
                //知道了
                Logger.d(TAG, "知道了点击");
                bizOffDialog.dismiss();
            }

            @Override
            public void onNegativeClick(JSONObject data) {

            }
        });

        luckinGuideDialog = new QFlexDialog(getContext(), null, true);
    }

    private void reportBuyButtonMc(int position, String title) {
        try {
            if (mainFragment.data != null && mainFragment.data.get(position) != null) {
                ContainerReporter.reportLuckinClickDealBuy(mainFragment.data.get(position).jsonObject, title);
            }
        } catch (Throwable e) {
            QQThrowableReporter.report(TAG, e, true);
        }

    }

    public void updateLuckInView(LuckinMainPage mainPage, boolean isCache) {
        if (mainPage == null) {
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason",  "updateLuckInView failed main page null ");
            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
            return;
        }
        if (mainPage.spuDetailList == null || mainPage.spuDetailList.size() == 0) {
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason",  "updateLuckInView no data");
            BabelHelperAdapter.log(BABEL_TYPE_SMC_REPORT, info);
            return;
        }
        onHideLoadingView();
        HadesUtils.runOnMainThread(()-> realUpdateView(mainPage, isCache));

    }

    private void realUpdateView(LuckinMainPage mainPage, boolean isCache) {
        try {
            if (!TextUtils.isEmpty(mainPage.pageColor) && rootView != null) {
                try {
                    rootView.setBackgroundColor(Color.parseColor(mainPage.pageColor));
                } catch (Throwable e) {
                    QQThrowableReporter.report(TAG, e, true);
                }
            }
            if (titleBarView != null) {
                titleBarView.removeAllViews();
                titleBarView.addView(titleFlexView);
                titleFlexView.showView(mainPage.titleBarData);
                titleFlexView.addEventListener(openWeiChat);
                titleFlexView.addEventListener(jumpLoginEvent);
                titleFlexView.addEventListener(luckinEventClickListener);
            }
            if (bottomNavBarView != null) {
                bottomNavBarView.removeAllViews();
                bottomNavBarView.addView(bottomFlexNavBarView);
                bottomFlexNavBarView.showView(mainPage.bottomNavData);
                bottomFlexNavBarView.addEventListener(switchTabEventListener);
            }
            if (fragmentContainer != null && mainFragment != null) {
                mainFragment.updateListData(mainPage.spuDetailList);
                mainFragment.updateFeedBackgroundColor(mainPage.feedBackgroundColor, mainPage.feedBorderTopRadius);
                mainFragment.setCache(isCache);
                Logger.d(TAG, "show fragment current fragment enum: "+ getFragmentEnum());
                if (getFragmentEnum() == LuckinFragmentEnum.DEAL) {
                    mainFragment.setTargetUrl(mTargetUrl);
                    showFragment(mainFragment, LuckinFragmentEnum.DEAL);
                }
            }
            if (titleBarView != null) {
                titleBarView.postDelayed(()->{
                    sendLoginEvent(UserCenter.getInstance(titleBarView.getContext()).isLogin());
                    sendLoginEvent(true);
                    if (QtitansLuckinPreRequestManager.getInstance().getShopPage() != null) {
                        sendShopCountChangeEvent(QtitansLuckinPreRequestManager.getInstance().getShopPage(), titleFlexView);
                    }
                }, 100);
            }

        } catch (Throwable e) {
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason",  "updateLuckInView failed: "+ e.getMessage());
            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
        }
    }

    private void initData() {
        if (HadesUtils.enableLuckinPageCache() && QtitansLuckinPreRequestManager.getInstance().getMainPage() == null && QtitansLuckinPreRequestManager.getInstance().getCacheData() != null) {
            Logger.d(TAG, "using cache data");
            updateLuckInView(QtitansLuckinPreRequestManager.getInstance().getCacheData(), true);
        }
        if (QtitansLuckinPreRequestManager.getInstance().getMainPage() != null) {
            Logger.d(TAG, "using request data");
            requestNearbyShopList(QtitansLuckinPreRequestManager.getInstance().getShopPage());
            updateLuckInView(QtitansLuckinPreRequestManager.getInstance().getMainPage(), false);
        } else if (QtitansLuckinPreRequestManager.getInstance().getPreloadState() != PreloadState.PRELOAD_FAILED) {
            Logger.d(TAG, "pre request start, data not ready, set callback");
            QtitansLuckinPreRequestManager.getInstance().setPreRequestCallback(new IFlexLuckinPageRequestCallback() {
                @Override
                public void onSuccess(LuckinMainPage data) {
                    requestNearbyShopList(data);
                    updateLuckInView(data, false);
                }

                @Override
                public void onFailed(String message) {
                    requestLuckinPage();
                }
            });
        } else {
            Logger.d(TAG, "real request data");
            requestLuckinPage();
        }
    }

    public void requestNearbyShopList(LuckinMainPage data) {
        QtitansLuckinPreRequestManager.getInstance().requestNearbyShops(this.getContext(), data, new IFlexLuckinPageRequestCallback() {
            @Override
            public void onSuccess(LuckinMainPage data) {
                if (titleBarView != null) {
                    titleBarView.postDelayed(()->{
                        initShopCountListener(data);
                    }, 100);
                }

            }

            @Override
            public void onFailed(String message) {
            Logger.d(TAG, "request shop list failed");
            }
        });
    }

    private void initShopCountListener(LuckinMainPage data) {
        try {
            if (data != null && data.shopList != null) {
                sendShopCountChangeEvent(data, titleFlexView);
                showChangeShopDialogIfNeed(data);
                showBizOffDialogIfNeed(data);
            }
        }catch (Throwable e) {
            QQThrowableReporter.report(TAG, e, true);
        }

    }

    public void switchFragment(String action, String shopId) {
        switch (action) {
            case "DEAL":
                if (getFragmentEnum() != LuckinFragmentEnum.DEAL) {
                    bottomNavBarView.setVisibility(View.VISIBLE);
                    setFragmentEnum(LuckinFragmentEnum.DEAL);
                    QtitansLuckinFlexFragment fragment = getFragment(LuckinFragmentEnum.DEAL);
                    if (fragment != null) {
                        mainFragment = fragment;
                    }
                    if (!TextUtils.isEmpty(shopId) &&  mTargetUrl != null) {
                        handleRereshIfNeed(QtitansLuckinContainerFragment.this.getContext(), shopId, -1, -1);
                    };
                    showFragment(fragment, LuckinFragmentEnum.DEAL);
                }
                break;
            case "ORDER":
                if (getFragmentEnum() != LuckinFragmentEnum.ORDER) {
                    setFragmentEnum(LuckinFragmentEnum.ORDER);
                    bottomNavBarView.setVisibility(View.VISIBLE);
                    QtitansLuckinFlexFragment fragment = getFragment(LuckinFragmentEnum.ORDER);
                    if (fragment != null) {
                        orderFragment = (QtitansLuckinHistoryOrderFragment) fragment;
                    }

                    showFragment(fragment, LuckinFragmentEnum.ORDER);
                }
                break;
            case "SHOP":
                bottomNavBarView.setVisibility(View.GONE);
                if (getFragmentEnum() != LuckinFragmentEnum.SHOP) {
                    setFragmentEnum(LuckinFragmentEnum.SHOP);
                    QtitansLuckinFlexFragment fragment = getFragment(LuckinFragmentEnum.SHOP);
                    if (fragment != null) {
                        shopFragment = (QtitansLuckinShopFragment)fragment;
                    }
                    showFragment(fragment, LuckinFragmentEnum.SHOP);
                }
            default://do nothing
                break;
        }
    }

    private void requestLuckinPage() {
        if (mTargetUrl != null) {
            QtitansLuckinPreRequestManager.getInstance().requestSaveMoneyDealQuery(QtitansLuckinContainerFragment.this.getContext(), mTargetUrl, new IFlexLuckinPageRequestCallback() {
                @Override
                public void onSuccess(LuckinMainPage data) {
                    requestNearbyShopList(data);
                    updateLuckInView(data, false);
                }

                @Override
                public void onFailed(String message) {
                    Logger.d(TAG, "real request failed:"+message);
                    RouterManager.jumpToMTHome(QtitansLuckinContainerFragment.this.getContext());
                }
            });
        }

    }

    private void showFragment(Fragment fragment, LuckinFragmentEnum fragmentEnum) {
        try {
            Logger.d(TAG, "show fragment");
            if (getActivity() == null || !isAdded()) {
                Logger.d(TAG, "fragment not attach to activity");
                return;
            }
            FragmentManager fm = this.getChildFragmentManager();
            FragmentTransaction fragmentTransaction = fm.beginTransaction();
            Fragment fragmentDeal = fm.findFragmentByTag(LuckinFragmentEnum.DEAL.name());
            Fragment fragmentShop = fm.findFragmentByTag(LuckinFragmentEnum.SHOP.name());
            Fragment fragmentOrder = fm.findFragmentByTag(LuckinFragmentEnum.ORDER.name());
            if (fragmentEnum == LuckinFragmentEnum.DEAL ) {
                if (fragmentDeal == null) {
                    fragmentDeal = fragment;
                    fragmentTransaction.add(R.id.luckin_fragment_container, fragment, LuckinFragmentEnum.DEAL.name());
                }
                if (QtitansLuckinPreRequestManager.getInstance().getMainPage() != null && fragmentDeal instanceof QtitansLuckinFlexFragment) {
                    ((QtitansLuckinFlexFragment)fragmentDeal).updateListData(QtitansLuckinPreRequestManager.getInstance().getMainPage().spuDetailList);
                }
                fragmentTransaction.show(fragmentDeal);
                if (fragmentShop != null) {
                    fragmentTransaction.hide(fragmentShop);
                }
                if (fragmentOrder != null) {
                    fragmentTransaction.hide(fragmentOrder);
                }
            } else if (fragmentEnum == LuckinFragmentEnum.ORDER) {
                if (fragmentOrder == null) {
                    fragmentOrder = fragment;
                    fragmentTransaction.add(R.id.luckin_fragment_container, fragment, LuckinFragmentEnum.ORDER.name());
                }
                fragmentTransaction.show(fragmentOrder);
                if (fragmentDeal != null) {
                    fragmentTransaction.hide(fragmentDeal);
                }
                if (fragmentShop != null) {
                    fragmentTransaction.hide(fragmentShop);
                }

            } else if(fragmentEnum == LuckinFragmentEnum.SHOP) {
                if (fragmentShop == null) {
                    fragmentShop = fragment;
                    fragmentTransaction.add(R.id.luckin_fragment_container, fragment, LuckinFragmentEnum.SHOP.name());
                }
                if (QtitansLuckinPreRequestManager.getInstance().getShopPage() != null && fragmentShop instanceof QtitansLuckinFlexFragment) {
                    ((QtitansLuckinFlexFragment)fragmentShop).updateListData(QtitansLuckinPreRequestManager.getInstance().getShopPage().shopList);
                }
                fragmentTransaction.show(fragment);
                if (fragmentDeal != null) {
                    fragmentTransaction.hide(fragmentDeal);
                }
                if (fragmentOrder != null) {
                    fragmentTransaction.hide(fragmentOrder);
                }
            }
            fragmentTransaction.commitAllowingStateLoss();
            //同步执行
            fm.executePendingTransactions();

        } catch (Throwable e) {
           if (BuildConfig.LOG_ENABLE) {
               Logger.e(TAG, e.getMessage());
           }
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason",  "show fragment failed: "+ e.getMessage());
            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
        }
    }

    private QtitansLuckinFlexFragment getFragment(LuckinFragmentEnum fragmentEnum) {
        QtitansLuckinFlexFragment fragment = null;
        if (fragmentEnum== LuckinFragmentEnum.ORDER) {
            if (orderFragment == null) {
                orderFragment = new QtitansLuckinHistoryOrderFragment();
            }
            fragment = orderFragment;

        } else if (fragmentEnum == LuckinFragmentEnum.SHOP) {
            if (shopFragment == null) {
                shopFragment = new QtitansLuckinShopFragment();
            }
            fragment = shopFragment;

        } else if (fragmentEnum == LuckinFragmentEnum.DEAL){
            if (mainFragment == null) {
                mainFragment = new QtitansLuckinFlexFragment();
            }
            fragment = mainFragment;
        }
        return fragment;
    }

    private void jumpLogin(Context context ) {
        if (context == null) return;
        Intent intent = new Intent("com.meituan.android.intent.action.login");
        intent.setPackage(context.getPackageName());
        context.startActivity(intent);
    }

    public void sendLoginEvent(boolean isLogin) {
        try {
            if (titleFlexView != null) {
                Event event = new Event("onLoginStatusChanged", EventScope.GLOBAL, this.getContext());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("isLogin", isLogin);
                event.setData(jsonObject);
                titleFlexView.sendEvent(event);
            }
        } catch (Throwable e) {
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", TAG+": handleRereshIfNeed requestNearbyShops failed: " + e.getMessage());
            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
        }

    }

    public void handleRereshIfNeed(Context context, String shopId, double lat, double lng) {

        QtitansLuckinPreRequestManager.getInstance().requestSaveMoneyDealQueryByShop(context, mTargetUrl, shopId, lat, lng, new IFlexLuckinPageRequestCallback() {
            @Override
            public void onSuccess(LuckinMainPage data) {
                if (mainFragment != null) {
                    mainFragment.updateListData(data.spuDetailList);
                    if (data.originData != null) {
                        updateCacheData(data.originData);
                    }
                }

                QtitansLuckinPreRequestManager.getInstance().requestNearbyShops(context, data, new IFlexLuckinPageRequestCallback() {
                    @Override
                    public void onSuccess(LuckinMainPage data) {
                        if (data != null && data.shopList != null && titleFlexView != null) {
                            initShopCountListener(data);
                            sendShopCountChangeEvent(data, titleFlexView);
                        }
                    }

                    @Override
                    public void onFailed(String message) {
                        // do nothing
                        HashMap<String, Object> info = new HashMap<>();
                        info.put("reason", TAG+": handleRereshIfNeed requestNearbyShops failed: " +message);
                        BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
                    }
                });
            }

            @Override
            public void onFailed(String message) {
                HashMap<String, Object> info = new HashMap<>();
                info.put("reason", TAG+": handleRereshIfNeed requestSaveMoneyDealQueryByShop failed: " +message);
                BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
            }
        });
    }

    private synchronized void updateCacheData(JsonObject data) {
        if (!HadesUtils.enableLuckinPageCache()) return;
        HadesUtils.runOnWorkThread(()->{
            try {
                JsonObject cacheData = data.deepCopy();
                if (cacheData.has("spuDetailList") && cacheData.get("spuDetailList") instanceof JsonArray) {
                    //只缓存首屏6条数据
                    JsonArray spuDetailArray = cacheData.getAsJsonArray("spuDetailList");
                    if (spuDetailArray != null && spuDetailArray.size() > 6) {
                        JsonArray firstScreenArray = new JsonArray();
                        String utmValue = QtitansLuckinPreRequestManager.getInstance().getUtmWidgetLchVal();
                        for (int i = 0; i < 6; i++) {
                            spuDetailArray.get(i).getAsJsonObject().addProperty("utm_qq_widget_lch_2025", utmValue);
                            spuDetailArray.get(i).getAsJsonObject().addProperty("isCache", true);
                            firstScreenArray.add(spuDetailArray.get(i));
                        }
                        cacheData.add("spuDetailList", firstScreenArray);
                    }
                    StorageHelper.setLuckinDealPageData(cacheData.toString());
                }
            } catch (Throwable e) {
                HashMap<String, Object> info = new HashMap<>();
                info.put("reason", TAG+": set luckin deal page cache failed " + e.getMessage());
                BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
            }

        });
    }

    private void sendShopCountChangeEvent(LuckinMainPage data, QtitansFlexCardView flexCardView) {
        try {
            if (data == null || data.shopList == null) return;
            Event event = new Event("shopCountChanged", EventScope.GLOBAL, this.getContext());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("shopCount",data.shopList.size() - 1);
            event.setData(jsonObject);
            flexCardView.sendEvent(event);
            if (data.shopList.size() > 2) {
                //更新首页选中地址状态
                JSONObject shopData = data.shopList.get(1).jsonObject;
                if (shopData == null || !shopData.has("mopInService")) return;
                if (!shopData.optBoolean("mopInService") ) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("mopViewType", 1);
                    mainFragment.updateItemData(0, map);
                    //更新首页缓存数据
                    if (QtitansLuckinPreRequestManager.getInstance().getMainPage() != null && QtitansLuckinPreRequestManager.getInstance().getMainPage().spuDetailList != null && QtitansLuckinPreRequestManager.getInstance().getMainPage().spuDetailList.size() > 0) {
                        QtitansLuckinPreRequestManager.getInstance().getMainPage().spuDetailList.get(0).jsonObject.put("mopViewType", 1);
                    }
                }

            }

        } catch (Throwable e) {
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", TAG+":sendShopCountChangeEvent failed: " +e.getMessage());
            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
        }

    }
    private void showChangeShopDialogIfNeed(LuckinMainPage mainPage) {
        if (mainPage == null || mainPage.shopList == null || mainPage.shopList.size() <= 1 || mainPage.shopList.get(1) == null) {
            return;
        }
        try {
            long lastShowTime = StorageHelper.getSMCDialogTime();
            if (DateUtils.isToday(lastShowTime)) {
                 return;
            }
            String buttonColor = "#0022AB";
            if (mainPage.brandConfig != null) {
                buttonColor = (String) mainPage.brandConfig.get("buttonColor");
            }
            if (!changeShopDialog.isShowing()) {
                JSONObject dialogData = new JSONObject(mainPage.shopList.get(1).jsonObject.toString());
                dialogData.put("templateUrl", mainPage.dialogTemplateUrl);
                dialogData.put("templateName", mainPage.dialogTemplateName);
                dialogData.put("themeColor", buttonColor);
                changeShopDialog.setFlexData(dialogData);
                changeShopDialog.show();

                StorageHelper.saveSMCDialogTime(System.currentTimeMillis());
            }
        } catch (Throwable e) {
            QQThrowableReporter.report(TAG, e, true);
        }
    }

    private void showBizOffDialogIfNeed(LuckinMainPage mainPage) {
        if (mainPage == null || mainPage.shopList == null || mainPage.shopList.size() <= 1) {
            return;
        }
        HadesUtils.runOnMainThread(()-> {
            try {
                for (int i = 1; i < mainPage.shopList.size(); i++) {
                    QtitansFlexData shop = mainPage.shopList.get(i);
                    boolean bizOpen = shop.jsonObject.optBoolean("mopInService");
                    if (bizOpen) {
                        return;
                    }
                }
                if (!bizOffDialog.isShowing()) {
                    String buttonColor = "#0022AB";
                    if (mainPage.brandConfig != null) {
                        buttonColor = (String) mainPage.brandConfig.get("buttonColor");
                    }
                    JSONObject dialogData = new JSONObject();
                    dialogData.put("templateUrl", mainPage.dialogTemplateUrl);
                    dialogData.put("templateName", mainPage.dialogTemplateName);
                    dialogData.put("themeColor", buttonColor);
                    bizOffDialog.show(dialogData);
                }
            } catch (Throwable e) {
                QQThrowableReporter.report(TAG, e, true);
            }
        });
    }

    public LuckinFragmentEnum getFragmentEnum() {
        return fragmentEnum;
    }

    public void setFragmentEnum(LuckinFragmentEnum fragmentEnum) {
        this.fragmentEnum = fragmentEnum;
    }

    public void onHideLoadingView() {
        Logger.d("luckincache", "onHideloading");
        if (iContainerView != null) {
            Logger.d("luckincache", "hide");
            iContainerView.onHideLoadingView();
        }
        if (!UserCenter.getInstance(this.getContext()).isLogin() && !isShowLogin) {
            jumpLogin(QtitansLuckinContainerFragment.this.getContext());
            isShowLogin = true;
        }
        tryShowSubscribe();
        tyrShowGuide();

    }

    private void tyrShowGuide() {
        if (TextUtils.isEmpty(mTargetUrl)) return;
        try {
            Uri uri = Uri.parse(mTargetUrl);
            String luckinGuide = uri.getQueryParameter("luckinGuideView");
            if ("1".equals(luckinGuide)) {
                String templateStr = HadesUtils.getLuckinShopFeedTemplate();
                if (!TextUtils.isEmpty(templateStr)) {
                    JSONObject dialog = new JSONObject(templateStr);
                    JSONObject jsonObject = dialog.optJSONObject("guideDialogTemplate");
                    if (jsonObject != null) {
                        luckinGuideDialog.show(jsonObject);
                    }
                }
            }

        }catch (Throwable e) {
            QQThrowableReporter.report("QtitansLuckinContainerFragment", e);
        }

    }
}
