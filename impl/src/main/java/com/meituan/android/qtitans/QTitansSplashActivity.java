package com.meituan.android.qtitans;

import static com.meituan.android.hades.BaseRouterActivity.EXTRA_KEY_BUSINESS_TYPE;
import static com.meituan.android.hades.BaseRouterActivity.EXTRA_KEY_FW_SOURCE;
import static com.meituan.android.hades.BaseRouterActivity.EXTRA_KEY_RED_INFO;
import static com.meituan.android.hades.BaseRouterActivity.EXTRA_KEY_WIDGET_ENUM;
import static com.meituan.android.hades.dyadater.utils.HadesUtilsAdapter.EXTRA_QTITANS_DSP_PLUS_FLAG;
import static com.meituan.android.hades.impl.r.HadesReceiver.EXTRA_KEY_SESSION_ID;
import static com.meituan.android.hades.router.RouterManager.DEFAULT_LCH_WIDGET;
import static com.meituan.android.qtitans.ContainerReporterLocal.CID_CONTAINER_INNER_PV;
import static com.meituan.android.qtitans.QtitansVideoPushSplashActivity.QTITANS_SPLASH_VIDEO_PUSH_MIN_PROGRAM_SCHEME_BASE;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.FragmentActivity;
import android.text.TextUtils;

import com.meituan.android.aurora.ActivitySwitchCallbacks;
import com.meituan.android.hades.BuildConfig;
import com.meituan.android.hades.HadesWidgetEnum;
import com.meituan.android.hades.bean.SubscribeType;
import com.meituan.android.hades.dyadater.container.ContainerConst;
import com.meituan.android.hades.dyadater.container.ContainerServiceFactory;
import com.meituan.android.hades.dyadater.container.ContainerUtilsAdapter;
import com.meituan.android.hades.dyadater.container.QtitansContainerVisitType;
import com.meituan.android.hades.dyadater.container.QtitansLoadingPageContent;
import com.meituan.android.hades.dyadater.container.adapter.ContainerAdapterManager;
import com.meituan.android.hades.dyadater.container.interfaces.ContainerConfigCallback;
import com.meituan.android.hades.dyadater.container.interfaces.IContainerConfig;
import com.meituan.android.hades.dyadater.container.interfaces.IContainerProvider;
import com.meituan.android.hades.dyadater.container.interfaces.IContainerService;
import com.meituan.android.hades.dyadater.container.interfaces.IQtitansNativeConfigStrategy;
import com.meituan.android.hades.dyadater.desk.DeskResourceData;
import com.meituan.android.hades.dyadater.desk.DeskSourceEnum;
import com.meituan.android.hades.dyadater.desk.DesktopContainerPushEnum;
import com.meituan.android.hades.dyadater.desk.feedback.FeedbackExtensions;
import com.meituan.android.hades.hf.HadesStreamerManager;
import com.meituan.android.hades.impl.R;
import com.meituan.android.hades.impl.desk.ScreenShotManager;
import com.meituan.android.hades.impl.desk.feedback.FeedbackManager;
import com.meituan.android.hades.impl.model.BaseResponse;
import com.meituan.android.hades.impl.net.HadesRetrofit;
import com.meituan.android.hades.impl.report.PushReporter;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.report.ReportParamsKey;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.HadesWidgetUtils;
import com.meituan.android.hades.impl.utils.Lifecycle;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.impl.utils.LxReportUtils;
import com.meituan.android.hades.impl.utils.StorageHelper;
import com.meituan.android.hades.impl.widget.RedDotHelper;
import com.meituan.android.hades.impl.widget.util.WidgetRecorder;
import com.meituan.android.hades.router.HadesRouterPushAnimation;
import com.meituan.android.hades.router.RouterManager;
import com.meituan.android.hades.router.RouterScene;
import com.meituan.android.hades.utils.CollectionUtils;
import com.meituan.android.qtitans.container.bean.ContainerResourceData;
import com.meituan.android.qtitans.container.preload.QPreloadManager;
import com.meituan.android.walmai.widget.AbsDeskAppWT;
import com.meituan.metrics.util.TimeUtil;
import com.meituan.passport.UserCenter;
import com.sankuai.meituan.mbc.dsp.core.Dsp;
import com.sankuai.meituan.msv.page.outsidead.splashad.SplashAdManager;
import com.sankuai.meituan.msv.widget.WidgetJumpManager;
import com.sankuai.meituan.msv.widget.install.CCConstants;
import com.sankuai.meituan.retrofit2.Call;
import com.sankuai.meituan.retrofit2.Callback;
import com.sankuai.meituan.retrofit2.Response;
import com.sankuai.titans.base.Titans;

import org.json.JSONObject;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class QTitansSplashActivity extends FragmentActivity {

    public static final String TAG = "QTitansSplashActivity";
    public final static String QTITANS_SPLASH_SCHEME_BASE = "imeituan://www.meituan.com/qtitans/splash";
    public static final String EXTRA_QTITANS_DESK_RESOURCE_DATA = "extra_qtitans_desk_resource_data";
    public final static String Qtitans_CONTAINER_PARAMS = "Qtitans_CONTAINER_PARAMS";
    public static final String FROM_PUSH = "PUSH";
    public static final String FROM_WIDGET = "WIDGET";
    public static final String EXTRA_TARGET = "extra_target";
    public static final String WIDGET_TRANS_ID = "widget_trans_id";
    public static final String EXTRA_DESK_ENUM_CODE = "extra_desk_enum_code";
    public static final String EXTRA_FROM = "extra_from";
    public static final String QUERY_KEY_LCH = "lch";
    public static final String EXTRA_KEY_AW_TYPE = "hades_router_aw_type";
    public static final String EXTRA_KEY_FW_ENUM = "hades_router_fw_enum";
    public static final String EXTRA_KEY_FW_SCENE = "hades_router_fw_scene";
    public static final String EXTRA_KEY_FW_PUSH_TYPE = "hades_router_fw_push_type";
    public static final String EXTRA_KEY_FW_RESOURCE_ID = "hades_router_fw_resource_id";
    public static final String EXTRA_KEY_REMIND_MODE = "hades_router_remind_mode";
    private ActivitySwitchCallbacks floatLayerLifecycleCallback;
    private IContainerService containerService;
    private ActivitySwitchCallbacks backgroundLifecycleCallback;


    public static boolean canIUse(Context context, String url) {
        return !HadesWidgetUtils.useMgcRouter(context, url); //非MGC
    }

    public static Intent obtainWidgetSplashIntent(Context context,
                                                  String targetUrl,
                                                  int checkSource,
                                                  QtitansLoadingPageContent loadingPageContent,
                                                  HadesWidgetEnum widgetEnum,
                                                  String resourceId,
                                                  String redInfo,
                                                  int source,
                                                  @NonNull QtitansContainerVisitType visitType) {
        try {
            if (!canIUse(context, targetUrl)) {
                Logger.d(TAG, "canIUse=false " + "url=" + targetUrl + " from=widget");
                return null;
            }

            if (loadingPageContent == null) {
                return null;
            }

            if (TextUtils.isEmpty(targetUrl)) {
                return null;
            }

            Uri uri = getSplashUri(targetUrl, resourceId, false, String.valueOf(checkSource));
            if (uri == null) {
                return null;
            }

            if (StorageHelper.getKeyQcSModel(context)) {
                Uri.Builder dataBuilder = uri.buildUpon();
                dataBuilder.appendQueryParameter("_speed_mode", "1");
                uri = dataBuilder.build();
            }

            Intent targetIntent = new Intent();
            targetIntent.setData(uri);
            targetIntent.putExtra(EXTRA_TARGET, targetUrl);
            targetIntent.putExtra(EXTRA_FROM, FROM_WIDGET);
            JSONObject loadingViewParams = obtainLoadingViewParams(String.valueOf(checkSource), String.valueOf(SubscribeType.DESK_APP),
                    loadingPageContent, visitType);
            JSONObject containerJsonObject = new JSONObject();
            try {
                containerJsonObject.put("loadingViewParams", loadingViewParams);
                containerJsonObject.put("targetUrl", targetUrl);
                containerJsonObject.put("jumpScene", FROM_WIDGET);
                containerJsonObject.put("sceneCode", widgetEnum.getWidgetNumCode());
                containerJsonObject.put("resourceId", resourceId);

            } catch (Throwable e) {

            }
            targetIntent.putExtra(ContainerConst.Qtitans_CONTAINER_PARAMS, containerJsonObject.toString());
            targetIntent.putExtra(EXTRA_KEY_RED_INFO, redInfo);
            targetIntent.putExtra(EXTRA_KEY_FW_SOURCE, String.valueOf(source));
            targetIntent.putExtra(EXTRA_KEY_WIDGET_ENUM, widgetEnum.getWidgetNumCode());
            targetIntent.putExtra(EXTRA_KEY_BUSINESS_TYPE, SubscribeType.DESK_APP);
            updateWidgetAlias(context, targetIntent, containerJsonObject);
            return targetIntent;
        } catch (Throwable e) {
            QQThrowableReporter.report("QTitansSplashActivity", e);
        }
        return null;
    }

    public static void updateWidgetAlias(Context context, Intent targetIntent, JSONObject containerParams) {
        try {
            JSONObject loadingViewParams = containerParams.optJSONObject("loadingViewParams");
            if (StorageHelper.getQcUpdateAlias(context)) {
                if (context == null
                        || targetIntent == null
                        || containerParams == null
                        || loadingViewParams == null) {
                    return;
                }
                String checkSource = loadingViewParams.optString("checkSource");
                HadesWidgetEnum hadesWidgetEnum = ContainerUtilsAdapter.getHadesWidgetEnum(checkSource);
                if (hadesWidgetEnum == null || HadesWidgetEnum.DESK_APP_127 == hadesWidgetEnum) {
                    return;
                }
                String businessType = loadingViewParams.optString("businessType");
                if (TextUtils.isEmpty(businessType) || !String.valueOf(SubscribeType.DESK_APP).equals(businessType)) {
                    return;
                }
                String visitType = loadingViewParams.optString("visitType");
                if (TextUtils.isEmpty(visitType) || !QtitansContainerVisitType.VisitWidget.getType().equals(visitType)) {
                    return;
                }
                Uri uri = targetIntent.getData();
                if (uri == null) {
                    return;
                }
                if (hadesWidgetEnum.equals(HadesWidgetEnum.DESK_APP)) {
                    Uri.Builder builder = uri.buildUpon();
                    targetIntent.setData(builder.build());
                    return;
                }
                targetIntent.setComponent(new ComponentName(context.getPackageName(), "com.meituan.android.qtitans.QTitansSplashActivity.diff" + checkSource));
                Uri.Builder builder = uri.buildUpon();
                builder.path("/qtitans/splash/diff" + checkSource);
                targetIntent.setData(builder.build());
            }
        } catch (Throwable e) {
            QQThrowableReporter.report("QTitansSplashActivity", e);
        }
    }

    public static Intent obtainPushSplashIntent(Context context,
                                                DeskSourceEnum deskSourceEnum,
                                                String targetUrl,
                                                DeskResourceData deskResourceData) {
        try {
            if (!canIUse(context, targetUrl)) {
                Logger.d(TAG, "canIUse=false " + "url=" + targetUrl + " from=push");
                return null;
            }

            if (TextUtils.isEmpty(targetUrl) || deskResourceData == null || deskSourceEnum == null) {
                return null;
            }

            Uri uri = Uri.parse(targetUrl);
            if (uri == null) {
                return null;
            }

            Intent routerIntent = new Intent(Intent.ACTION_VIEW);
            JSONObject containerJsonObject = new JSONObject();
            JSONObject loadingViewParamsJson = new JSONObject();
            String checkSource = null;
            if (deskResourceData.mContainerPushInfo != null) {
                // 当且仅当marketingType = subscribe_push 或 subscribe_push_nf（即订阅push）且ContainerPushInfo不为空时，表示当前一定处于订阅push场景下，
                // 根据PM诉求，订阅push一定跳转小程序页面，因此需要额外构造loadingViewParams，用于展示开屏页
                if ("subscribe_push".equals(deskResourceData.marketingType) || "subscribe_push_nf".equals(deskResourceData.marketingType)
                        || TextUtils.equals("coconut_pudding_pageant", deskResourceData.marketingType)
                        || TextUtils.equals("watermelon_chocolate_gala", deskResourceData.marketingType)
                        || deskResourceData.mContainerPushInfo.pushTypeContainer.equals(DesktopContainerPushEnum.REVISIT.getType())
                ) {
                    checkSource = String.valueOf(deskResourceData.mContainerPushInfo.checkSource);
                    try {
                        loadingViewParamsJson.put("checkSource", checkSource);
                        loadingViewParamsJson.put("businessType", String.valueOf(deskResourceData.mContainerPushInfo.businessType));
                        loadingViewParamsJson.put("pushTypeContainer", String.valueOf(deskResourceData.mContainerPushInfo.pushTypeContainer));
                        loadingViewParamsJson.put("visitType", QtitansContainerVisitType.VisitPush.getType());
                        if (deskResourceData.loadingPageContent != null) {
                            loadingViewParamsJson.put("useCapsule", deskResourceData.loadingPageContent.useCapsule);
                            loadingViewParamsJson.put("useMiniProgram", deskResourceData.loadingPageContent.useMiniProgram);

                            if (deskResourceData.mContainerPushInfo.pushTypeContainer.equals(DesktopContainerPushEnum.REVISIT.getType())) {
                                loadingViewParamsJson.put("useScreenPage", true);
                                loadingViewParamsJson.put("bottomText", deskResourceData.loadingPageContent.bottomText);
                                loadingViewParamsJson.put("logoUrl", deskResourceData.loadingPageContent.logoUrl);
                                loadingViewParamsJson.put("iconUrl", deskResourceData.loadingPageContent.icon);
                                loadingViewParamsJson.put("iconName", deskResourceData.loadingPageContent.iconName);

                            }
                        }
                        containerJsonObject.put("isFromExternalLink", false);
                    } catch (Throwable e) {

                    }
                }
                if ("30001".equals(checkSource)) {
                    targetUrl = targetUrl + "&" + "enable_new_router=1";
                    routerIntent.putExtra(Dsp.INTENT_EXTRA_IS_DSP_COLD_START, true);
                    routerIntent.putExtra(Dsp.INTENT_EXTRA_IS_RETURN_HANDLED, true);
                    routerIntent.putExtra("_source_from_qq", true); // QQ 冷启链路标识
                }
            }
            containerJsonObject.put("loadingViewParams", loadingViewParamsJson);
            containerJsonObject.put("targetUrl", targetUrl);
            containerJsonObject.put("jumpScene", FROM_PUSH);
            containerJsonObject.put("sceneCode", deskSourceEnum.getCode());
            containerJsonObject.put("source", deskSourceEnum.name());
            containerJsonObject.put("resourceId", deskResourceData.resourceId);
            containerJsonObject.put("scene", deskResourceData.scene);
            containerJsonObject.put("sessionId", deskResourceData.sessionId);
            containerJsonObject.put("pushType", deskResourceData.pushType);
            containerJsonObject.put("popupType", deskResourceData.popupType);
            containerJsonObject.put("remindMode", PushReporter.getRemindMode(deskResourceData));
            containerJsonObject.put("awType", PushReporter.getAwType(deskResourceData));
            containerJsonObject.put("containerPerfStartTime", deskResourceData.pushClickElapsedTime);
            String urlId = uri.getQueryParameter("urlId");
            containerJsonObject.put("urlId", urlId);
            routerIntent.putExtra(EXTRA_TARGET, targetUrl);
            routerIntent.putExtra(EXTRA_FROM, FROM_PUSH);
            routerIntent.putExtra(ContainerConst.Qtitans_CONTAINER_PARAMS, containerJsonObject.toString());
            routerIntent.putExtra(EXTRA_QTITANS_DESK_RESOURCE_DATA, HadesUtils.toJson(deskResourceData));
            routerIntent.putExtra(EXTRA_DESK_ENUM_CODE, deskSourceEnum.getCode());
            Uri targetUri = getSplashUri(targetUrl, deskResourceData.resourceId, true, checkSource);
            if (targetUri == null) {
                return null;
            }
            routerIntent.setData(targetUri);
            return routerIntent;

        } catch (Throwable e) {
            QQThrowableReporter.report("QTitansSplashActivity", e);
        }
        return null;
    }

    private static Uri getSplashUri(String targetUrl, String resourceId, boolean isPush, String checkSource) {
        Uri uri = Uri.parse(targetUrl);
        if (uri == null) {
            return null;
        }
        String exitType = uri.getQueryParameter("exitType");
        String link = QTITANS_SPLASH_SCHEME_BASE;
        // 短视频push场景下，即exitType为1且checkSource为30001时，走小程序独立栈
        // 此处其他1*1小程序由于无法控制返回键逻辑，暂时不接入小程序独立栈
        if (isPush && "1".equals(exitType) && "30001".equals(checkSource)) {
            link = QTITANS_SPLASH_VIDEO_PUSH_MIN_PROGRAM_SCHEME_BASE;
        }
        Uri.Builder dataBuilder = Uri.parse(link).buildUpon();
        String lch = uri.getQueryParameter(QUERY_KEY_LCH);
        dataBuilder.appendQueryParameter("_page_new", "1"); //不跳MT小助手闪屏
        if (!TextUtils.isEmpty(lch)) {
            dataBuilder.appendQueryParameter(QUERY_KEY_LCH, lch);
        } else {
            dataBuilder.appendQueryParameter(QUERY_KEY_LCH, DEFAULT_LCH_WIDGET);
            HadesWidgetUtils.reportIllegal(resourceId, ReportParamsKey.ILLEGAL_TYPE.EMPTY_LCH, targetUrl);
        }
        return dataBuilder.build();
    }

    private String mTargetUrl;
    private String mSessionId;
    private long onCreateStartTime;
    private long onCreateElapseTime;
    private long onCreateStartUptime;
    private boolean isHandlePushRequest = false;
    private JSONObject qtitansContainerParams;
    private static IQtitansNativeConfigStrategy mQtitansNativeConfigStrategy;
    private String mUriStr = "";
    private JSONObject data;
    private HadesRouterPushAnimation mPushAnimation;
    private static final TimeOutRunnable mTimeOutRunnable = new TimeOutRunnable();
    private IContainerConfig containerConfig;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        try {
            onCreateStartTime = System.currentTimeMillis();
            onCreateElapseTime = TimeUtil.elapsedTimeMillis();
            onCreateStartUptime = SystemClock.uptimeMillis();
            if (HadesUtils.enableQtitansDexContainer()) {
                DexContainerEnterHelper.enterDexContainer();
            }
            Lifecycle.listen();
            if (isVideoScene() && !isVideoAdScene(SplashAdManager.getInstance().isHitSplashAdExp(this))) {
                HadesUtils.removeFromMainThread(mTimeOutRunnable);
            }
            registerFloatLayerLifeCycleListener();
            Intent intent = getIntent();
            if (intent == null) {
                finishSelf("splash intent null");
                return;
            }
            if (intent.hasExtra(QTitansLuckinSplashActivity.EXTRA_KEY_LUCKIN_REOPEN) && intent.getBooleanExtra(QTitansLuckinSplashActivity.EXTRA_KEY_LUCKIN_REOPEN, false)) {
                Intent luckinIntent = new Intent(Intent.ACTION_VIEW);
                luckinIntent.setData(Uri.parse(QTitansLuckinContainerActivity.Qtitans_MINIPROGRAM_SCHEME_BASE));
                luckinIntent.setFlags(Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT);
                startActivity(luckinIntent);
                return;
            }

            isHandlePushRequest = false;
            DeskResourceData resourceData = getDeskResourceData();
            doOnCreate(intent, resourceData, null);

        } catch (Throwable e) {
            finishSelf("onCreate error");
            QQThrowableReporter.report("QTitansSplashActivity", e);
        }
    }

    public void registerBackGroundLifeCycleListener(String targetUrl) {
        if (backgroundLifecycleCallback == null) {
            backgroundLifecycleCallback = new ActivitySwitchCallbacks() {
                @Override
                public void onBackground() {
                    try {
                        String lch = HadesUtils.getLchFromTargetUrl(targetUrl);
                        StorageHelper.setQtitansBackgroundFlag(lch, true);
                        StorageHelper.setQtitansBackgroundTime(lch, TimeUtil.elapsedTimeMillis());
                    } catch (Throwable e) {
                        QQThrowableReporter.report(TAG, e);
                    }

                }
            };
        }
        Lifecycle.registerCallbackRemoveDuplication(backgroundLifecycleCallback);
    }


    public void registerFloatLayerLifeCycleListener() {
        if (floatLayerLifecycleCallback == null) {
            floatLayerLifecycleCallback = new ActivitySwitchCallbacks() {
                @Override
                public void onActivityDestroyed(Activity activity) {
                    HadesStreamerManager.removeConfig(activity);
                    if (CollectionUtils.isEmpty(HadesStreamerManager.getActivityListConfig())) {
                        Lifecycle.unRegisterCallback(floatLayerLifecycleCallback);
                    }
                }
            };
        }
        Lifecycle.registerCallbackRemoveDuplication(floatLayerLifecycleCallback);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (isVideoScene()) {
            startActivity();
        }
    }

    protected void doOnCreate(Intent intent, DeskResourceData resourceData, String replaceUrl) {
        try {
            Logger.d(TAG, "doOnCreate");
            String targetUrl = intent.getStringExtra(EXTRA_TARGET);
            registerBackGroundLifeCycleListener(targetUrl);
            ContainerAdapterManager.reportContainerSplashBabel("doOnCreate", targetUrl);
            QPreloadManager.getInstance().executeSecondaryTask(this, targetUrl); //push场景用户如果点击过快，需要补充执行一次容器次级任务
            if (TextUtils.equals(intent.getStringExtra(EXTRA_FROM), FROM_WIDGET)) { // widget 进入刷新红点
                String redInfo = intent.getStringExtra(EXTRA_KEY_RED_INFO);
                String source = intent.getStringExtra(EXTRA_KEY_FW_SOURCE);
                int code = intent.getIntExtra(EXTRA_KEY_WIDGET_ENUM, -1);
                RedDotHelper.reportRedDot(RouterScene.DESK_WIDGET, code, source, redInfo);
            }

            //埋点参数
            mTargetUrl = TextUtils.isEmpty(replaceUrl) ? intent.getStringExtra(EXTRA_TARGET) : replaceUrl;
            String jumpScene = intent.getStringExtra(EXTRA_FROM);
            String scene = intent.getStringExtra(EXTRA_KEY_FW_SCENE);
            mSessionId = intent.getStringExtra(EXTRA_KEY_SESSION_ID);
            int sceneCode = intent.getIntExtra(EXTRA_KEY_FW_ENUM, -1);
            String resourceId = intent.getStringExtra(EXTRA_KEY_FW_RESOURCE_ID);
            String source = intent.getStringExtra(EXTRA_KEY_FW_SOURCE);
            String remindMode = intent.getStringExtra(EXTRA_KEY_REMIND_MODE);
            int pushType = intent.getIntExtra(EXTRA_KEY_FW_PUSH_TYPE, 0);
            String awType = intent.getStringExtra(EXTRA_KEY_AW_TYPE);
            int deskTypeCode = resourceData != null ? resourceData.deskType.getCode() : -1;
            String paramsStr = intent.getStringExtra(ContainerConst.Qtitans_CONTAINER_PARAMS);
            if (!TextUtils.isEmpty(paramsStr) || HadesUtils.disableQtitansDexContainer()) {
                qtitansContainerParams = new JSONObject(paramsStr);
                if (!TextUtils.isEmpty(replaceUrl)) {
                    qtitansContainerParams.put("targetUrl", replaceUrl);
                }
                mTargetUrl = qtitansContainerParams.optString("targetUrl");
                mSessionId = qtitansContainerParams.optString("sessionId");
                jumpScene = qtitansContainerParams.optString("jumpScene");
                scene = qtitansContainerParams.optString("scene");
                sceneCode = qtitansContainerParams.optInt("sceneCode");
                remindMode = qtitansContainerParams.optString("remindMode");
                resourceId = qtitansContainerParams.optString("resourceId");
                pushType = qtitansContainerParams.optInt("pushType");
                awType = qtitansContainerParams.optString("awType");
                qtitansContainerParams.put("splashOnCreateTime", onCreateElapseTime);
                if (getIntent() != null) {
                    boolean isDspPlus = getIntent().getBooleanExtra(EXTRA_QTITANS_DSP_PLUS_FLAG, false);
                    qtitansContainerParams.put("isDSPPlusFlag", isDspPlus);
                    String deskData = getIntent().getStringExtra(EXTRA_QTITANS_DESK_RESOURCE_DATA);
                    if (!TextUtils.isEmpty(deskData)) {
                        qtitansContainerParams.put("deskResourceDataStr", deskData);
                    }
                }
                qtitansContainerParams.put("isLogin", UserCenter.getInstance(HadesUtils.getContext()).isLogin());
                IContainerProvider provider = ContainerServiceFactory.getInstance().getProvider();
                if (provider != null) {
                    Logger.d(TAG, "provider ready before observer change");
                    ContainerAdapterManager.reportContainerReasonDexBabel("provider ready before observer change");
                    ContainerAdapterManager.reportContainerProviderDexBabel(onCreateStartTime, System.currentTimeMillis() - onCreateStartTime);
                    handleJump(provider, intent, targetUrl, qtitansContainerParams.toString());
                } else {
                    final ContainerServiceFactory.ProviderObserver observer = new ContainerServiceFactory.ProviderObserver() {
                        @Override
                        public void onProviderChanged(IContainerProvider provider) {
                            try {
                                Logger.d(TAG, "handleJump with provider changed" + provider.getClass().getName());
                                // 取消注册观察者，避免重复回调
                                ContainerServiceFactory.getInstance().unregisterObserver(this);
                                handleJump(provider, intent, targetUrl, qtitansContainerParams.toString());
                                ContainerAdapterManager.reportContainerProviderDexBabel(onCreateStartTime, System.currentTimeMillis() - onCreateStartTime);
                                ContainerAdapterManager.reportContainerReasonDexBabel("provider ready with observer change");
                            } catch (Throwable e) {
                                QQThrowableReporter.report("QTitansSplashActivity", e);
                                ContainerAdapterManager.reportContainerReasonDexBabel("onProviderChanged: "+e.getMessage());
                                defaultStart(intent, targetUrl);
                            }
                        }

                        @Override
                        public void onProviderFailed(String error) {
                            try {
                                // 回退本地实现兜底
                                Logger.d(TAG, "onProviderFailed：" + error);
                                ContainerAdapterManager.reportContainerReasonDexBabel("provider failed: " + error);
                                // 取消注册观察者，避免重复回调
                                ContainerServiceFactory.getInstance().unregisterObserver(this);
                                // 使用本地实现作为兜底
                                IContainerProvider localProvider = LocalContainerProviderImpl.getInstance();
                                // 将本地实现设置为全局Provider
                                ContainerServiceFactory.getInstance().setProvider(localProvider);
                                handleJump(localProvider, intent, targetUrl, qtitansContainerParams.toString());
                                ContainerAdapterManager.reportContainerReasonDexBabel(error);
                            } catch (Throwable e) {
                                QQThrowableReporter.report("QTitansSplashActivity", e);
                                ContainerAdapterManager.reportContainerReasonDexBabel("onProviderFailed: "+e.getMessage());
                                defaultStart(intent, targetUrl);
                            }

                        }
                    };
                    // 注册观察者
                    ContainerServiceFactory.getInstance().registerObserver(observer);
                }

            }


            String from = intent.getStringExtra(EXTRA_FROM);
            if (TextUtils.equals(from, FROM_WIDGET)) {
                Uri u = intent.getData();
                String clickPath = (u == null ? "null" : u.getPath());
                int widgetEnumCode = intent.getIntExtra(EXTRA_KEY_WIDGET_ENUM, -1);
                HadesWidgetEnum widgetEnum = HadesWidgetEnum.getInstanceByWidgetNumCode(widgetEnumCode);
                WidgetRecorder.recordClick(getApplicationContext(), widgetEnum, resourceId, AbsDeskAppWT.TARGET_ID, true, clickPath, false);
            }

            PushReporter.reportJumpRouter(false,
                    mTargetUrl,
                    jumpScene,
                    sceneCode,
                    source,
                    scene,
                    mSessionId,
                    remindMode,
                    resourceId,
                    pushType,
                    awType,
                    null,
                    null,
                    deskTypeCode,
                    null);

            PushReporter.reportGameInfo(false,
                    mTargetUrl,
                    jumpScene,
                    sceneCode,
                    source,
                    scene,
                    mSessionId,
                    remindMode,
                    resourceId,
                    pushType,
                    awType,
                    null,
                    null,
                    deskTypeCode,
                    null);

            PushReporter.reportPinDau(
                    "",
                    mTargetUrl,
                    jumpScene,
                    sceneCode,
                    resourceId,
                    onCreateStartTime,
                    onCreateStartUptime,
                    true,
                    qtitansContainerParams != null ? qtitansContainerParams.toString() : "");

        } catch (Throwable e) {
            QQThrowableReporter.report("QTitansSplashActivity", e);
            String targetUrl = intent.getStringExtra(EXTRA_TARGET);
            defaultStart(intent, targetUrl);
        }

    }

    private void defaultStart(Intent intent, String targetUrl) {
        ContainerReporterLocal.logCreateIntent("QTitansSplashActivity doOnCreate qtitansContainerParams null", intent, null, mTargetUrl, -1);
        ContainerAdapterManager.reportContainerSplashBabel("defaultStart", targetUrl);
        //兜底，走业务侧容器
        if (TextUtils.isEmpty(mTargetUrl)) {
            finishSelf("splash targetUrl empty");
            return;
        }
        try {
            RouterManager.jumpToBizActivity(this, mTargetUrl);
        } catch (Throwable e) {
            QQThrowableReporter.report("QTitansSplashActivity", e);
        }
    }

    private void handleJump(IContainerProvider provider, Intent intent, String targetUrl, String paramsStr) {
        if (provider == null) {
            Logger.d(TAG, "handleJump provider null");
            defaultStart(intent, targetUrl);
            return;
        }
        ContainerAdapterManager.reportContainerSplashBabel("handleJump", targetUrl);
        Logger.d(TAG, "handleJump：" + provider.getClass().getName());
        if (getLoadingViewParams() != null
                && "30001".equals(getLoadingViewParams().optString("checkSource"))) {
            mUriStr = targetUrl;
            containerConfig = provider.getContainerConfig();
            if (containerConfig != null) {
                containerConfig.updateQtitansContainerParams(paramsStr);
                containerConfig.init();

            }
            mQtitansNativeConfigStrategy = provider.getQtitansNativeConfigStrategy();
            if (mQtitansNativeConfigStrategy != null) {
                mQtitansNativeConfigStrategy.init(this, null);
            }
            requestQContainerConfig();

        } else {
            Logger.d(TAG, "handleJump openQtitansContainer");
            ContainerReporterLocal.logCreateIntent("QTitansSplashActivity doOnCreate openQtitansContainer", intent, null, mTargetUrl, -1);
            containerService = provider.getContainerService();
            if (containerService != null) {
                Logger.d(TAG, "handleJump  containerService not null");
                containerService.openQtitansContainer(this, paramsStr);
            }

        }
    }

    private void startActivity() {
        DeskResourceData data = getDeskResourceData();
        if (qtitansContainerParams != null
                && "PUSH".equals(qtitansContainerParams.optString("jumpScene"))
                && HadesUtils.enableShowMinProgramAnimation(data)) {
            mPushAnimation = new HadesRouterPushAnimation();
            mPushAnimation.showMinProgramAnimation(this, data.getVideoDeskPushRectInfo(), this::jumpUrl);
        } else {
            jumpUrl();
        }
        long time = System.currentTimeMillis();
        long uptime = SystemClock.uptimeMillis();
        ContainerReporterLocal.reportContainerJump(qtitansContainerParams.toString(), time, uptime);
        if (getLoadingViewParams() != null &&
                TextUtils.equals(getLoadingViewParams().optString("visitType"), QtitansContainerVisitType.VisitVideoWidget22.getType())) {
            WidgetRecorder.recordClick(this, HadesWidgetEnum.FEATURE22_VIDEO, "", "videoFeature22Widget", true, "", false);
        }
    }

    private DeskResourceData getDeskResourceData() {
        if (getIntent() == null) {
            return null;
        }
        String resourceDataJson = getIntent().getStringExtra(EXTRA_QTITANS_DESK_RESOURCE_DATA);
        return HadesUtils.fromJson(resourceDataJson, DeskResourceData.class);
    }

    private void jumpUrl() {
        Uri uri = Uri.parse(mUriStr);
        WidgetJumpManager openPageManager = new WidgetJumpManager();
        String hitSplashAdExp = SplashAdManager.getInstance().isHitSplashAdExp(this);
        if (isVideoAdScene(hitSplashAdExp)) {
            SplashAdManager.getInstance().preloadAd(this, hitSplashAdExp, openPageManager.isColdStart(this, uri));
            Uri adUri = Uri.parse(CCConstants.SPLASH_AD_SCHEME);
            Intent intent = new Intent(Intent.ACTION_VIEW, adUri);
            intent.putExtra("msvTargetUrl", mUriStr);
            intent.putExtra(CCConstants.WIDGET_SPLASH_ACTIVITY_NAME, this.getClass().getName());
            if (qtitansContainerParams != null) {
                ContainerReporterLocal.logOpenIntent("QtitansSplashActivity startActivity is adActivity", getIntent(), qtitansContainerParams.toString(), uri.toString());
            }
            startActivity(intent);
            // 禁用过渡动画
            overridePendingTransition(0, 0);
        } else {
            if (qtitansContainerParams != null) {
                ContainerReporterLocal.logOpenIntent("QtitansSplashActivity startActivity is openPage", getIntent(), qtitansContainerParams.toString(), uri.toString());
            }
            onResumeInner();
            openPageManager.openPage(this, uri);
            overridePendingTransition(R.anim.desk_fade_in, R.anim.desk_fade_out);
            finish();
        }
    }

    private boolean isVideoAdScene(String hitSplashAdExp) {
        // 判断是否命中了视频广告实验
        if (TextUtils.isEmpty(hitSplashAdExp)) {
            return false;
        }
        // 是短视频push但开关关闭
        if (isPushScene() && !HadesUtils.enableVideoPushAd()) {
            return false;
        }
        // widget开关是否开启
        return HadesUtils.enableVideoWidgetAd();
    }

    private boolean isPushScene() {
        return qtitansContainerParams != null && "PUSH".equals(qtitansContainerParams.optString("jumpScene"));
    }

    @Override
    protected void onResume() {
        super.onResume();
        onResumeInner();
    }

    private void onResumeInner() {
        try {
            HadesUtils.runOnWorkThread(() -> LxReportUtils.pageEvent(CID_CONTAINER_INNER_PV, new HashMap<>()));
            if (isVideoScene() && mQtitansNativeConfigStrategy != null) {
                mQtitansNativeConfigStrategy.init(this, null);
                if (qtitansContainerParams != null) {
                    mQtitansNativeConfigStrategy.setQtitasnContainerParams(qtitansContainerParams.toString());
                }

            }
        } catch (Throwable e) {
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M && Build.VERSION.SDK_INT <= Build.VERSION_CODES.P) {
                // Android 7-9 isTopOfTask方法容易因为ActivityRecord被回收造成crash
                // https://crash.sankuai.com/#/new/overview/message?filter=%5B%7B%22k%22%3A%22hash%22%2C%22v%22%3A%22java.lang.IllegalArgumentException%3Acom.sankuai.meituan.mbc.dsp.DspActivity.onResume%28Unknown%20Source%3A129%29%22%7D%2C%7B%22k%22%3A%22timelike%22%2C%22v%22%3A%22%E6%9C%80%E8%BF%9130%E5%A4%A9%22%7D%5D&channel=Unprovided&project=android_platform_monitor&type=crash
                // 尝试修复
                handleOnResumeException();
            }
            QQThrowableReporter.report("QTitansSplashActivity", e);
        }
    }

    private boolean isVideoScene() {
        return getLoadingViewParams() != null && "30001".equals(getLoadingViewParams().optString("checkSource"));
    }

    /**
     * 修改mCalled = true
     */
    private void handleOnResumeException() {
        try {
            Class dspSuperClass = Activity.class;
            Field callField = dspSuperClass.getDeclaredField("mCalled");
            callField.setAccessible(true);
            callField.setBoolean(QTitansSplashActivity.this, true);
        } catch (Throwable throwable) {
            Titans.serviceManager().getStatisticsService().reportClassError("QtitansWebContainer", "handleOnResumeException", throwable);
            QQThrowableReporter.report("QTitansSplashActivity", throwable);
        }
    }

    @Override
    protected void onPause() {
        try {
            super.onPause();
            // 短视频商业化场景特殊处理，由于当前activity是SingleTask，避免快速点击组件进入时当前页面被销毁导致其他业务无法回调onDestroy
            if (isVideoScene() && isVideoAdScene(SplashAdManager.getInstance().isHitSplashAdExp(this))) {
                return;
            }
            finish();
        } catch (Throwable e) {
        }
    }

    @Override
    protected void onStop() { //容器页 onResume 后会回调
        try {
            super.onStop();
            ContainerReporterLocal.reportContainerOnStop("SPLASH");
            if (mQtitansNativeConfigStrategy != null && getLoadingViewParams() != null && "30001".equals(getLoadingViewParams().optString("checkSource"))) {
                HadesRetrofit.getInstance(this).getContainerResources(
                        getLoadingViewParams().optString("businessType"),
                        getLoadingViewParams().optString("checkSource"),
                        getLoadingViewParams().optString("visitType")).enqueue(new Callback<BaseResponse<ContainerResourceData>>() {
                    @Override
                    public void onResponse(Call<BaseResponse<ContainerResourceData>> call, Response<BaseResponse<ContainerResourceData>> response) {
                        try {
                            if (response != null && response.body() != null && response.body().data != null) {
                                String dataStr = HadesUtils.toJson(response.body().data);
                                data = new JSONObject(dataStr);
                                if (data.optBoolean("capsuleAnimation")) {
                                    mQtitansNativeConfigStrategy.showTips(QTitansSplashActivity.this, dataStr);
                                }
                            }
                        } catch (Throwable e) {

                        }

                    }

                    @Override
                    public void onFailure(Call<BaseResponse<ContainerResourceData>> call, Throwable t) {
                    }
                });
            }
        } catch (Throwable e) {
        }
    }

    @Override
    protected void onDestroy() {
        try {
            super.onDestroy();
            if (mPushAnimation != null) {
                mPushAnimation.clearMinProgramAnimation();
            }
            // 适配短视频商业化广告场景下胶囊无法展示的问题，延长胶囊生命周期监听时长
            if (isVideoScene() && isVideoAdScene(SplashAdManager.getInstance().isHitSplashAdExp(this))) {
                HadesUtils.runOnMainThreadWithDelay(mTimeOutRunnable, HadesUtils.getSplashNvCacheTime());
            } else {
                HadesUtils.runOnMainThread(mTimeOutRunnable);
            }
        } catch (Throwable e) {
        }
    }

    protected void finishSelf(String reason) {
        try {
            finish();
            Logger.d(TAG, "finishSelf reason: " + reason);
        } catch (Throwable e) {
        }
    }

    public JSONObject getLoadingViewParams() {
        return qtitansContainerParams != null ? qtitansContainerParams.optJSONObject("loadingViewParams") : null;
    }

    private void requestQContainerConfig() {
        String configType = "container";
        String checkSource = containerConfig.getCurrentCheckSource();
        String businessType = containerConfig.getCurrentBusinessType();
        long startRequestTime = TimeUtil.elapsedTimeMillis();
        requestQContainerConfig(configType, businessType, checkSource, "", new ContainerConfigCallback() {
            @Override
            public void onGetConfigSuccess(String containerConfig, String pageConfig) {
                if (pageConfig == null) {
                    if (BuildConfig.LOG_ENABLE) {
                        Logger.d("pageConfig is null");
                    }
                    onGetConfigFail("pageConfig is null");
                    return;
                }
                long endTime = TimeUtil.elapsedTimeMillis();
                onConfigUpdate(pageConfig);
                Map<String, Object> log = new HashMap<>();
                log.put(ReportParamsKey.CONTAINER.CONTAINER_STAGE, "onGetConfigSuccess");
                log.put("tag", TAG);
                try {
                    JSONObject pageConfigJson = new JSONObject(pageConfig);
                    log.put("isFromCache", pageConfigJson.optBoolean("isFromCache"));
                    log.put("configVersion", pageConfigJson.optString("configVersion"));
                } catch (Throwable e) {
                    QQThrowableReporter.report("QTitansSplashActivity", e);
                }
                log.put("checkSource", checkSource);
                log.put("businessType", businessType);
                log.put("containerType", getContainerType());
                log.put("requestTimeDiff", endTime - startRequestTime);
                log.put("isActivityFinish", isActivityFinish(QTitansSplashActivity.this));
                ContainerReporterLocal.reportLog(log);
            }

            @Override
            public void onGetConfigFail(String errorMsg) {
                if (BuildConfig.LOG_ENABLE) {
                    Logger.e("initTitleBar onGetConfigFail");
                }
                startActivity();
            }
        });
    }

    private void requestQContainerConfig(String configType, String businessType, String checkSource,
                                         String uriId,
                                         ContainerConfigCallback callback) {
        containerConfig.requestContainerConfig(
                this,
                businessType,
                checkSource,
                configType,
                uriId,
                callback);
    }

    /**
     * 容器类型
     *
     * @return
     */
    public String getContainerType() {
        return qtitansContainerParams == null ? "unknown" : qtitansContainerParams.optString("containerType");
    }

    private void onConfigUpdate(String pageConfig) {
        try {
            if (!isActivityFinish(this) && pageConfig != null) {
                if (mQtitansNativeConfigStrategy != null) {
                    mQtitansNativeConfigStrategy.setQtitasnContainerParams(qtitansContainerParams.toString());
                    mQtitansNativeConfigStrategy.setPageConfig(pageConfig);
                }
                JSONObject loadingParams = qtitansContainerParams == null ? null : qtitansContainerParams.optJSONObject("loadingViewParams");
                if (loadingParams != null && "1".equals(loadingParams.optString("useCapsule"))
                        && isVideo(loadingParams.toString())) {
                    Uri.Builder builder = Uri.parse(mUriStr).buildUpon();
                    builder.appendQueryParameter("useCapsule", "1");
                    mUriStr = builder.build().toString();
                }
                startActivity();
            } else if (isPushScene()) {
                if (getIntent() != null) {
                    if (!TextUtils.isEmpty(qtitansContainerParams.optString("deskResourceDataStr"))) {
                        if (!qtitansContainerParams.optBoolean("isDSPPlusFlag")) {
                            if (FeedbackManager.getInstance().registerListenerWhenRouterActivityStart()) {
                                FeedbackManager.getInstance().registerActivityLifecycle(this.getApplicationContext());
                            }
                            addFeedbackButton(qtitansContainerParams.optString("deskResourceDataStr"));
                        } else if (qtitansContainerParams.optBoolean("isDSPPlusFlag")) {
                            HadesUtils.runOnWorkThread(() -> {
                                DeskResourceData deskResourceData = HadesUtils.fromJson(qtitansContainerParams.optString("deskResourceDataStr"), DeskResourceData.class);
                                int code = qtitansContainerParams == null ? -1 : qtitansContainerParams.optInt("sceneCode");
                                ScreenShotManager.getInstance(this).registerScreenshotInLifecycle(ScreenShotManager.ScreenShotEnum.LANDING,
                                        deskResourceData, code, FeedbackManager.SCENE_DAW, null);
                            });
                        }
                    }
                }
            }
        } catch (Throwable e) {
            if (BuildConfig.LOG_ENABLE) {
                e.printStackTrace();
            }
            startActivity();
        }
    }

    public static boolean isActivityFinish(Activity activity) {
        return activity == null || activity.isFinishing() || activity.isDestroyed();
    }

    public boolean isVideo(String mParams) {
        try {
            if (TextUtils.isEmpty(mParams)) {
                return false;
            }
            JSONObject jsonObject = new JSONObject(mParams);
            return "30001".equals(jsonObject.optString("checkSource"));
        } catch (Throwable throwable) {
            QQThrowableReporter.report("QTitansSplashActivity", throwable);
        }
        return false;
    }

    private void addFeedbackButton(String strDeskResourceData) {
        try {
            HadesUtils.runOnMainThreadWithDelay(() -> {
                HadesUtils.runOnWorkThread(() -> {
                    int code = qtitansContainerParams == null ? -1 : qtitansContainerParams.optInt("sceneCode");

                    DeskResourceData deskResourceData = HadesUtils.fromJson(strDeskResourceData, DeskResourceData.class);
                    if (deskResourceData == null) {
                        return;
                    }
                    FeedbackManager.getInstance().showFeedbackIcon(
                            this.getApplicationContext(), deskResourceData,
                            DeskSourceEnum.getBycode(code), FeedbackManager.SCENE_DESK_PUSH);
                    if (FeedbackExtensions.isRiskAbTestGroup(deskResourceData)
                            && FeedbackExtensions.isRiskUser(deskResourceData)
                            && data != null && data.optBoolean("capsuleAnimation")) {
                        mQtitansNativeConfigStrategy.showTips(QTitansSplashActivity.this, data.toString());
                    } else {
                        ScreenShotManager.getInstance(this).registerScreenshotInLifecycle(ScreenShotManager.ScreenShotEnum.LANDING,
                                deskResourceData, code, FeedbackManager.SCENE_SUBSCRIBE_PUSH, () -> mQtitansNativeConfigStrategy.showTips(QTitansSplashActivity.this, data.toString())
                        );
                    }
                });

            }, 1000);
        } catch (Throwable e) {
            if (BuildConfig.LOG_ENABLE) {
                e.printStackTrace();
            }
        }
    }

    private static class TimeOutRunnable implements Runnable {

        @Override
        public void run() {
            if (mQtitansNativeConfigStrategy != null) {
                mQtitansNativeConfigStrategy.reset();
                mQtitansNativeConfigStrategy = null;
            }
        }
    }

    public static JSONObject obtainLoadingViewParams(final String checkSource, String businessType,
                                                     QtitansLoadingPageContent loadingPageContent,
                                                     @NonNull QtitansContainerVisitType visitType) {
        JSONObject loadingParams = new JSONObject();
        try {
            loadingParams.put("bottomText", loadingPageContent.bottomText);
            loadingParams.put("backgroundUrl", loadingPageContent.backgroundUrl);
            loadingParams.put("logoUrl", loadingPageContent.logoUrl);
            loadingParams.put("iconUrl", loadingPageContent.icon);
            loadingParams.put("iconName", loadingPageContent.iconName);
            loadingParams.put("slogan", loadingPageContent.slogan);
            loadingParams.put("subText", loadingPageContent.subText);
            loadingParams.put("checkSource", checkSource);
            loadingParams.put("businessType", businessType);
            loadingParams.put("useMiniProgram", loadingPageContent.useMiniProgram);
            loadingParams.put("gameSource", loadingPageContent.gameSource);
            loadingParams.put("useScreenPage", loadingPageContent.useScreenPage);
            loadingParams.put("useCapsule", loadingPageContent.useCapsule);
            loadingParams.put("visitType", visitType == null ? QtitansContainerVisitType.UNKNOWN.getType()
                    : visitType.getType());
            if (loadingPageContent.mContainerPushInfo != null) {
                loadingParams.put("pushTypeContainer", loadingPageContent.mContainerPushInfo.pushTypeContainer);
            }

        } catch (Throwable e) {

        }
        return loadingParams;
    }

}
