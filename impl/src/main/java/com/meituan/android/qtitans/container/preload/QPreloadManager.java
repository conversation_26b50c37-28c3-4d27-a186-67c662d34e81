package com.meituan.android.qtitans.container.preload;

import static com.meituan.android.qtitans.container.QtitansContainerFragment.mmpToMscUri;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.meituan.android.aurora.Aurora;
import com.meituan.android.hades.AbsPreloadTask;
import com.meituan.android.hades.dyadater.utils.HadesUtilsAdapter;
import com.meituan.android.hades.impl.config.HadesConfigMgr;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.impl.utils.StorageHelper;
import com.meituan.android.launcher.main.io.SecondaryAsyncTask;
import com.meituan.android.launcher.secondary.SecondaryLauncher;
import com.meituan.android.mrn.config.MRNErrorType;
import com.meituan.android.mrn.engine.MRNManager;
import com.meituan.android.mrn.engine.MRNPreRenderUtil;
import com.meituan.android.mrn.router.MRNURL;
import com.meituan.android.hades.dyadater.container.ContainerType;
import com.meituan.android.qtitans.container.common.QtitansLog;
import com.meituan.android.qtitans.container.msc.MSCPreloadCallback;
import com.meituan.android.qtitans.container.msc.MSCPreloadTask;

import com.meituan.android.qtitans.container.qqflex.QtitansFlexPageRequestManager;

import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class QPreloadManager {

    private static final String TAG = "QPreload";
    private static final List<String> rnBlackList = new ArrayList<>();
    private static final String BLANK_HTML = "about:blank";

    static {
        rnBlackList.add("rn_meishi_food-home");
    }

    public static QPreloadManager getInstance() {
        return Holder.INSTANCE;
    }

    private QPreloadManager() {
    }

    public void preload(Context context, String targetUrl, IPreloadCallback callback) {
        Logger.d(TAG + "preload container url=" + targetUrl);
        if (TextUtils.isEmpty(targetUrl)) {
            return;
        }
        QtitansFlexPageRequestManager.getInstance().setTargetUrl(targetUrl);
        Uri uri = Uri.parse(targetUrl);
        ContainerType type = ContainerType.containType(uri.getPath());
        Context ctx = context.getApplicationContext();
        switch (type) {
            case WEB: {
//                preloadBlankWebView(context);
                callback.onPreloadSuccess(targetUrl);
                break;
            }
            case MRN: {
                MRNURL mrnurl = new MRNURL(targetUrl);
                if (rnBlackList.contains(mrnurl.getMetaName())) {
                    return;
                }
                MRNManager.preLoadJsBundle(ctx, mrnurl.getMetaName(), new MRNPreRenderUtil.IMRNPreLoadCallback() {
                    @Override
                    public void onPreLoadSuccess() {
                        QtitansLog.log(TAG + " MRN onPreLoadSuccess");
                        callback.onPreloadSuccess(targetUrl);
                    }

                    @Override
                    public void onPreLoadError(MRNErrorType errorType) {
                        QtitansLog.log(TAG + " MRN onPreLoadError:: " + errorType);
                        callback.onPreloadFailed();
                    }
                });
                break;
            }
            case MMP:
            case MSC: {
                if (!HadesUtils.mscP()) {
                    QtitansLog.logE("MSCPreloadTask preload not allowed!");
                    return;
                }
                String replaceUrl = targetUrl;
                if (type == ContainerType.MMP) {
                    replaceUrl = mmpToMscUri(targetUrl);
                    Logger.d("QtitansContainerFragment"+"mmp path:"+targetUrl+" replace url "+ replaceUrl);
                }
                HadesUtils.runOnWorkThread(new MSCPreloadTask(ctx, replaceUrl, new MSCPreloadCallback() {
                    @Override
                    public void onSuccess(String resource) {
                        QtitansLog.log(TAG + " preload msc success, resource:" + resource);
                        callback.onPreloadSuccess(targetUrl);
                    }

                    @Override
                    public void onFailed(String message) {
                        QtitansLog.log(TAG + " preload msc failed, message:" + message);
                        callback.onPreloadFailed();
                    }
                }
                ));
            }
            case UNKNOWN: {
                break;
            }
        }

        String preloadKey = uri.getQueryParameter("plk");
        if (TextUtils.isEmpty(preloadKey)) {
            return;
        }
        HadesUtils.runOnWorkThread(() -> {
            if (!ServiceLoader.isInited()) {
                ServiceLoader.init(context.getApplicationContext(), new ServiceLoader.OnErrorListener() {
                    @Override
                    public void onError(Throwable throwable) {
                        QtitansLog.logE(TAG + " ServiceLoader init failed " + throwable.getMessage());
                    }
                });
            } else {
                QtitansLog.log(TAG + " ServiceLoader inited");
            }
            List<AbsPreloadTask> tasks = ServiceLoader.load(AbsPreloadTask.class, preloadKey);
            if (tasks == null || tasks.isEmpty()) {
                return;
            }

            for (AbsPreloadTask task : tasks) {
                if (TextUtils.equals(task.getKey(), preloadKey)) {
                    if (task.mainThread()) {
                        HadesUtils.runOnMainThread(task);
                    } else {
                        task.run();
                    }
                }
            }
            StorageHelper.setPreloadTask(HadesUtilsAdapter.getLchFromTargetUrl(targetUrl), true);
        });
    }

    public void executeSecondaryTask(Context context, String targetUrl) {
        if (TextUtils.isEmpty(targetUrl)) return;
        if (!HadesConfigMgr.getInstance(context).getPreloadSwitch()) return;
        if (!HadesUtils.wmS(context)) return;

        try {
            Context ctx = context.getApplicationContext();
            Uri uri = Uri.parse(targetUrl);
            ContainerType type = ContainerType.containType(uri.getPath());
            switch (type) {
                case WEB: {
                    Set<String> knbTasks = SecondaryLauncher.getColdStartKNBTaskIDSet();
                    SecondaryAsyncTask.getSecondaryLauncher(ctx).start(knbTasks);
                    Aurora.getInstance().startSecondaryByTaskIdSet(knbTasks);
                    break;
                }
                case MRN: {
                    Set<String> mrnTasks = SecondaryLauncher.getColdStartMRNTaskIDSet();
                    SecondaryAsyncTask.getSecondaryLauncher(ctx).start(mrnTasks);
                    Aurora.getInstance().startSecondaryByTaskIdSet(mrnTasks);
                    break;
                }
                case MMP:
                case MSC: {
                    Set<String> mscTasks = SecondaryLauncher.getColdStartMSCTaskIDSet();
                    SecondaryAsyncTask.getSecondaryLauncher(ctx).start(mscTasks);
                    Aurora.getInstance().startSecondaryByTaskIdSet(mscTasks);
                    break;
                }
                case UNKNOWN:
                    break;
            }
        } catch (Throwable throwable) {
            QQThrowableReporter.report("QPreloadManager", throwable);
        }
    }


    public interface IPreloadCallback {
        void onPreloadSuccess(String url);

        void onPreloadFailed();
    }

    private static class Holder {
        private static final QPreloadManager INSTANCE = new QPreloadManager();
    }
}
