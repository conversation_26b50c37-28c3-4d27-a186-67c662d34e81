package com.meituan.android.qtitans.container.qqflex;


import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.constraint.ConstraintLayout;
import android.support.v4.content.Loader;
import android.support.v7.app.AppCompatActivity;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.support.v7.widget.StaggeredGridLayoutManager;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.google.gson.reflect.TypeToken;
import com.meituan.android.common.locate.LoadConfig;
import com.meituan.android.common.locate.LocationLoaderFactory;
import com.meituan.android.common.locate.MasterLocator;
import com.meituan.android.common.locate.MasterLocatorFactory;
import com.meituan.android.common.locate.MasterLocatorFactoryImpl;
import com.meituan.android.common.locate.MtLocation;
import com.meituan.android.common.locate.loader.LoadConfigImpl;
import com.meituan.android.common.locate.util.LocationUtils;
import com.meituan.android.dynamiclayout.controller.LayoutController;
import com.meituan.android.dynamiclayout.controller.event.Event;
import com.meituan.android.dynamiclayout.controller.event.EventListener;
import com.meituan.android.dynamiclayout.controller.event.EventScope;
import com.meituan.android.hades.dyadater.infrastruct.utils.UUIDUtils;
import com.meituan.android.hades.dyadater.utils.HadesUtilsAdapter;
import com.meituan.android.hades.impl.BuildConfig;
import com.meituan.android.hades.impl.R;
import com.meituan.android.hades.impl.report.BabelHelper;
import com.meituan.android.hades.impl.report.PushReporter;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.router.RouterManager;
import com.meituan.android.pin.Pin;
import com.meituan.android.pin.PinCallback;
import com.meituan.android.privacy.interfaces.PermissionGuard;
import com.meituan.android.privacy.locate.MtLocationCache;
import com.meituan.android.privacy.locate.MtLocationLoaderWrapper;
import com.meituan.android.qtitans.container.qqflex.listview.QtitansFlexData;
import com.meituan.android.qtitans.container.qqflex.listview.QtitansFlexItemDecoration;
import com.meituan.android.qtitans.container.qqflex.listview.QtitansFlexListAdapter;
import com.meituan.android.qtitans.container.qqflex.listview.QtitansFlexReporter;
import com.meituan.android.hades.dyadater.container.PreloadState;
import com.meituan.android.qtitans.container.qqflex.model.QtitansFlexPageData;
import com.meituan.android.qtitans.container.qqflex.model.WifiInfo;
import com.meituan.crashreporter.CrashReporter;
import com.meituan.crashreporter.CrashReporterConfig;
import com.sankuai.meituan.mbc.dsp.core.Dsp;
import com.sankuai.titans.result.TitansPermissionUtil;
import com.sankuai.waimai.foundation.location.v2.WMLocation;
import com.sankuai.waimai.foundation.location.v2.WmAddress;
import com.squareup.picasso.Picasso;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import okhttp3.OkHttpClient;

public class QtitansFlexContainerActivity extends AppCompatActivity {
    private final static String TAG = QtitansFlexContainerActivity.class.getSimpleName();
    public static final String BABEL_CUSTOM_PAGE_REPORT = "flex_custom_page_report";
    public static final String SCHEME = "imeituan://www.meituan.com/qtitans/flex/page";
    private LinearLayout titleBarContainer;
    private LinearLayout cardLisContainer;
    private RecyclerView productListView;
    private QtitansFlexListAdapter mDynamicAdapter;
    private final static int DEFAULT_SPAN = 2;
    private ImageView backgroundImg;
    private View statusView;
    private QtitansFlexCardView titleFlexView;
    private QtitansFlexCardView cardListTopView;
    private QtitansFlexCardView feedHeaderView;

    private boolean isRefresh;
    private MtLocation location;
    private JSONObject locationInfo = new JSONObject();
    private ArrayList<WifiInfo> wifiInfoList;
    private boolean isWifiRequestComplete;
    private boolean isLocationComplete;
    private String mTargetUrl;
    private EventListener backPressEvent;
    private EventListener locationEvent;
    private EventListener openWeiChat;
    private ConstraintLayout rootContainer;
    private String backPressUrl;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();
        if (intent == null) {
            finish();
            return;
        }
        try {
            Uri uri = getIntent().getData();
            if (uri != null) {
                mTargetUrl = uri.toString();
            }
            CrashReporter.getInstance().init(this, new CrashReporterConfig() {
            });
            setContentView(R.layout.activity_qtitans_flex_page);
            makeImersive();
            initView();
            initLocationInfo();
            initData(intent);
        } catch (Throwable e) {
            QQThrowableReporter.report(TAG, e, true);
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", TAG + " onCreate failed: " + e.getMessage());
            BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);
        }

    }

    private void initLocationInfo() {
        location = MtLocationCache.getInstance()
                .getLastKnownLocationSync("pt-604734193ad1da2b", HadesUtils.getContext());
       locationInfo = getLocationInfo(location);
    }

    private void requestLocationPermissionAndLocated(Intent intentUrl) {
        HadesUtils.runOnWorkThread(()->{
            TitansPermissionUtil.requestPermission(this, PermissionGuard.PERMISSION_LOCATION_ONCE, "pt-6db9656d437e0ec1", false, (granted, code) -> {
                if (granted) {
                    LocationUtils.setChannel(LocationUtils.CHANNEL.MEITUAN);
                    LocationUtils.setUuid(UUIDUtils.getUUID(this));
                    Context context = new WeakReference<Context>(this).get();
                    MasterLocator masterLocator = new MasterLocatorFactoryImpl().createMasterLocator(context, new OkHttpClient(), null, null, "", MasterLocatorFactory.REQUEST_MEITUAN_CITYID, MasterLocatorFactory.PROCESS_MAIN);
                    MtLocationLoaderWrapper locationLoaderWrapper = MtLocationLoaderWrapper.with(this, "pt-6db9656d437e0ec1", masterLocator);
                    if (locationLoaderWrapper != null) {
                        LoadConfig loadConfig = new LoadConfigImpl();
                        Loader<MtLocation> locationLoader = locationLoaderWrapper.createMtLocationLoader(context, LocationLoaderFactory.LoadStrategy.normal, loadConfig);
                        if (locationLoader != null) {
                            locationLoader.registerListener(0, (loader, location) -> {
                                isLocationComplete = true;
                                if (location != null && location.getStatusCode() == 0) {
                                    this.location = location;
                                    if (isWifiRequestComplete) {
                                        refreshPage(intentUrl, context, location, wifiInfoList);
                                    }

                                } else {
                                    Logger.d(TAG, "location failed");
                                }
                            });
                            locationLoader.startLoading();
                        }
                    }
                }
            });
            try {
                JSONObject wifiInfo = new JSONObject();
                wifiInfo.put("type", "wifi_brief");
                Pin.getDeliveryData(wifiInfo.toString(), new PinCallback() {
                    @Override
                    public void onSuccess(JSONObject jsonObject) {
                        try {
                            isWifiRequestComplete = true;
                            String result = jsonObject.getString("result");
                            if (isLocationComplete && !TextUtils.isEmpty(result)) {
                                Type type = new TypeToken<ArrayList<WifiInfo>>() {
                                }.getType();
                               ArrayList<WifiInfo> resultWifiInfo = HadesUtils.fromJson(result, type);
                                wifiInfoList = resultWifiInfo;
                                refreshPage(intentUrl, QtitansFlexContainerActivity.this, location, resultWifiInfo);
                            }
                        } catch (Throwable e) {
                            QQThrowableReporter.report(TAG, e, true);
                            HashMap<String, Object> info = new HashMap<>();
                            info.put("reason", TAG + " getDeliveryData failed: " + e.getMessage());
                            BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errMsg) {
                        isWifiRequestComplete = true;
                        if (isLocationComplete) {
                            refreshPage(intentUrl, QtitansFlexContainerActivity.this, location, null);
                        }
                    }
                });
            } catch (Throwable e) {
                QQThrowableReporter.report(TAG, e, true);
                HashMap<String, Object> info = new HashMap<>();
                info.put("reason", TAG + " requestLocationPermissionAndLocated failed: " + e.getMessage());
                BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);
            }

        });
    }

    private void refreshPage(Intent intentUrl, Context context, MtLocation location, ArrayList<WifiInfo> wifiInfo) {
        QtitansFlexPageRequestManager.getInstance().locationWifiRequestSupplyPageData(context, intentUrl.getData(), location.getLongitude(), location.getLatitude(), wifiInfo, new IFlexPageRequestCallback() {
            @Override
            public void onSuccess(QtitansFlexPageData data) {
                if (data != null && data.hasNext ) {
                    HadesUtils.runOnMainThread(()->{
                        updateFlexPageData(data);
                    });
                    HadesUtils.runOnWorkThread(()->{
                        if (location instanceof WMLocation) return;
                        if (titleFlexView != null) {
                            try {
                                Event event = new Event("locationRefresh", EventScope.PAGE, titleFlexView.getContext());
                                JSONObject locationData = getLocationInfo(location);
                                event.setData(locationData);
                                titleFlexView.sendEvent(event);
                            } catch (Throwable e) {
                                Logger.e(TAG, "location event send failed");
                                HashMap<String, Object> info = new HashMap<>();
                                info.put("reason", TAG + " locationRefresh send event: " + e.getMessage());
                                BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);
                            }

                        }
                    });

                }
            }

            @Override
            public void onFailed(String message) {
                Logger.d(TAG, "location request failed: " + message);
                HashMap<String, Object> info = new HashMap<>();
                info.put("reason", TAG + " locationRefresh send event: " + message);
                BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);

            }
        });
    }

    private static JSONObject getLocationInfo(MtLocation location) {
        JSONObject data = new JSONObject();
        try {
            if (location != null) {
                data.put("provider", location.getProvider());
                data.put("accuracy", (double) location.getAccuracy());
                data.put("latitude", Double.isNaN(location.getLatitude()) ? 0.0 : location.getLatitude());
                data.put("longitude", Double.isNaN(location.getLongitude()) ? 0.0 : location.getLongitude());
                data.putOpt("altitude", location.hasAltitude() ? location.getAltitude() : null);
                data.put("time", location.getTime());
                if (location.getExtras() != null) {
                    data.put("address", location.getExtras().getString("address"));
                    data.put("country", location.getExtras().getString("country"));
                    data.put("province", location.getExtras().getString("province"));
                    data.put("district", location.getExtras().getString("district"));
                    data.put("city", location.getExtras().getString("city"));
                    data.put("detail", location.getExtras().getString("detail"));
                    data.put("adcode", location.getExtras().getString("adcode"));
                }
            }

        } catch (Throwable e) {
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", TAG + " getLocationInfo failed: " + e.getMessage());
            BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);
        }

        return data;
    }

    private void initData(@NonNull Intent intent) {
        if (QtitansFlexPageRequestManager.getInstance().getFlexPageData() != null) {
            Logger.d(TAG, "using preload data");
            updateFlexPageData(QtitansFlexPageRequestManager.getInstance().getFlexPageData());
        } else if (QtitansFlexPageRequestManager.getInstance().getPreloadState() != null && QtitansFlexPageRequestManager.getInstance().getPreloadState() != PreloadState.PRELOAD_FAILED) {
            QtitansFlexPageRequestManager.getInstance().setPreloadCallback(new IFlexPageRequestCallback() {
                @Override
                public void onSuccess(QtitansFlexPageData data) {
                    Logger.d(TAG, "preload has start, waiting result");
                    updateFlexPageData(data);
                }

                @Override
                public void onFailed(String message) {
                    Logger.d(TAG, "preload failed, request data");
                    realRequestFlexPageData(intent);
                }
            });
        } else {
            Logger.d(TAG, "preload not start, request  data");
            realRequestFlexPageData(intent);
        }
    }

    private void realRequestFlexPageData(Intent intent) {
        Uri uri = intent.getData();
        if (uri == null) {
            Logger.e(TAG, "uri null");
            return;
        }
        double[] coordinate = HadesUtilsAdapter.getXy("pt-604734193ad1da2b");
        double lat = 0;
        double lng = 0;
        if (coordinate != null) {
            lat = coordinate[0];
            lng = coordinate[1];
        }
        QtitansFlexPageRequestManager.getInstance().requestSupplyDetailPageData(this, uri, lat, lng, new IFlexPageRequestCallback() {
            @Override
            public void onSuccess(QtitansFlexPageData data) {
                updateFlexPageData(data);
            }

            @Override
            public void onFailed(String message) {
                Logger.e(TAG, "realRequestFlexPageData failed: "+message);
            }
        });
    }

    private void updateFlexPageData(QtitansFlexPageData data) {
        HadesUtils.runOnMainThread(()->{
            if (data == null) return;
            if (QtitansFlexReporter.getInstance() instanceof QtitansFlexReporter) {
                ((QtitansFlexReporter) QtitansFlexReporter.getInstance()).reset();
            }
            if (!TextUtils.isEmpty(data.backgroundImageUrl) && backgroundImg != null) {
                Picasso.with(this).load(data.backgroundImageUrl).into(backgroundImg);
            }
            if (!TextUtils.isEmpty(data.backgroundColor) && rootContainer != null) {
                rootContainer.setBackgroundColor(Color.parseColor(data.backgroundColor));
            }
            if(!TextUtils.isEmpty(data.feedBackgroundColorStart) && !TextUtils.isEmpty(data.feedBackgroundColorEnd)) {
                int startColor = Color.parseColor(data.feedBackgroundColorStart);
                int endColor = Color.parseColor(data.feedBackgroundColorEnd);
                GradientDrawable gradientDrawable = new GradientDrawable(
                        GradientDrawable.Orientation.TOP_BOTTOM,
                        new int[] {startColor, startColor ,endColor,endColor, endColor, endColor, endColor, endColor}
                );
                productListView.setBackground(gradientDrawable);
            }
            updateTitleView(data);
            updateScrollView(data);
            updateFlexList(data.flexDataList);
        });

    }

    private void updateFlexList(List<QtitansFlexData>  items) {
        if (mDynamicAdapter != null) {
            mDynamicAdapter.setData(items);
        }
    }

    private void updateScrollView(QtitansFlexPageData data) {
        try {
            cardLisContainer.removeAllViews();
            HashMap<String, Object> map = new HashMap<>();
            map.put("templateName", data.cardListHeaderTemplateName);
            map.put("templateUrl", data.cardListHeaderTemplateUrl);
            cardListTopView.showView(new JSONObject(map));
            cardLisContainer.addView(cardListTopView);
            if (data.cardListData != null) {
                for (JSONObject cardMap: data.cardListData) {
                    QtitansFlexCardView cardView = QtitansFlexTemplatePreloader.acquireFlexView();
                    if (cardView == null) {
                        cardView = new QtitansFlexCardView(this);
                        ViewGroup.LayoutParams cardLayoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                        cardView.setLayoutParams(cardLayoutParams);
                    }
                    cardLisContainer.addView(cardView);
                    cardView.showView(cardMap);
                }
            }
            JSONObject headerJson = new JSONObject();
            headerJson.put("templateUrl", data.cardFeedHeaderTemplateUrl);
            headerJson.put("templateName",data.cardFeedHeaderTemplateName);
            feedHeaderView.showView(headerJson);
            cardLisContainer.addView(feedHeaderView);

        } catch (Throwable e) {
            QQThrowableReporter.report(TAG, e, true);
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", TAG + " updateScrollView failed: " + e.getMessage());
            BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);
        }
    }

    private void updateTitleView(QtitansFlexPageData data) {
        try {
            JSONObject locationData = locationInfo;
            locationData.put("templateName", data.titleTemplateName);
            locationData.put("templateUrl", data.titleTemplateUrl);
            titleBarContainer.removeAllViews();
            titleBarContainer.addView(titleFlexView);
            titleFlexView.showView(locationData);
            titleFlexView.addEventListener(backPressEvent);
            titleFlexView.addEventListener(locationEvent);
        } catch (Throwable e) {
            QQThrowableReporter.report(TAG, e, true);
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", TAG + " updateTitleView failed: " + e.getMessage());
            BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);
        }

    }

    private void initView() {
        rootContainer = findViewById(R.id.flex_root_container);
        titleBarContainer = findViewById(R.id.title_bar_container);
        statusView = findViewById(R.id.status_bar_view);
        ViewGroup.LayoutParams layoutParams = statusView.getLayoutParams();
        if (layoutParams != null) {
            layoutParams.height = HadesUtils.getStatusBarHeight(this);
            statusView.setLayoutParams(layoutParams);
        }
        cardLisContainer = findViewById(R.id.card_list_container);
        backgroundImg = findViewById(R.id.background_image);
        Picasso.with(this).load("https://p0.meituan.net/pushresource/6b797792fd617eb2925cc9292884e1b5875722.png").into(backgroundImg);
        titleFlexView = QtitansFlexTemplatePreloader.acquireFlexView();
        if (titleFlexView == null) {
            titleFlexView = new QtitansFlexCardView(this);
            LinearLayout.LayoutParams titleLayoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            titleFlexView.setLayoutParams(titleLayoutParams);
        }

        backPressEvent = new EventListener("backPressed", EventScope.PAGE, null) {
            @Override
            public void handleEvent(Event event, LayoutController layoutController) {
                JSONObject data = event.getData();
                if (data != null) {
                    try {
                        Logger.d(TAG, "backPressed event received");
                        String targetUrl = data.getString("targetUrl");
                        String action = data.getString("action");
                        if ("interceptLeft".equals(action)) {
                            backPressUrl = targetUrl;
                            //预留左滑拦截，页面初始化时发送事件
                            return;
                        }
                        if (TextUtils.isEmpty(targetUrl)) {
                            QtitansFlexContainerActivity.this.onBackPressed();
                        } else {
                            RouterManager.jumpToBizActivity(QtitansFlexContainerActivity.this, targetUrl);
                        }
                    } catch (Throwable e) {
                        QQThrowableReporter.report(TAG, e, true);
                    }
                }
            }
        };
        locationEvent = new EventListener("selectLocation", EventScope.PAGE, null) {
            @Override
            public void handleEvent(Event event, LayoutController layoutController) {
                try {
                    if (event.getData() == null) return;
                    String targetUrl = event.getData().getString("targetUrl");
                    if (!TextUtils.isEmpty(targetUrl)) {
                        Intent intent = new Intent(Intent.ACTION_VIEW);
                        intent.setData(Uri.parse(targetUrl));
                        intent.putExtra(Dsp.INTENT_EXTRA_IS_DSP_COLD_START, true);
                        QtitansFlexContainerActivity.this.startActivityForResult(intent, 101);
                    }

                } catch (Throwable e) {
                    QQThrowableReporter.report(TAG, e, true);
                    HashMap<String, Object> info = new HashMap<>();
                    info.put("reason", TAG + " locationEvent handleEvent failed: " + e.getMessage());
                    BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);
                }
            }
        };

        cardListTopView = QtitansFlexTemplatePreloader.acquireFlexView();
        if (cardListTopView == null) {
            cardListTopView = new QtitansFlexCardView(this);
            LinearLayout.LayoutParams cardTopLayoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            cardListTopView.setLayoutParams(cardTopLayoutParams);
        }
        feedHeaderView = QtitansFlexTemplatePreloader.acquireFlexView();
        if (feedHeaderView == null) {
            feedHeaderView = new QtitansFlexCardView(this);
            ViewGroup.LayoutParams headerLayoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            feedHeaderView.setLayoutParams(headerLayoutParams);
        }
        initFlexListView();
    }

    private void initFlexListView() {
        productListView = findViewById(R.id.product_info_list);
        GradientDrawable gradientDrawable = new GradientDrawable(
                GradientDrawable.Orientation.TOP_BOTTOM,
                new int[] {0xFFFFFFFF, 0xFFFFFFFF,0xFFF5F5F5,0xFFF5F5F5,0xFFF5F5F5,0xFFF5F5F5,0xFFF5F5F5,0xFFF5F5F5}
        );
        productListView.setBackground(gradientDrawable);
        mDynamicAdapter = new QtitansFlexListAdapter(productListView);
        StaggeredGridLayoutManager staggeredGridLayoutManager = new StaggeredGridLayoutManager(DEFAULT_SPAN, LinearLayoutManager.VERTICAL);
        productListView.setAdapter(mDynamicAdapter);
        productListView.addItemDecoration(new QtitansFlexItemDecoration());
        productListView.setLayoutManager(staggeredGridLayoutManager);
        mDynamicAdapter.setLoadMoreListener(() -> {
            if (mDynamicAdapter != null && QtitansFlexPageRequestManager.getInstance().getFlexPageData() != null && QtitansFlexPageRequestManager.getInstance().getFlexPageData().hasNext) {
                double lat = 0;
                double lng = 0;
                if (location != null) {
                    lat = location.getLatitude();
                    lng = location.getLongitude();
                }
                QtitansFlexPageRequestManager.getInstance().requestLoadMoreSupplyPageData(QtitansFlexContainerActivity.this, lat, lng, new IFlexPageRequestCallback() {
                    @Override
                    public void onSuccess(QtitansFlexPageData pageData) {
                        HadesUtils.runOnMainThread(()->{
                            mDynamicAdapter.hideLoading();
                            if (pageData != null && pageData.flexDataList != null && !pageData.flexDataList.isEmpty() && mDynamicAdapter != null) {
                                mDynamicAdapter.addData(pageData.flexDataList);
                            }
                        });
                    }
                    @Override
                    public void onFailed(String message) {
                        Logger.e(TAG, "load more request failed: " + message);
                        mDynamicAdapter.hideLoading();
                    }
                });
            }
        });
    }

    private void makeImersive() {
        Window window = getWindow();
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.setStatusBarColor(Color.TRANSPARENT);
    }

    @Override
    protected void onResume() {
        super.onResume();
        try {
            PushReporter.reportFlexPagePv(mTargetUrl, false);
            //部分情况onWindowFocus未回调
            HadesUtils.runOnMainThreadWithDelay(()->{
                if (HadesUtils.isScreenOn(this) && !HadesUtils.isLocked() && !isRefresh) {
                    requestLocationPermissionAndLocated(getIntent());
                    PushReporter.reportFlexPagePv(mTargetUrl, false);
                    isRefresh = true;
                }
            },500);
        } catch (Throwable e) {
            QQThrowableReporter.report(TAG, e,true);
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", TAG + " onResume failed: " + e.getMessage());
            BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);
        }
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus && !isRefresh) {
            requestLocationPermissionAndLocated(getIntent());
            PushReporter.reportFlexPagePv(mTargetUrl, true);
            isRefresh = true;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null && data.hasExtra("selected_address")) {
            HadesUtils.runOnWorkThread(()->{
                if (BuildConfig.LOG_ENABLE) {
                    Logger.d(TAG, "location change:"+data.getStringExtra("selected_address"));
                }
                if (titleFlexView != null) {
                    try {
                        String addressStr = data.getStringExtra("selected_address");
                        if (addressStr != null) {
                            WmAddress address = WmAddress.parse(addressStr);
                            JSONObject addressJson = new JSONObject(addressStr);
                            Event event = new Event("onAddressChanged", EventScope.PAGE, this);
                            event.setData(addressJson);
                            titleFlexView.sendEvent(event);
                            if (address != null && address.getWMLocation() != null) {
                                location = address.getWMLocation();
                                refreshPage(getIntent(), this, address.getWMLocation(), null);
                            }
                        }

                    } catch (Throwable e) {
                       QQThrowableReporter.report(TAG, e, true);
                        HashMap<String, Object> info = new HashMap<>();
                        info.put("reason", TAG + " onResume failed: " + e.getMessage());
                        BabelHelper.log(BABEL_CUSTOM_PAGE_REPORT, info);
                    }

                }

            });
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        QtitansFlexPageRequestManager.getInstance().reset();
        location = null;
        wifiInfoList = null;
        backPressUrl = null;
        if (QtitansFlexReporter.getInstance() instanceof QtitansFlexReporter) {
            ((QtitansFlexReporter) QtitansFlexReporter.getInstance()).reset();
        }
    }

    @Override
    public void onBackPressed() {
        if (!TextUtils.isEmpty(backPressUrl)) {
            RouterManager.jumpToBizActivity(this, backPressUrl);
        } else {
            super.onBackPressed();
        }
    }
}
