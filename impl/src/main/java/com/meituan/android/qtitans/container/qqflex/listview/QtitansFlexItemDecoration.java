package com.meituan.android.qtitans.container.qqflex.listview;

import android.graphics.Rect;
import android.support.v7.widget.RecyclerView;
import android.support.v7.widget.StaggeredGridLayoutManager;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.qtitans.container.common.QtitansUtils;

public class QtitansFlexItemDecoration extends RecyclerView.ItemDecoration {
    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        outRect.left = 0;
        outRect.top = 0;
        outRect.bottom = 0;
        outRect.right = 0;
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (layoutParams instanceof StaggeredGridLayoutManager.LayoutParams) {
            StaggeredGridLayoutManager layoutManager = (StaggeredGridLayoutManager) parent.getLayoutManager();
            StaggeredGridLayoutManager.LayoutParams layoutParamsS = (StaggeredGridLayoutManager.LayoutParams) layoutParams;
            int position = layoutManager.getPosition(view);
            if (parent.getAdapter() instanceof QtitansFlexListAdapter && ((QtitansFlexListAdapter) parent.getAdapter()).getData().size() > position ) {
                QtitansBaseData baseData = ((QtitansFlexListAdapter) parent.getAdapter()).getData().get(position);
                if (baseData.style != null) {
                    if (layoutParamsS.getSpanIndex() == 0 && layoutManager.getSpanCount() != 1) {
                        outRect.left = QtitansUtils.dip2px(parent.getContext(),baseData.style.left);
                        outRect.top = QtitansUtils.dip2px(parent.getContext(),baseData.style.top);
                        outRect.bottom =  QtitansUtils.dip2px(parent.getContext(),baseData.style.bottom);
                        outRect.right =  QtitansUtils.dip2px(parent.getContext(),baseData.style.right);
                    } else if (layoutParamsS.getSpanIndex() == 1 && layoutManager.getSpanCount() != 1) {
                        outRect.left = QtitansUtils.dip2px(parent.getContext(),baseData.style.right);
                        outRect.top = QtitansUtils.dip2px(parent.getContext(),baseData.style.top);
                        outRect.bottom =  QtitansUtils.dip2px(parent.getContext(),baseData.style.bottom);
                        outRect.right =  QtitansUtils.dip2px(parent.getContext(),baseData.style.left);
                    } else if (layoutManager.getSpanCount() == 1) {
                        outRect.left = QtitansUtils.dip2px(parent.getContext(),baseData.style.left);
                        outRect.top = QtitansUtils.dip2px(parent.getContext(),baseData.style.top);
                        outRect.bottom =  QtitansUtils.dip2px(parent.getContext(),baseData.style.bottom);
                        outRect.right =  QtitansUtils.dip2px(parent.getContext(),baseData.style.right);
                    }
                }

            }

        }
    }
}
