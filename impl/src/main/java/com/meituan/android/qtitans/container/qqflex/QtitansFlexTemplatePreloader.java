package com.meituan.android.qtitans.container.qqflex;

import android.content.Context;
import android.view.ViewGroup;

import com.meituan.android.dynamiclayout.adapters.LayoutLoader;
import com.meituan.android.dynamiclayout.controller.DynamicTemplatePreloader;
import com.meituan.android.hades.impl.BuildConfig;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class QtitansFlexTemplatePreloader {
    private static final String TAG = "QtitansDynamicPreloader";
    private static final ArrayList<QtitansFlexCardView> flexItems = new ArrayList<>();
    public static void preloadTemplates(int mode, String activityName) {
        HadesUtils.runOnExecutor(()->{
            Map<String, ArrayList<String>> map = HadesUtils.getFlexBoxTemplatePreload();
            if (!map.isEmpty()) {
                List<String> tmpList = map.get("templateList");
                List<String> nameList = map.get("nameList");
                preloadInner(mode, activityName, nameList, tmpList);
            }

        });
    }

    public static void preloadSaveMoneyTemplates(int mode, String activityName) {
        HadesUtils.runOnExecutor(()->{
            Map<String, ArrayList<String>> map = HadesUtils.getSaveMoneyFlexTemplate();
            if (!map.isEmpty()) {
                List<String> tmpList = map.get("templateList");
                List<String> nameList = map.get("nameList");
                preloadInner(mode, activityName, nameList, tmpList);
            }

        });
    }

    private static void preloadInner(int mode, String activityName, List<String> nameList, List<String> templateList) {
        try {
            List<String> templates = new ArrayList<>();
            List<String> names = new ArrayList<>();
            if (templateList != null && !templateList.isEmpty() ) {
                templates.addAll(templateList);
            }
            if (nameList != null && !nameList.isEmpty()) {
                names.addAll(nameList);
            }

            if (BuildConfig.LOG_ENABLE) {
                Logger.d(TAG, "preload template list size: " + names.size());
            }
            preloadInnerNew(mode, activityName,  templates, names);
        } catch (Throwable e) {
            if (BuildConfig.LOG_ENABLE) {
                Logger.e(TAG, "preload template failed: " + e.getMessage());
            }

        }
    }

    private static void preloadInnerNew(int mode,String activityName,  List<String> templates, List<String> names) {
        DynamicTemplatePreloader preLoader = new DynamicTemplatePreloader.Builder()
                .urlList(templates)
                .nameList(names)
                .activityName(activityName)
                .moduleName("qqflex")
                .layoutLoader(new LayoutLoader())
                .preloadMode(mode)
                .build();
        preLoader.preload();
    }

    public static void preCreateFlexView(Context context, int size) {
        for (int i=0; i < size; i++) {
            QtitansFlexCardView view = new QtitansFlexCardView(context);
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            view.setLayoutParams(layoutParams);
            flexItems.add(view);
        }
    }

    public static QtitansFlexCardView acquireFlexView() {
        try {
            if (flexItems.isEmpty()) return null;
            return flexItems.remove(flexItems.size() - 1);
        } catch (Throwable e) {
            QQThrowableReporter.report(TAG,e, true);
        }
      return null;
    }
}
