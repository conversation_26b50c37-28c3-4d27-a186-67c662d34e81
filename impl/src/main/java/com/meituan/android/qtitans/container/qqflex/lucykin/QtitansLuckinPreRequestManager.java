package com.meituan.android.qtitans.container.qqflex.lucykin;

import static com.meituan.android.hades.dyadater.container.ContainerConst.BABEL_TYPE_SMC_REPORT;

import android.content.Context;
import android.net.Uri;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.google.gson.JsonObject;
import com.meituan.android.hades.dyadater.utils.HadesUtilsAdapter;
import com.meituan.android.hades.impl.model.BaseResponse;
import com.meituan.android.hades.impl.report.BabelHelper;
import com.meituan.android.hades.impl.report.QQThrowableReporter;
import com.meituan.android.hades.impl.utils.HadesUtils;
import com.meituan.android.hades.impl.utils.Logger;
import com.meituan.android.hades.impl.utils.StorageHelper;
import com.meituan.android.qtitans.container.qqflex.QtitansFlexRetrofit;
import com.meituan.android.qtitans.container.qqflex.lucykin.model.LuckinMainPage;
import com.meituan.android.qtitans.container.qqflex.lucykin.model.LuckinQuerySpuDetail;
import com.meituan.android.hades.dyadater.container.PreloadState;
import com.sankuai.common.utils.JsonHelper;
import com.sankuai.meituan.retrofit2.Response;

import java.util.HashMap;
import java.util.Set;

public class QtitansLuckinPreRequestManager {

    private static final String TAG = QtitansLuckinPreRequestManager.class.getSimpleName();
    private static final QtitansLuckinPreRequestManager INSTANCE = new QtitansLuckinPreRequestManager();

    public static QtitansLuckinPreRequestManager getInstance() {
        return INSTANCE;
    }

    public static final String ONLINE_HOST = "https://kk.meituan.com/";
    public static final String ONLINE_SHOP_HOST = "https://apimobile.meituan.com/";

    private IFlexLuckinPageRequestCallback requestCallback;
    private IFlexLuckinPageRequestCallback preRequestCallback;
    PreloadState preloadState;

    private LuckinMainPage mainPage;

    public LuckinMainPage getCacheData() {
        return cacheData;
    }

    private LuckinMainPage cacheData;

    public LuckinMainPage getOrderPage() {
        return orderPage;
    }

    private LuckinMainPage orderPage;

    public LuckinMainPage getShopPage() {
        return shopPage;
    }

    private LuckinMainPage shopPage;

    public void setLng(double lng) {
        this.lng = lng;
    }

    public void setLat(double lat) {
        this.lat = lat;
    }

    private double lng;

    public double getLng() {
        return lng;
    }

    public double getLat() {
        return lat;
    }

    private double lat;

    public String getBrandId() {
        return brandId;
    }

    private String brandId;

    public String getPoiId() {
        return poiId;
    }

    public void setPoiId(String poiId) {
        this.poiId = poiId;
    }

    private String poiId;

    public String getUtmWidgetLchVal() {
        return utmWidgetLchVal;
    }
    private String utmWidgetLchVal;

    public void requestSaveMoneyDealQuery(@NonNull Context context, String url, IFlexLuckinPageRequestCallback requestCallback) {
        double[] coordinate = HadesUtilsAdapter.getXy("pt-604734193ad1da2b");
        if (coordinate != null) {
            lat = coordinate[0];
            lng = coordinate[1];
        }
        requestSaveMoneyDealQueryByShop(context, url, "", lat, lng, requestCallback);
    }

    public void requestSaveMoneyDealQueryByShop(@NonNull Context context, @NonNull String url, String poiId, double lat, double lng, IFlexLuckinPageRequestCallback requestCallback) {
        try {
            if (TextUtils.isEmpty(url)) {
                Logger.d(TAG, "save money url null");
                return;
            }
            Uri uri = Uri.parse(url);
            String utmValue = uri.getQueryParameter("utm_qq_widget_lch_2025");
            if (!TextUtils.isEmpty(utmValue)) {
                utmWidgetLchVal = utmValue;
            }
            String webUrl = uri.getQueryParameter("url");
            if (TextUtils.isEmpty(webUrl)) {
                Logger.d(TAG, "save money web url null, not deal request");
                return;
            }
            Uri webUri = Uri.parse(webUrl);
            brandId = webUri.getQueryParameter("brandId");
            double latTmp = lat;
            double lngTmp = lng;
            if (latTmp < 0 && lngTmp < 0) {
                double[] coordinate = HadesUtilsAdapter.getXy("pt-604734193ad1da2b");
                if (coordinate != null) {
                    latTmp = coordinate[0];
                    lngTmp = coordinate[1];
                }
            } else {
                this.lat = lat;
                this.lng = lng;
            }
            if (!TextUtils.isEmpty(poiId)) {
                this.poiId = poiId;
            }
            HashMap<String, Object> map = new HashMap<>(getQueryMapFromUri(webUri));
            map.put("lat", latTmp);
            map.put("lng", lngTmp);
            map.put("poiId", TextUtils.isEmpty(poiId) ? this.poiId:poiId);
            Logger.d(TAG, "request save money deal:lat:" + latTmp+ ",lng:" + lngTmp + ",poiId:" + poiId);
            requestLuckinPage(context, map, requestCallback);

        } catch (Throwable e) {
            QQThrowableReporter.report(TAG, e, true);
            HashMap<String, Object> info = new HashMap<>();
            info.put("reason", TAG+ ": requestSaveMoneyDealQueryByShop failed: " + e.getMessage());
            BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
        }

    }

    public void refreshSkuName(Context context, String topSpuName, IFlexLuckinPageRequestCallback requestCallback) {
        HadesUtils.runOnWorkThread(() -> {
            HashMap<String, Object> map = new HashMap<>();
            double tmplat = getLat();
            double tmpLng = getLng();
            if (tmplat < 0) {
                double[] coordinate = HadesUtilsAdapter.getXy("pt-604734193ad1da2b");
                if (coordinate != null && coordinate.length > 0 ) {
                    tmplat = coordinate[0];
                    tmpLng = coordinate[1];
                }
            }
            map.put("lat", tmplat);
            map.put("lng", tmpLng);
            map.put("poiId", poiId);
            map.put("topSpuName", topSpuName);
            map.put("brandId", brandId);
            requestLuckinPage(context, map, requestCallback);
        });
    }

    private void requestLuckinPage(Context context, HashMap<String, Object> map, IFlexLuckinPageRequestCallback requestCallback) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                Response<BaseResponse<LuckinMainPage>> response = QtitansFlexRetrofit.getInstance(context, ONLINE_HOST).getSaveMoneyDealQuery(map).execute();
                if (response != null && response.body() != null && response.body().hasData()) {
                    mainPage = response.body().data;
                    if (!TextUtils.isEmpty(utmWidgetLchVal)) {
                        mainPage.updateUtmLchParams(utmWidgetLchVal);
                    }
                    if (requestCallback != null) {
                        requestCallback.onSuccess(mainPage);
                    } else {
                        if (preRequestCallback != null) {
                            preRequestCallback.onSuccess(mainPage);
                        }
                    }
                } else {
                    if (requestCallback != null) {
                        requestCallback.onFailed("request save money deal query failed，"+(response != null && response.body() != null? response.body().msg:""));
                    } else {
                        if (preRequestCallback != null) {
                            preRequestCallback.onFailed("pre request save money deal query failed"+(response != null && response.body() != null? response.body().msg:""));
                        }
                    }

                }
            } catch (Throwable e) {
                if (requestCallback != null) {
                    requestCallback.onFailed("request save money deal query failed:" + e.getMessage());
                } else {
                    if (preRequestCallback != null) {
                        preRequestCallback.onFailed("pre request save money deal query failed" + e.getMessage());
                    }
                }
            }
        });
    }

    public void requestLuckinHostoryOrderQuery(Context context, IFlexLuckinPageRequestCallback requestCallback) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                Response<BaseResponse<LuckinMainPage>> response = QtitansFlexRetrofit.getInstance(context, ONLINE_HOST).getSaveMoneyOrderQuery(brandId).execute();
                if (response != null && response.body() != null && response.body().hasData()) {
                    orderPage = response.body().data;
                    if (requestCallback != null) {
                        if (!TextUtils.isEmpty(utmWidgetLchVal)) {
                            orderPage.updateUtmLchParams(utmWidgetLchVal);
                        }
                        requestCallback.onSuccess(orderPage);
                    }
                } else {
                    if (requestCallback != null) {
                        requestCallback.onFailed("request luckin hostory order query failed" + (response != null && response.body() != null ? response.body().msg : ""));
                    }
                }
            } catch (Throwable e) {
                if (requestCallback != null) {
                    requestCallback.onFailed(e.getMessage());
                }
            }
        });
    }

    public void requestNearbyShops(Context context, LuckinMainPage data, IFlexLuckinPageRequestCallback requestCallback) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                if (mainPage == null) return;
                if (TextUtils.isEmpty(QtitansLuckinPreRequestManager.getInstance().getBrandId())) {
                    HashMap<String, Object> info = new HashMap<>();
                    info.put("reason", TAG+ ": requestNearbyShops brandId null");
                    BabelHelper.log(BABEL_TYPE_SMC_REPORT, info);
                    return;
                }
                double tmplat = getLat();
                double tmpLng = getLng();
                if (tmplat < 0) {
                    double[] coordinate = HadesUtilsAdapter.getXy("pt-604734193ad1da2b");
                    if (coordinate != null && coordinate.length > 0 ) {
                        tmplat = coordinate[0];
                        tmpLng = coordinate[1];
                    }
                }
                reportToolsApiLimit(context, "/mop/api/shop/nearbyShops");
                Response<BaseResponse<LuckinMainPage>> response = QtitansFlexRetrofit.getInstance(context, ONLINE_SHOP_HOST).getNearByShops(data, tmplat, tmpLng).execute();
                if (response != null && response.body() != null && response.body().data != null) {
                    shopPage = response.body().data;
                    if (requestCallback != null) {
                        if (!TextUtils.isEmpty(utmWidgetLchVal)) {
                            shopPage.updateUtmLchParams(utmWidgetLchVal);
                        }
                        requestCallback.onSuccess(shopPage);
                    }

                } else {
                    if (requestCallback != null) {
                        requestCallback.onFailed("request shop list failed" + (response != null && response.body() != null? response.body().msg:""));
                    }
                }

            } catch (Throwable e) {
                if (requestCallback != null) {
                    requestCallback.onFailed(e.getMessage());
                }
            }

        });
    }

    public void querySpuDetail(Context context, String spuId, String skuId, String shopId, String circledSkuIds, IFlexLuckinSpuDetailCallback callback) {
        HadesUtils.runOnWorkThread(() -> {
            try {
                reportToolsApiLimit(context, "/mop/api/menu/querySpuDetail");
                HashMap<String, Object> map = new HashMap<>();
                map.put("spuId", spuId);
                map.put("skuId", skuId);
                map.put("shopId", shopId);
                map.put("isNative", true);
                map.put("circledSkuIds", circledSkuIds);
                map.put("brandId", brandId);
                if (getLat() > 0) {
                    map.put("latitude", lat);
                    map.put("longitude", lng);
                }
                Response<BaseResponse<LuckinQuerySpuDetail>> response = QtitansFlexRetrofit.getInstance(context, ONLINE_SHOP_HOST).querySpuDetail(map).execute();
                if (response.body() != null && response.body().data != null && response.code() == 200) {
                    callback.onSuccess(response.body().data);
                } else {
                    callback.onFailed("request spu failed" +(response.body() != null ? response.body().msg:""));
                }
            } catch (Throwable e) {
                QQThrowableReporter.report("querySpuDetail", e, true);
            }
        });
    }

    public void reportToolsApiLimit(Context context, String url) {
        HadesUtils.runOnWorkThread(()->{
            try {
                QtitansFlexRetrofit.getInstance(context).reportToolApiLimit(url).execute();
            } catch (Throwable e) {

            }

        });

    }

    private HashMap<String, Object> getQueryMapFromUri(Uri uri) {
        HashMap<String, Object> queryMap = new HashMap<>();
        Set<String> params = uri.getQueryParameterNames();
        if (params != null && !params.isEmpty()) {
            for (String param : params) {
                if (!queryMap.containsKey(param)) {
                    queryMap.put(param, uri.getQueryParameter(param));
                }
            }
        }
        return queryMap;
    }

    public IFlexLuckinPageRequestCallback getRequestCallback() {
        return requestCallback;
    }

    public void setRequestCallback(IFlexLuckinPageRequestCallback requestCallback) {
        this.requestCallback = requestCallback;
    }

    public IFlexLuckinPageRequestCallback getPreRequestCallback() {
        return preRequestCallback;
    }

    public void setPreRequestCallback(IFlexLuckinPageRequestCallback preRequestCallback) {
        this.preRequestCallback = preRequestCallback;
    }

    public LuckinMainPage getMainPage() {
        return mainPage;
    }

    public PreloadState getPreloadState() {
        return preloadState;
    }

    public void setPreloadState(PreloadState preloadState) {
        this.preloadState = preloadState;
    }

    public void readLuckinPageCacheData() {
        if (!HadesUtils.enableLuckinPageCache()) {
            Logger.d(TAG, "read page cache switch close");
            return;
        }
        HadesUtils.runOnWorkThread(()->{
            try {
                Logger.d(TAG, "read page cache start");
                String originData = StorageHelper.getLuckinPageDealCache();
                if (TextUtils.isEmpty(originData)) {
                    Logger.d("luckincache", "no cache data");
                    return;
                }
                JsonObject data = JsonHelper.toJsonObject(originData);
                if (data == null) {
                    Logger.d("luckincache", "parse cache data failed");
                    return;
                }
                LuckinMainPage luckinMainPage = new LuckinMainPage().getLuckinMainPage(data, true);
                if (luckinMainPage != null) {
                    cacheData = luckinMainPage;
                }
                Logger.d(TAG, "read page cache end");
            } catch (Throwable e) {
                Logger.e(TAG, "reading cache data failed");
            }

        });

    }

}
