package com.meituan.android.hades.dyadater.container.adapter;

import android.content.Context;
import android.support.annotation.Keep;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.meituan.android.dynamiclayout.controller.LayoutController;
import com.meituan.android.dynamiclayout.controller.event.Event;
import com.meituan.android.dynamiclayout.controller.event.EventListener;
import com.meituan.android.dynamiclayout.controller.event.EventScope;
import com.meituan.android.dynamiclayout.controller.presenter.TemplateData;
import com.meituan.android.hades.dyadater.container.FlexEvent;
import com.meituan.android.hades.dyadater.container.FlexEventListener;
import com.meituan.android.hades.dyadater.desk.IFlexViewClickEventListenerAdapter;
import com.meituan.android.hades.dyadater.desk.IFlexViewShowStatusListenerAdapter;
import com.meituan.android.hades.dyadater.desk.ui.QQFlexEvent;
import com.meituan.android.qtitans.container.qqflex.IFlexCardShowStatusListener;
import com.meituan.android.qtitans.container.qqflex.QtitansFlexCardView;
import com.meituan.android.qtitans.container.qqflex.event.IFlexViewClickEventListener;
import com.meituan.crashreporter.CrashReporter;
import com.meituan.crashreporter.CrashReporterConfig;

import org.json.JSONObject;

import java.util.HashMap;

@Keep
public class QtitansFlexCardViewAdapter {
    QtitansFlexCardView mFlexCardView;
    public QtitansFlexCardViewAdapter(Context context) {
        mFlexCardView = new QtitansFlexCardView(context);
    }

    public View getView() {
        return mFlexCardView;
    }

    public void showView(JSONObject data) {
        mFlexCardView.showView(data);
    }
    public void setOnClickListener(View.OnClickListener listener) {
        mFlexCardView.setOnClickListener(listener);
    }

    public void addEventListener(FlexEventListener listener) {
        if (listener != null) {
            EventListener eventListener = new EventListener(listener.getAction(), EventScope.get(listener.getScope()), null) {
                @Override
                public void handleEvent(Event event, LayoutController layoutController) {
                    listener.handleEventData(event.getData());
                }
            };
            mFlexCardView.addEventListener(eventListener);
        }
    }

    public void setClickEventListener(IFlexViewClickEventListener listener) {
         mFlexCardView.setClickEventListener(listener);
    }

    public void sendEvent(FlexEvent event) {
        if (event != null) {
            Event event1 = new Event(event.getActionName(), EventScope.get(event.getScope()), mFlexCardView.getContext());
            if (event.getData() != null) {
                event1.setData(event.getData());
            }
            mFlexCardView.sendEvent(event1);
        }

    }

    public void setLayoutParams(ViewGroup.LayoutParams params) {
        mFlexCardView.setLayoutParams(params);
    }


}
