package com.meituan.android.hades.dyadater.desk.ui;

import android.content.Context;
import android.support.annotation.Keep;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.meituan.android.dynamiclayout.controller.event.Event;
import com.meituan.android.dynamiclayout.controller.event.EventScope;
import com.meituan.android.dynamiclayout.controller.presenter.TemplateData;
import com.meituan.android.hades.dyadater.desk.IFlexViewClickEventListenerAdapter;
import com.meituan.android.hades.dyadater.desk.IFlexViewShowStatusListenerAdapter;
import com.meituan.android.hades.impl.R;
import com.meituan.android.qtitans.container.qqflex.IFlexCardShowStatusListener;
import com.meituan.android.qtitans.container.qqflex.QtitansFlexCardView;
import com.meituan.crashreporter.CrashReporter;
import com.meituan.crashreporter.CrashReporterConfig;

import org.json.JSONObject;

import java.util.HashMap;

@Keep
public class DeskFloatWinViewAdapter {
    public static QtitansFlexCardView cardView;

    /**
     * TopFloatWin
     */
    public static int get_slide_out_to_top() {
        return R.anim.slide_out_to_top;
    }

    public static int get_top_floatwin_icon() {
        return R.id.top_floatwin_icon;
    }

    public static int get_top_floatwin_illustration() {
        return R.id.top_floatwin_illustration;
    }

    public static int get_top_floatwin_name() {
        return R.id.top_floatwin_name;
    }

    public static int get_top_floatwin_title() {
        return R.id.top_floatwin_title;
    }

    public static int get_top_floatwin_content() {
        return R.id.top_floatwin_content;
    }

    public static int get_layout_floatwin_base() {
        return R.id.layout_floatwin_base;
    }

    public static int get_hades_top_floatwin_multi_element_honor() {
        return R.layout.hades_top_floatwin_multi_element_honor;
    }

    public static int get_hades_top_floatwin_multi_element_huawei() {
        return R.layout.hades_top_floatwin_multi_element_huawei;
    }

    public static int get_hades_top_floatwin_multi_element_xiaomi() {
        return R.layout.hades_top_floatwin_multi_element_xiaomi;
    }

    public static int get_hades_top_floatwin_multi_element_vivo() {
        return R.layout.hades_top_floatwin_multi_element_vivo;
    }

    public static int get_hades_top_floatwin_multi_element_oppo() {
        return R.layout.hades_top_floatwin_multi_element_oppo;
    }

    public static int get_hades_top_scroll_floatwin() {
        return R.layout.hades_top_scroll_floatwin;
    }

    public static int get_hades_top_scroll_y_floatwin() {
        return R.layout.hades_top_scroll_y_floatwin;
    }

    public static int get_hades_top_floatwin() {
        return R.layout.hades_top_floatwin;
    }

    public static int get_close_push() {
        return R.id.close_push;
    }

    public static int get_close_push_background() {
        return R.id.close_push_background;
    }

    public static int get_close_push_desk_image() {
        return R.id.close_push_desk_image;
    }

    public static int close_push_multi_element() {
        return R.id.close_push_multi_element;
    }

    public static int get_ll_multi_element() {
        return R.id.ll_multi_element;
    }

    public static int getFloatWinRootViewId() {
        return R.id.floatwin_root;
    }

    public static int getDeskImageViewId() {
        return R.id.floatwin_desk_image;
    }

    public static int get_float_win_close_background() {
        return R.drawable.float_win_close_background;
    }

    public static int get_float_win_close_after_background() {
        return R.drawable.float_win_close_after_background;
    }

    /**
     *  BottomFloatWin
     */

    public static int get_hades_bottom_floatwin() {
        return R.layout.hades_bottom_floatwin;
    }

    public static int get_floatwin_x_btn() {
        return R.id.floatwin_x_btn;
    }

    public static int get_floatwin_close_btn() {
        return 0;
    }

    /**
     * CenterFloatWin
     */

    public static int get_hades_center_floatwin() {
        return R.layout.hades_center_floatwin;
    }

    /**
     * FullSrcFloatWin
     */
    public static int get_hades_full_floatwin() {
        return R.layout.hades_full_act_layout;
    }

    public static int get_full_scr_background() {
        return R.id.full_scr_background;
    }

    public static int get_full_scr_mask() {
        return R.id.full_scr_mask;
    }

    public static int get_full_desk_center_image() {
        return R.id.desk_center_image;
    }

    public static int get_full_desk_bottom_image() {
        return R.id.desk_bottom_image;
    }

    public static int get_full_container_view() {
        return R.id.full_container_view;
    }

    public static int get_full_action_area() {
        return R.id.action_area;
    }

    public static int get_full_no_action_area() {
        return R.id.no_action_area;
    }

    public static int get_full_close_image() {
        return R.id.close_image;
    }

    public static int get_full_root_layout() {
        return R.id.full_scr_root_layout;
    }

//    public static int get_full_scr_dialog() {
//        return R.id.full_scr_dialog;
//    }

    public static int get_full_close_image_left() {
        return R.id.close_image_left;
    }

    public static int get_full_close_image_right() {
        return R.id.close_image_right;
    }

    public static int get_full_close_image_bottom() {
        return R.id.close_image_bottom;
    }

    public static int get_full_close_image_bottom_right() {
        return R.id.close_image_bottom_right;
    }

    public static int get_full_close_image_bottom_left() {
        return R.id.close_image_bottom_left;
    }

    public static int get_ic_close() {
        return R.drawable.hades_ic_close_center;
    }

    /**
     * AbsFloatWin
     */

//    public static int get_floatwin_close_stub() {
//        return R.id.floatwin_close_stub;
//    }
//
//    public static int get_floatwin_close_layout() {
//        return R.id.floatwin_close_layout;
//    }
//
//    public static int get_text_close_temp() {
//        return R.id.text_close_temp;
//    }
//
//    public static int get_text_close_7days() {
//        return R.id.text_close_7days;
//    }
//
//    public static int get_text_feedback() {
//        return R.id.text_feedback;
//    }

    public static int get_qq_scale_anim() {
        return R.anim.qq_scale_anim;
    }

    public static int get_hades_sticky_floatwin() {
        return R.layout.hades_sticky_floatwin;
    }

    public static int get_slide_in_from_top() {
        return R.anim.slide_in_from_top;
    }


    /**
     * CapsuleFloatWin
     * @return
     */
    public static int get_capsule_scr_background() {
        return R.id.capsule_scr_background;
    }

    public static int get_yellow_capsule() {
        return R.id.yellow_capsule;
    }

    public static int get_capsule_image() {
        return R.id.capsule_image;
    }

    public static int get_wait_pay_text() {
        return R.id.wait_pay_text;
    }

    public static int get_wait_pay_time() {
        return R.id.wait_pay_time;
    }

    public static int get_bottom_win() {
        return R.id.bottom_win;
    }

    public static int get_close_remind() {
        return R.id.close_remind;
    }

    public static int get_tv_close_remind() {
        return R.id.tv_close_remind;
    }

    public static int get_iv_close_remind() {
        return R.id.iv_close_remind;
    }

    public static int get_check_order() {
        return R.id.check_order;
    }

    public static int get_check_order_tv() {
        return R.id.check_order_tv;
    }

    public static int get_check_order_iv() {
        return R.id.check_order_iv;
    }

    public static int get_capsule_space() {
        return R.id.capsule_space;
    }

    public static int get_hades_capsule_act_layout() {
        return R.layout.hades_capsule_act_layout;
    }

    /**
     * TransFloatWin
     */

    public static int get_hades_floatwin_trans() {
        return R.layout.hades_floatwin_trans;
    }

    public static int get_ll_top_area() {
        return R.id.ll_top_area;
    }

    public static int get_ll_hot_area() {
        return R.id.ll_hot_area;
    }

    public static int get_ll_bottom_area() {
        return R.id.ll_bottom_area;
    }

    /**
     * ScreenShotView
     */

    public static int get_hades_screen_shot_layout() {
        return R.layout.screen_shot_popup_layout;
    }

    public static int get_hades_screen_shot_root() {
        return R.id.ss_root;
    }

    public static int get_screen_shot_image() {
        return R.id.screenShotImage;
    }

    public static int get_ss_feedback() {
        return R.id.ss_feedback;
    }

    public static int get_ss_no_disturb() {
        return R.id.ss_no_disturb;
    }

    public static int get_ss_push_setting() {
        return R.id.ss_push_setting;
    }

    public static int get_ss_close() {
        return R.id.ss_close;
    }

    /**
     * BottomDialogFloatWin
     */

    public static int get_hades_bottom_dialog_floatwin() {
        return R.layout.hades_act_wc_layout;
    }

    public static int get_hades_dialog_root_layout() {
        return R.id.dialog_root_layout;
    }

    public static int get_hades_close_view() {
        return R.id.top_close_area;
    }

    public static int get_hades_center_image() {
        return R.id.lock_center_image;
    }

    public static int get_hades_bottom_dialog_view() {
        return R.id.bottom_dialog_view;
    }

    public static int get_dialog_open_anim() {
        return R.anim.dialog_open;
    }

    public static int get_hades_lock_desk_act() {
        return R.layout.hades_lock_desk_act;
    }

    public static int get_hades_lock_root_layout() {
        return R.id.lock_root_layout;
    }

    public static int get_hades_lock_win_background() {
        return R.id.lock_win_background;
    }

    public static int get_hades_lock_win_container() {
        return R.id.lock_win_container;
    }

    public static int get_time_text_view() {
        return R.id.timeTextView;
    }

    public static int get_date_text_view() {
        return R.id.dateTextView;
    }

    public static void addBottomDialogContent(FrameLayout containerView, HashMap<String, Object> dialogData, IFlexViewClickEventListenerAdapter listener) {
        CrashReporter.getInstance().init(containerView.getContext(), new CrashReporterConfig(){});
        QtitansFlexCardView cardView  = new QtitansFlexCardView(containerView.getContext());
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        cardView.setLayoutParams(layoutParams);
        containerView.addView(cardView);
        JSONObject data = new JSONObject(dialogData);
        cardView.showView(data);
        cardView.setClickEventListener(listener::handleClickEvent);
        cardView.setClickEventListener(listener::handleClickEvent);
    }

    public static void addBottomDialogContent(FrameLayout containerView, HashMap<String, Object> dialogData,
                                              IFlexViewClickEventListenerAdapter clickEventListenerAdapter,
                                              IFlexViewShowStatusListenerAdapter showStatusListenerAdapter) {
        CrashReporter.getInstance().init(containerView.getContext(), new CrashReporterConfig(){});
        QtitansFlexCardView cardView  = new QtitansFlexCardView(containerView.getContext());
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        cardView.setLayoutParams(layoutParams);
        containerView.addView(cardView);
        JSONObject data = new JSONObject(dialogData);
        cardView.setClickEventListener(clickEventListenerAdapter::handleClickEvent);
        cardView.setShowStatusListener(new IFlexCardShowStatusListener() {
            @Override
            public void onShow(TemplateData data, boolean viewChanged) {
                showStatusListenerAdapter.onShow();
            }

            @Override
            public void onFailed(TemplateData data) {
                showStatusListenerAdapter.onFailed();
            }
        });


        cardView.showView(data);
    }
    public static void sendEvent(Context context, QQFlexEvent event) {
        if (cardView != null && event != null) {
            EventScope scope;
            switch (event.eventScope) {
                case 0:
                    scope = EventScope.GLOBAL;
                    break;
                case 1:
                    scope = EventScope.PAGE;
                    break;
                case 2:
                    scope = EventScope.MODULE;
                    break;
                default:
                    scope = EventScope.UNDEFINED;
            }
            Event flexEvent = new Event(event.action, scope, context);
            if (event.data != null) {
                flexEvent.setData(event.data);
            }
            cardView.sendEvent(flexEvent);
        }
    }

}
