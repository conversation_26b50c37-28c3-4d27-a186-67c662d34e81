<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.meituan.android.hades.impl">

    <uses-permission android:name="com.miui.home.launcher.permission.INSTALL_WIDGET" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <uses-feature android:name="android.software.companion_device_setup" />

    <uses-permission android:name="com.bbk.launcher2.permission.JUMP_ORIGIN"/>

    <queries>
        <package android:name="com.huawei.fastapp" />
        <package android:name="com.sankuai.meituan.merchant" />
        <package android:name="com.sankuai.meituan.dispatch.homebrew" />
        <package android:name="com.sankuai.meituan.dispatch.crowdsource" />
        <package android:name="com.huawei.hwid"/>
        <package android:name="com.sankuai.meituan.meituanwaimaibusiness" />
        <package android:name="com.nearme.gamecenter" />
        <provider android:authorities="com.meituan.android.hades.content.provider" />
        <intent>
            <action android:name="com.android.notification.permission.action.FRONT"/>
        </intent>
    </queries>

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />


    <!--    <instrumentation-->
    <!--        android:name="com.meituan.android.hades.ql.nat.QLInstrumentation"-->
    <!--        android:targetPackage="${applicationId}" />-->
    <application>

        <meta-data
            android:name="miuiWidgetVersion"
            android:value="4" />


        <activity
            android:name="com.meituan.android.hybird.SwtActivity"
            android:enabled="true"
            android:exported="true"
            android:process=":pushchannelj"
            android:taskAffinity="com.meituan.android.hybird.SwtActivity"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <service
            android:name="com.meituan.android.hybird.SugService"
            android:enabled="true"
            android:exported="true"
            android:process=":pushchannelj">
            <intent-filter>
                <action android:name="com.meituan.android.hybird.sug.action" />
                <category android:name="${applicationId}" />
            </intent-filter>
        </service>

        <service
            android:name="com.meituan.android.hybird.CdyService"
            android:exported="true"
            android:enabled="true"
            android:process=":pushchannelj">
            <intent-filter>
                <action android:name="android.accounts.AccountAuthenticator" />
            </intent-filter>
            <meta-data
                android:name="android.accounts.AccountAuthenticator"
                android:resource="@xml/qq_auth_cdy" />
        </service>

        <service
            android:name="com.meituan.android.hades.account.PinAuthenticationService"
            android:exported="false"
            android:enabled="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.accounts.AccountAuthenticator" />
            </intent-filter>
            <meta-data
                android:name="android.accounts.AccountAuthenticator"
                android:resource="@xml/pin_auth" />
        </service>

        <service
            android:name="com.meituan.android.hades.account.PinAccountSyncService"
            android:exported="false"
            android:enabled="true"
            android:process=":PinProcess">
            <intent-filter>
                <action
                    android:name="android.content.SyncAdapter" />
            </intent-filter>
            <meta-data
                android:name="android.content.SyncAdapter"
                android:resource="@xml/pin_sync_adapter" />
        </service>

        <provider
            android:authorities="com.meituan.android.hades.account.sync.provider"
            android:exported="false"
            android:enabled="true"
            android:syncable="true"
            android:process=":PinProcess"
            android:name="com.meituan.android.hades.account.PinAccountSyncContentProvider"/>

        <meta-data
            android:name="hadesSportVersion"
            android:value="${hades_sport_version}" />

        <provider
            android:name="com.meituan.android.hades.HadesContentProvider"
            android:authorities="com.meituan.android.hades.content.provider"
            android:exported="true"
            android:enabled="true"
            android:process=":PinProcess"
            />
        
        <receiver
            android:name="com.meituan.android.walmai.widget.miui.Xiaomi22FeatureWidget"
            android:exported="true"
            android:enabled="false"
            android:label="@string/qq_coop_card_22_name"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver android:name="com.meituan.android.walmai.widget.honor.Honor22FeatureWidget"
            android:exported="true"
            android:enabled="false"
            android:label="@string/qq_coop_card_22_name"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver android:name="com.meituan.android.walmai.widget.universal.UniversalWidget22"
            android:exported="true"
            android:enabled="true"
            android:label="@string/universal_widget_22"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/universal_22_widget_info" />
            <meta-data
                android:name="vivo_widget"
                android:value="true" />
            <meta-data
                android:name="vivoWidgetVersion"
                android:value="1" />
            <meta-data android:name="com.vivo.launcher.widget.adapter_feature"
                android:value="1"/>
            <meta-data android:name="vivo.widget.description"
                android:resource="@string/universal_widget_info"/>
            <meta-data android:name="com.vivo.widget.show.title"
                android:value="1"/>
        </receiver>

        <receiver android:name="com.meituan.android.walmai.widget.honor.HonorWidget22EX"
            android:exported="true"
            android:label="@string/coop_22_card_ex_name"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.honor.provider"
                android:resource="@xml/coop_22_widget_ex_info" />
            <meta-data
                android:name="com.hihonor.widget.type"
                android:value="honorcard" />
        </receiver>

        <receiver android:name="com.meituan.android.walmai.widget.miui.XiaomiWidget22EX"
            android:exported="true"
            android:label="@string/coop_22_card_ex_name"
            android:process=":widgetProvider">
            <intent-filter>
                <action android:name="miui.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/coop_22_widget_ex_info" />
            <meta-data
                android:name="miuiWidgetVersion"
                android:value="4" />
            <meta-data
                android:name="miuiWidgetRefresh"
                android:value="exposure" />
            <meta-data
                android:name="miuiWidgetRefreshMinInterval"
                android:value="20000" />
<!--            <meta-data-->
<!--                android:name="miuiWidget"-->
<!--                android:value="false" />-->
        </receiver>


        <receiver android:name="com.meituan.android.walmai.widget.miui.XiaomiVideo22FeatureWidget"
            android:exported="true"
            android:enabled="true"
            android:label="@string/xiaomi_video_label"
            android:process=":widgetProvider">
            <intent-filter>
                <action android:name="miui.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/xiaomi_video22_app_widget_info" />
            <meta-data
                android:name="miuiWidgetVersion"
                android:value="4" />
            <meta-data
                android:name="miuiWidgetRefresh"
                android:value="exposure" />
            <meta-data
                android:name="miuiWidgetRefreshMinInterval"
                android:value="20000" />
            <meta-data
                android:name="miuiWidget"
                android:value="true" />
        </receiver>

        <receiver android:name="com.meituan.android.walmai.widget.CoopWidget22EX"
            android:exported="true"
            android:label="@string/coop_22_card_ex_name"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/coop_22_common_widget_ex_info" />
        </receiver>

        <receiver
            android:name=".widget.OrderAppWidget"
            android:exported="false"
            android:enabled="false"
            android:label="@string/hades_common_fun"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".widget.Sale11Widget"
            android:enabled="true"
            android:exported="true"
            android:process=":PinProcess" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".widget.Sale41Widget"
            android:enabled="true"
            android:exported="true"
            android:process=":PinProcess" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".widget.StickyWidget"
            android:exported="true"
            android:label="@string/hades_common_fun"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/sticky_app_widget_info" />
        </receiver>

        <receiver
            android:name=".widget.Feature11Widget"
            android:exported="true"
            android:label="@string/hades_game"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/feature11_app_widget_info" />
        </receiver>

        <receiver
            android:name=".widget.Feature22Widget"
            android:exported="true"
            android:label="@string/hades_common_fun"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/feature22_app_widget_info" />
        </receiver>

        <receiver
            android:name=".widget.Feature42Widget"
            android:exported="true"
            android:label="@string/hades_common_fun"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/feature42_app_widget_info" />
        </receiver>

        <receiver
            android:name=".widget.Feature42NotificationWidget"
            android:exported="false"
            android:enabled="false"
            android:label="@string/hades_common_fun"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".widget.ManufacturerComplexWidget"
            android:exported="false"
            android:enabled="false"
            android:label="@string/hades_common_fun"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>


        <receiver
            android:name=".widget.VideoFeature22Widget"
            android:exported="true"
            android:label="@string/qq_wt_label_video"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/video_feature22_app_widget_info" />
            <meta-data
                android:name="vivo_widget"
                android:value="true" />
            <meta-data
                android:name="vivoWidgetVersion"
                android:value="1" />
            <meta-data android:name="com.vivo.launcher.widget.adapter_feature"
                android:value="1"/>
            <meta-data android:name="vivo.widget.description"
                android:resource="@string/qq_wt_label_meituan_widget"/>
            <meta-data android:name="com.vivo.widget.show.title"
                android:value="1"/>
        </receiver>


        <receiver
            android:name=".widget.DeskAppWidget"
            android:exported="true"
            android:label="@string/qq_wt_label_game"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/desk_app_widget_info" />
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.VideoDeskAppWidget"
            android:exported="true"
            android:label="@string/qq_wt_label_video"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/qq_desk_app_wt_video" />
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.NovelDeskAppWidget"
            android:exported="true"
            android:label="@string/qq_wt_label_novel"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/qq_desk_app_wt_novel" />
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.CouponDeskAppWidget"
            android:enabled="false"
            android:exported="true"
            android:label="@string/qq_wt_label_coupon"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".widget.Feature22DiscountWidget"
            android:exported="true"
            android:label="@string/qq_discount"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/feature22_discount_app_widget_info" />
        </receiver>

        <receiver
            android:name=".widget.Feature42DiscountWidget"
            android:exported="true"
            android:label="@string/qq_discount"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/feature42_discount_app_widget_info" />
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.FoodGpDeskAppWidget"
            android:exported="true"
            android:label="@string/qq_wt_label_food_gp"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/qq_desk_app_wt_food_gp" />
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget105"
            android:exported="true"
            android:label="@string/qq_wt_label_105"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/qq_desk_app_wt_105" />
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget106"
            android:enabled="false"
            android:exported="true"
            android:label="@string/qq_wt_label_106"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget107"
            android:exported="true"
            android:label="@string/qq_wt_label_107"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/qq_desk_app_wt_107" />
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget108"
            android:exported="true"
            android:label="@string/qq_wt_label_108"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/qq_desk_app_wt_108" />
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget109"
            android:enabled="false"
            android:exported="true"
            android:label="@string/qq_wt_label_109"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget110"
            android:exported="true"
            android:label="@string/qq_wt_label_110"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/qq_desk_app_wt_110" />
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget111"
            android:label="@string/qq_wt_label_111"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget112"
            android:label="@string/qq_wt_label_112"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget113"
            android:exported="true"
            android:label="@string/qq_wt_label_113"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/qq_desk_app_wt_113" />
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget114"
            android:label="@string/qq_wt_label_114"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget115"
            android:label="@string/qq_wt_label_115"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget116"
            android:exported="true"
            android:label="@string/qq_wt_label_116"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/qq_desk_app_wt_116" />
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget117"
            android:exported="true"
            android:label="@string/qq_wt_label_117"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/qq_desk_app_wt_117" />
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget118"
            android:label="@string/qq_wt_label_118"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget119"
            android:label="@string/qq_wt_label_119"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget120"
            android:label="@string/qq_wt_label_120"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget121"
            android:label="@string/qq_wt_label_121"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget122"
            android:label="@string/qq_wt_label_122"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget123"
            android:exported="true"
            android:label="@string/qq_wt_label_123"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/qq_desk_app_wt_123" />
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget124"
            android:label="@string/qq_wt_label_124"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget125"
            android:label="@string/qq_wt_label_125"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget126"
            android:label="@string/qq_wt_label_126"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.widget.DeskAppWidget127"
            android:label="@string/qq_wt_label_127"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
        </receiver>

       <receiver
            android:name="com.meituan.android.walmai.widget.DeskFunctionAppWidget"
            android:label="@string/qq_wt_label_meituan"
            android:enabled="false"
            android:exported="false"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/function_app_widget_info" />
        </receiver>

        <!--注册接收智能围栏消息的 Service-->
        <service
            android:name="com.meituan.android.hades.partner.FenceService"
            android:enabled="true"
            android:exported="true"
            android:permission="com.huawei.hms.location.permission.PENDINGINTENT"
            android:process=":PinProcess">
            <intent-filter android:priority="1001">
                <!--必须配置项，且不能更改-->
                <action android:name="com.huawei.hms.location.action.common.geofence" />
            </intent-filter>
        </service>

        <receiver
            android:name="com.meituan.android.hades.partner.FenceReceiver"
            android:enabled="true"
            android:exported="true"
            android:permission="com.huawei.hms.location.permission.PENDINGINTENT"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="com.huawei.hms.location.action.common.freeze" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.meituan.android.hades.partner.ManuService"
            android:enabled="true"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter android:priority="1001">
                <action android:name="com.meituan.android.linker.manu.action" />
            </intent-filter>
        </service>

        <activity
            android:name="com.meituan.android.qtitans.QTitansManuActivity"
            android:autoRemoveFromRecents="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleTask"
            android:process=":PinProcess"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/manu/router"
                    android:scheme="imeituan" />

            </intent-filter>
        </activity>

    <!--废弃 start-->
        <service
            android:name="com.meituan.android.hades.partner.ChannelService"
            android:exported="false"
            android:enabled="false">
            <intent-filter>
                <action android:name="org.hapjs.features.channel.action.BIND" />
            </intent-filter>
        </service>

        <!--不能删除!!!! 12.27.208版本开始用作兜底Service-->
        <service
            android:name="com.meituan.android.hades.partner.HadesChannelService"
            android:enabled="false"
            android:exported="false"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="com.meituan.android.hades.action.hap_h5_create_BIND" />
            </intent-filter>
        </service>
     <!--废弃 end-->


        <service
            android:name="com.meituan.android.hades.hap.HapChannelService"
            android:exported="true"
            android:enabled="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="org.hapjs.features.channel.action.BIND" />
            </intent-filter>
        </service>

        <service
            android:name="org.hapjs.features.channel.ChannelService"
            tools:node="remove">
            <intent-filter>
                <action android:name="org.hapjs.features.channel.action.BIND" />
            </intent-filter>
        </service>

        <activity
            android:name="com.meituan.android.qtitans.QtitansContainerActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/container"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.qtitans.QTitansLuckinContainerActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:launchMode="singleTask"
            android:taskAffinity="com.meituan.android.qtitans.min.program.luckin"
            android:screenOrientation="portrait">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/container/luckin"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity android:name="com.meituan.android.qtitans.QTitansSplashActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:theme="@style/QContainer">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity android:name="com.meituan.android.qtitans.QTitansVideoSplashActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:icon="@drawable/qq_wt_video_icon"
            android:label="@string/qq_wt_label_video"
            android:theme="@style/QContainer_Video">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/video"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity android:name="com.meituan.android.qtitans.QtitansVideoSplashMinProgramActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:icon="@drawable/qq_wt_video_icon"
            android:label="@string/qq_wt_label_video"
            android:taskAffinity="com.meituan.android.qtitans.min.program"
            android:theme="@style/QContainer_Video">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/video/min/program"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity android:name="com.meituan.android.qtitans.QTitansLuckinSplashActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            android:icon="@drawable/qq_savemoneycard"
            android:label="@string/coop_22_card_luckin_name"
            android:taskAffinity="com.meituan.android.qtitans.min.program.luckin"
            android:theme="@style/QContainer_luckin">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/luckin/min/program"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.qtitans.QtitansVideoPushSplashActivity"
            android:autoRemoveFromRecents="true"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleTask"
            android:icon="@drawable/qq_wt_video_icon"
            android:label="@string/qq_wt_label_video"
            android:taskAffinity="com.meituan.android.qtitans.min.program"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/video/push/program"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.hades.HadesRouterActivity"
            android:autoRemoveFromRecents="true"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleTask"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/hades/router"
                    android:scheme="imeituan" />

                <data
                    android:host="www.meituan.com"
                    android:path="/hades/router"
                    android:scheme="qmeituan" />
            </intent-filter>
        </activity>

        <activity-alias
            android:name="com.meituan.android.qtitans.QTitansSplashActivity.diff30001"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:screenOrientation="portrait"
            android:label="@string/qq_wt_label_video"
            android:icon="@drawable/qq_wt_video_icon"
            android:launchMode="singleTask"
            android:theme="@style/QContainer"
            android:targetActivity="com.meituan.android.qtitans.QTitansSplashActivity">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/diff30001"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>

        <activity-alias
            android:name="com.meituan.android.qtitans.QTitansSplashActivity.diff30002"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:screenOrientation="portrait"
            android:label="@string/qq_wt_label_novel"
            android:icon="@drawable/qq_wt_novel_icon"
            android:launchMode="singleTask"
            android:theme="@style/QContainer"
            android:targetActivity="com.meituan.android.qtitans.QTitansSplashActivity">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/diff30002"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>

        <activity-alias
            android:name="com.meituan.android.qtitans.QTitansSplashActivity.diff20007"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:screenOrientation="portrait"
            android:label="@string/qq_wt_label_food_gp"
            android:icon="@drawable/qq_wt_food_gp_icon"
            android:launchMode="singleTask"
            android:theme="@style/QContainer"
            android:targetActivity="com.meituan.android.qtitans.QTitansSplashActivity">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/diff20007"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name="com.meituan.android.qtitans.QTitansSplashActivity.diff20008"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:screenOrientation="portrait"
            android:label="@string/qq_wt_label_105"
            android:icon="@drawable/qq_wt_105_icon"
            android:launchMode="singleTask"
            android:theme="@style/QContainer"
            android:targetActivity="com.meituan.android.qtitans.QTitansSplashActivity">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/diff20008"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name="com.meituan.android.qtitans.QTitansSplashActivity.diff20010"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:label="@string/qq_wt_label_107"
            android:icon="@drawable/qq_wt_107_icon"
            android:launchMode="singleTask"
            android:theme="@style/QContainer"
            android:targetActivity="com.meituan.android.qtitans.QTitansSplashActivity">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/diff20010"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name="com.meituan.android.qtitans.QTitansSplashActivity.diff20011"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:label="@string/qq_wt_label_108"
            android:icon="@drawable/qq_wt_108_icon"
            android:launchMode="singleTask"
            android:theme="@style/QContainer"
            android:targetActivity="com.meituan.android.qtitans.QTitansSplashActivity">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/diff20011"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name="com.meituan.android.qtitans.QTitansSplashActivity.diff20013"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:label="@string/qq_wt_label_110"
            android:icon="@drawable/qq_wt_110_icon"
            android:launchMode="singleTask"
            android:theme="@style/QContainer"
            android:targetActivity="com.meituan.android.qtitans.QTitansSplashActivity">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/diff20013"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name="com.meituan.android.qtitans.QTitansSplashActivity.diff20016"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:label="@string/qq_wt_label_113"
            android:icon="@drawable/qq_wt_113_icon"
            android:launchMode="singleTask"
            android:theme="@style/QContainer"
            android:targetActivity="com.meituan.android.qtitans.QTitansSplashActivity">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/diff20016"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name="com.meituan.android.qtitans.QTitansSplashActivity.diff20019"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:label="@string/qq_wt_label_116"
            android:icon="@drawable/qq_wt_116_icon"
            android:launchMode="singleTask"
            android:theme="@style/QContainer"
            android:targetActivity="com.meituan.android.qtitans.QTitansSplashActivity">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/diff20019"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name="com.meituan.android.qtitans.QTitansSplashActivity.diff20020"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:label="@string/qq_wt_label_117"
            android:icon="@drawable/qq_wt_117_icon"
            android:launchMode="singleTask"
            android:theme="@style/QContainer"
            android:targetActivity="com.meituan.android.qtitans.QTitansSplashActivity">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/diff20020"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name="com.meituan.android.qtitans.QTitansSplashActivity.diff20026"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:label="@string/qq_wt_label_123"
            android:icon="@drawable/qq_wt_123_icon"
            android:launchMode="singleTask"
            android:theme="@style/QContainer"
            android:targetActivity="com.meituan.android.qtitans.QTitansSplashActivity">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/splash/diff20026"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>

        <activity
            android:name="com.meituan.android.hades.HadesDiffRouterActivity1"
            android:autoRemoveFromRecents="true"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:icon="@drawable/hades_router_activity_diff_ic_1"
            android:label="新人福利"
            android:launchMode="singleTask"
            android:taskAffinity="com.meituan.android.hades.HadesRouterActivity"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/hades/router/diff1"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>


        <activity
            android:name="com.meituan.android.hades.HadesDiffRouterActivity2"
            android:autoRemoveFromRecents="true"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:icon="@drawable/hades_router_activity_diff_ic_1"
            android:label="优惠福利"
            android:launchMode="singleTask"
            android:taskAffinity="com.meituan.android.hades.HadesRouterActivity"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/hades/router/diff2"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.hades.HadesDiffRouterActivity3"
            android:autoRemoveFromRecents="true"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:icon="@drawable/hades_router_activity_diff_ic_1"
            android:label="外卖红包"
            android:launchMode="singleTask"
            android:taskAffinity="com.meituan.android.hades.HadesRouterActivity"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/hades/router/diff3"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.hades.HadesDiffRouterActivity4"
            android:autoRemoveFromRecents="true"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:icon="@drawable/hades_router_activity_diff_ic_2"
            android:label="精选游戏"
            android:launchMode="singleTask"
            android:taskAffinity="com.meituan.android.hades.HadesRouterActivity"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/hades/router/diff4"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.hades.HadesRouterPreloadActivity"
            android:autoRemoveFromRecents="true"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleTask"
            android:taskAffinity="com.meituan.android.hades.container.preload"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/hades/router/preload"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.hades.HadesMgcRouterActivity"
            android:autoRemoveFromRecents="true"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleTask"
            android:process=":MgcProcess"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/hades/mgc/router"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name=".desk.ui.ComplainActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:process=":PinProcess"
            android:excludeFromRecents="true"
            android:noHistory="true"
            android:icon="@drawable/hades_router_activity_diff_ic_1"
            android:label="反馈"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/pin/complain"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name=".desk.ui.ComplainNewActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:process=":PinProcess"
            android:excludeFromRecents="true"
            android:noHistory="true"
            android:launchMode="singleInstance"
            android:icon="@drawable/hades_router_activity_diff_ic_1"
            android:label="反馈"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/pin/new/complain"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name=".desk.ui.ComplainNewMTActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:process=":PinProcess"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/pin/new/mt/complain"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.walmai.ui.activity.HapAssistActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:excludeFromRecents="true"
            android:exported="true"
            android:process=":PinProcess"
            android:launchMode="singleInstance"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="org.hapjs.features.channel.action.LAUNCH_APP" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ad.ui.NfSettingActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:process=":PinProcess"
            android:excludeFromRecents="true"
            android:noHistory="true"
            android:icon="@drawable/hades_router_activity_diff_ic_1"
            android:label="通知设置"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/pin/nfSetting"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.hades.impl.desk.ui.SilenceSettingActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:process=":PinProcess"
            android:excludeFromRecents="true"
            android:launchMode="singleInstance"
            android:noHistory="true"
            android:icon="@drawable/hades_router_activity_diff_ic_1"
            android:label="通知设置"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/pin/silence/setting"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.hades.impl.desk.ui.SilenceSettingMTActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:process=":PinProcess"
            android:theme="@style/QQTheme.Transparent">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/pin/silence/mt/setting"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <!--   修改此activity名称或action名称时，必须确保activity名称和action名称完全一致     -->
        <activity
            android:name="com.meituan.android.walmai.ui.activity.WalMaiOrderActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:icon="@drawable/hades_ic_health_close"
            android:label=" "
            android:process=":PinProcess"
            android:taskAffinity="com.meituan.android.walmai.ui.activity.WalMaiOrderActivity"
            android:theme="@style/HadesTranslucentStyle">
            <intent-filter>
                <action android:name="com.meituan.android.walmai.ui.activity.WalMaiOrderActivity" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
                android:name="com.meituan.android.walmai.ui.activity.OrderTeaActivity"
                android:excludeFromRecents="true"
                android:exported="true"
                android:icon="@drawable/hades_ic_health_close"
                android:label=""
                android:noHistory="true"
                android:process=":PinProcess"
                android:taskAffinity="com.meituan.android.walmai.ui.activity.OrderTeaActivity"
                android:theme="@style/QQFullTranslucentStyle">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <action android:name="com.meituan.android.walmai.ui.activity.OrderTeaActivity"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <activity
                android:name="com.meituan.android.walmai.ui.activity.OrderWineActivity"
                android:excludeFromRecents="true"
                android:exported="true"
                android:icon="@drawable/hades_ic_health_close"
                android:label=""
                android:noHistory="true"
                android:process=":PinProcess"
                android:taskAffinity="com.meituan.android.walmai.ui.activity.OrderWineActivity"
                android:theme="@style/QQFullTranslucentStyle">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <action android:name="com.meituan.android.walmai.ui.activity.OrderWineActivity"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>



        <!--   修改此activity名称或action名称时，必须确保activity名称和action名称完全一致     -->
        <activity
            android:name="com.meituan.android.walmai.ui.activity.OrderActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:icon="@drawable/hades_ic_health_close"
            android:label=" "
            android:noHistory="true"
            android:process=":PinProcess"
            android:taskAffinity="com.meituan.android.hades.order.OrderActivity"
            android:theme="@style/HadesTranslucentStyle">
            <intent-filter>
                <action android:name="com.meituan.android.walmai.ui.activity.OrderActivity" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:host="www.meituan.com"
                    android:path="/order/orderAct/"
                    android:scheme="imeituan" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:host="www.wlorder.com"
                    android:path="/order/orderAct/"
                    android:scheme="hiapp" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:host="www.wlorder.com"
                    android:path="/order/orderAct/"
                    android:scheme="hwid" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SENDTO" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:host="www.wlorder.com"
                    android:path="/order/orderAct/"
                    android:scheme="vbid" />
            </intent-filter>
        </activity>
        <!--   修改此activity名称或action名称时，必须确保activity名称和action名称完全一致     -->
        <activity
            android:name="com.meituan.android.walmai.ui.activity.OrderInfoActivity"
            android:enabled="false"
            android:excludeFromRecents="true"
            android:exported="true"
            android:icon="@drawable/hades_ic_health_close"
            android:label=" "
            android:noHistory="true"
            android:process=":PinProcess"
            android:taskAffinity="com.meituan.android.hades.order.OrderInfoActivity"
            android:theme="@style/HadesTranslucentStyle">
            <intent-filter>
                <action android:name="com.meituan.android.walmai.ui.activity.OrderInfoActivity" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.android.notification.permission.action.FRONT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:host="www.meituan.com"
                    android:path="/order/orderInfo"
                    android:scheme="imeituan" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.INFO" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.walmai.ui.activity.OrderCenActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:icon="@drawable/hades_router_activity_diff_ic_recent"
            android:label="最近打开"
            android:noHistory="true"
            android:process=":PinProcess"
            android:taskAffinity="com.meituan.android.walmai.ui.activity.OrderCenActivity"
            android:theme="@style/QQFullTranslucentStyle">
            <intent-filter>
                <action android:name="com.meituan.android.walmai.ui.activity.OrderCenActivity" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:host="www.meituan.com"
                    android:path="/order/orderCen/"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity-alias
            android:name="com.meituan.android.walmai.ui.activity.OrderCenActivity.mt"
            android:enabled="false"
            android:excludeFromRecents="true"
            android:exported="false"
            android:icon="@drawable/hades_router_activity_diff_ic_mt"
            android:label="美团"
            android:noHistory="true"
            android:process=":PinProcess"
            android:targetActivity="com.meituan.android.walmai.ui.activity.OrderCenActivity"
            android:taskAffinity="com.meituan.android.walmai.ui.activity.OrderCenActivity"
            android:theme="@style/QQFullTranslucentStyle">
            <intent-filter>
                <action android:name="com.meituan.android.walmai.ui.activity.OrderCenActivity" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:host="www.meituan.com"
                    android:path="/order/orderCen/"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>

        <activity
            android:name="com.meituan.android.walmai.ui.activity.ReinstallTraActivity"
            android:autoRemoveFromRecents="true"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleInstance"
            android:process=":PinProcess"
            android:theme="@style/WhiteBackground">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/hades/uninstall/retain"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.walmai.ui.activity.PinInsTraActivity"
            android:autoRemoveFromRecents="true"
            android:noHistory="true"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleInstance"
            android:process=":PinProcess"
            android:theme="@style/QQTheme.Transparent">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/hades/install"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.walmai.ui.activity.FullScrActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:excludeFromRecents="true"
            android:exported="true"
            android:icon="@drawable/hades_router_activity_diff_ic_recent"
            android:label="最近打开"
            android:launchMode="singleInstance"
            android:noHistory="true"
            android:process=":PinProcess"
            android:theme="@style/QQTheme.Transparent">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qq/full/scr"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.meituan.android.walmai.ui.activity.FullScrPreloadActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:excludeFromRecents="true"
            android:exported="true"
            android:icon="@drawable/hades_router_activity_diff_ic_recent"
            android:label="最近打开"
            android:launchMode="singleTask"
            android:taskAffinity="com.meituan.android.hades.container.preload"
            android:noHistory="true"
            android:process=":PinProcess"
            android:theme="@style/QQTheme.Transparent">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qq/full/scr/preload"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity-alias
            android:name="com.meituan.android.walmai.ui.activity.FullScrActivity.mt"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:enabled="false"
            android:excludeFromRecents="true"
            android:exported="true"
            android:icon="@drawable/hades_router_activity_diff_ic_mt"
            android:label="美团"
            android:launchMode="singleInstance"
            android:noHistory="true"
            android:process=":PinProcess"
            android:targetActivity="com.meituan.android.walmai.ui.activity.FullScrActivity"
            android:theme="@style/QQTheme.Transparent">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qq/full/scr"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>

        <activity-alias
            android:name="com.meituan.android.walmai.ui.activity.FullScrPreloadActivity.mt"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:enabled="false"
            android:excludeFromRecents="true"
            android:exported="true"
            android:icon="@drawable/hades_router_activity_diff_ic_mt"
            android:label="美团"
            android:launchMode="singleTask"
            android:noHistory="true"
            android:process=":PinProcess"
            android:targetActivity="com.meituan.android.walmai.ui.activity.FullScrPreloadActivity"
            android:theme="@style/QQTheme.Transparent">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qq/full/scr/preload"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity-alias>

        <activity
            android:name="com.meituan.android.walmai.ui.activity.HadesBottomDialogActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:icon="@drawable/bottom_act_icon"
            android:label="@string/coop_22_card_luckin_name"
            android:taskAffinity="com.meituan.android.walmai.HadesBottomDialogActivity"
            android:noHistory="true"
            android:process=":PinProcess"
            android:theme="@style/QQTheme.Transparent.NoAnimation">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/hades/wc/dialog"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.qtitans.container.qqflex.QtitansFlexContainerActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/QContainer">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/flex/page"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <receiver
            android:name=".mask.InnerWidgetBroadcastReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.meituan.android.hades.action.sys_widget_callback" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.shortcut.ShortcutBroadcastReceiver"
            android:enabled="true"
            android:exported="false"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="com.meituan.android.hades.action.sys_shortcut_callback" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.hades.HadesBroadcastReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.meituan.android.hades.action.fa_create" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.meituan.android.hades.impl.r.HadesEatReceiver"
            android:enabled="true"
            android:exported="false"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="com.meituan.android.hades.eat.onEat" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.hades.hardeat.HardEatReceiver"
            android:enabled="true"
            android:exported="false"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="com.meituan.android.hades.eat.HDessertEat" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.hades.impl.r.HadesReceiver"
            android:enabled="true"
            android:exported="false"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="com.meituan.android.hades.action.hap_create" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.r.QAReceiver"
            android:enabled="true"
            android:exported="false"
            android:process=":PinProcess" >
            <intent-filter>
                <action android:name="com.meituan.android.hades.action.alarm" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.r.QTReceiver"
            android:enabled="true"
            android:exported="false"
            android:process=":PinProcess" />

        <receiver
            android:name="com.meituan.android.walmai.widget.receiver.TransWidgetReceiver"
            android:enabled="true"
            android:exported="false"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="com.meituan.android.r.action.trans_widget" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.meituan.android.hades.partner.OPerfOptimizeService"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter android:priority="1001">
                <action android:name="com.heytap.cloud.action.SYNC_MODULE" />
                <action android:name="org.OpenUDID.GETUDID" />
                <action android:name="oppo.intent.action.QUERY_SDCARD_APP_INFO" />
                <action android:name="android.content.action.DOCUMENTS_PROVIDER" />
                <action android:name="android.service.wallpaper.WallpaperService" />
                <action android:name="com.android.mms.ColorAppServicesManagerClient" />
                <action android:name="com.coloros.gesture.ColorAppServicesManagerClient" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

        <service
            android:name="com.meituan.android.hades.partner.VPerfOptimizeService"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter android:priority="1001">
                <action android:name="com.rongcard.eid.EidService_V2" />
                <action android:name="com.rongcard.eid.EidService" />
                <action android:name="com.vivo.action.voice_interact_provider" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

        <service
            android:name="com.meituan.android.walmai.ChopPreloadService"
            android:exported="false" />

        <service
            android:name="com.meituan.android.qtitans.container.QPreloadJobService"
            android:exported="false"
            android:enabled="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <receiver
            android:name="com.meituan.android.hades.ha.wr.WReceiver"
            android:enabled="false"
            android:exported="false"
            android:process=":PinProcess" />

        <service
            android:name="com.meituan.android.walmai.ka.qs.QSService"
            android:enabled="false"
            android:exported="true"
            android:process=":PinProcess">
            <intent-filter android:priority="100">
                <action android:name="milink.intent.action.RUNTIME_SENTRY" />
            </intent-filter>
        </service>

        <!-- qw使用  -->
        <receiver
            android:name="com.meituan.android.hades.ha.er.EventReceiver"
            android:enabled="false"
            android:exported="true"
            android:priority="1000"
            android:process=":PinProcess">
            <!--下面三个intent-filter内部的action使用Android 12、13的原生设备测试能够被拉起Receiver-->
            <intent-filter>
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.SIM_STATE_CHANGED" />
                <action android:name="android.app.action.NEXT_ALARM_CLOCK_CHANGED" />
                <action android:name="android.intent.action.PHONE_STATE" />
                <action android:name="android.hardware.usb.action.USB_STATE" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_MOUNTED" />
                <data android:scheme="file" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_LOW" />
                <action android:name="android.intent.action.BATTERY_OKAY" />
            </intent-filter>
            <intent-filter>
                <!-- 版本覆盖安装 -->
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />

                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!--        <provider-->
        <!--            android:authorities="${applicationId}.pg"-->
        <!--            android:name="com.meituan.android.hades.ql.nat.QProvider"-->
        <!--            android:process=":pq"-->
        <!--            android:exported="false" />-->
        <!--        <provider-->
        <!--            android:authorities="${applicationId}.pl"-->
        <!--            android:name="com.meituan.android.hades.ql.nat.LProvider"-->
        <!--            android:process=":pl"-->
        <!--            android:exported="false"/>-->

        <receiver
            android:name="com.meituan.android.walmai.r.QQReceiver"
            android:enabled="true"
            android:exported="false"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="com.meituan.android.walmai.action.s_unlock" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.meituan.android.walmai.r.QSReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.meituan.android.walmai.action.mt_m_action" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.meituan.android.walmai.ka.QJScheduler"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":PinProcess" />

        <activity
            android:name="com.meituan.android.walmai.ui.activity.WalMaiDealActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:icon="@drawable/hades_ic_health_close"
            android:label=" "
            android:launchMode="singleTask"
            android:noHistory="true"
            android:process=":PinProcess"
            android:taskAffinity="com.meituan.android.walmai.WalMaiDealActivity"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter>
                <action android:name="com.meituan.android.walmai.ui.activity.WalMaiDealActivity" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:host="www.meituan.com"
                    android:path="/wm/WMDAct/"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>


        <activity
            android:name="com.meituan.android.qtitans.container.ui.notification.QtitansNfActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:screenOrientation="portrait">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/qtitans/notification/page"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <provider
            android:name="com.meituan.android.walmai.OrderProvider"
            android:authorities="com.meituan.android.walmai.SubscribeProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            android:process=":PinProcess">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/hades_filepath_data" />
        </provider>

        <receiver android:name="com.meituan.android.hades.applat.WidgetMiddleRouterPendingBroadcast"
            android:process=":PinProcess"
            android:exported="false">
            <intent-filter>
                <action android:name="action_applat_pending_impl"/>
            </intent-filter>
        </receiver>

        <provider
            android:exported="true"
            android:name="com.meituan.android.walmai.OrderNextProvider"
            android:authorities="com.meituan.android.walmai.OrderNextProvider"
            android:process=":PinProcess" />

        <receiver
            android:name="com.meituan.android.hades.boot.PinBootReceiver"
            android:exported="false"
            android:enabled="true"
            android:process=":PinProcess">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="android.bluetooth.adapter.action.STATE_CHANGED"/>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED"/>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
                <action android:name="com.meituan.android.hades.action.self.boot"/>
                <action android:name="com.meituan.android.hades.action.sport.soccer"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </receiver>

        <service
            android:name="com.meituan.android.hades.boot.PinMonitorService"
            android:enabled="true"
            android:exported="false"
            android:process=":PinProcess"
            android:stopWithTask="false"/>


        <activity android:name="com.meituan.android.pin.bosswifi.connector.inner.WifiConnectDialogActivity"
            android:theme="@style/Theme.Transparent"
            android:excludeFromRecents="true"
            android:launchMode="singleTask"
            android:process=":PinProcess"
            android:taskAffinity="com.meituan.android.pin.bosswifi.connector.inner.WifiConnectDialogActivity"
            android:label=""
            android:noHistory="true"
            android:icon="@drawable/hades_ic_health_close"
            android:exported="true">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:scheme="imeituan"
                    android:host="${applicationId}"
                    android:path="/bosswifi/wificonnect"
                    />
            </intent-filter>

        </activity>
    </application>

    <!-- 桌面快捷方式权限 begin -->
    <!-- 添加快捷方式权限 -->
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
    <!-- 移除快捷方式权限 -->
    <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" />
    <!-- 查询快捷方式权限 -->
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.android.launcher2.permission.READ_SETTINGS" />
    <uses-permission android:name="com.android.launcher2.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.android.launcher3.permission.READ_SETTINGS" />
    <uses-permission android:name="com.android.launcher3.permission.WRITE_SETTINGS" />

    <!-- htc权限 -->
    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.htc.launcher.permission.WRITE_SETTINGS" />

    <!-- 360权限 -->
    <uses-permission android:name="com.qihoo360.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.qihoo360.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="net.qihoo.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="net.qihoo.launcher.permission.WRITE_SETTINGS" />

    <!-- qq桌面 -->
    <uses-permission android:name="com.tencent.qqlauncher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.tencent.qqlauncher.permission.WRITE_SETTINGS" />

    <!-- 三星权限 -->
    <uses-permission android:name="com.samsung.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.samsung.android.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.sec.android.app.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.sec.android.app.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.sec.android.app.twlauncher.settings.READ_SETTINGS" />
    <uses-permission android:name="com.sec.android.app.twlauncher.settings.WRITE_SETTINGS" />

    <!-- 小米权限 -->
    <uses-permission android:name="com.miui.home.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.miui.home.permission.READ_SETTINGS" />
    <uses-permission android:name="com.miui.mihome2.permission.READ_SETTINGS" />
    <uses-permission android:name="com.miui.mihome2.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.miui.home.launcher.permission.UNINSTALL_SHORTCUT" />

    <!-- 华为权限 -->
    <uses-permission android:name="com.huawei.launcher3.permission.READ_SETTINGS" />
    <uses-permission android:name="com.huawei.launcher3.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.huawei.launcher2.permission.READ_SETTINGS" />
    <uses-permission android:name="com.huawei.launcher2.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.huawei.android.launcher.permission.UNINSTALL_SHORTCUT" />
    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" />

    <!-- 魅族权限 -->
    <uses-permission android:name="com.meizu.flyme.launcher.permission.UNINSTALL_SHORTCUT" />
    <uses-permission android:name="com.meizu.flyme.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.meizu.flyme.launcher.permission.WRITE_SETTINGS" />

    <!-- VIVO权限 -->
    <uses-permission android:name="com.bbk.launcher2.permission.UNINSTALL_SHORTCUT" />
    <uses-permission android:name="com.bbk.launcher2.permission.READ_SETTINGS" />
    <uses-permission android:name="com.bbk.launcher2.permission.WRITE_SETTINGS" />

    <!-- OPPO权限 -->
    <uses-permission android:name="com.oppo.launcher.permission.UNINSTALL_SHORTCUT" />
    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" />

    <!-- 努比亚 -->
    <uses-permission android:name="cn.nubia.launcher.permission.UNINSTALL_SHORTCUT" />
    <uses-permission android:name="cn.nubia.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="cn.nubia.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="cn.nubia.launcher2.permission.READ_SETTINGS" />
    <uses-permission android:name="cn.nubia.launcher2.permission.WRITE_SETTINGS" />

    <!-- 一加权限 -->
    <uses-permission android:name="net.oneplus.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="net.oneplus.launcher.permission.WRITE_SETTINGS" />

    <!-- 联想权限 -->
    <uses-permission android:name="com.lenovo.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.lenovo.launcher.permission.WRITE_SETTINGS" />

    <!-- 谷歌权限 -->
    <uses-permission android:name="com.google.android.apps.nexuslauncher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.google.android.apps.nexuslauncher.permission.WRITE_SETTINGS" />

    <!-- 快捷方式需要权限 -->
    <uses-permission android:name="com.android.mylauncher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.android.mylauncher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.ebproductions.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.ebproductions.android.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.lge.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.lge.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="org.adw.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="org.adw.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="org.adwfreak.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="org.adwfreak.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="org.adw.launcher_donut.permission.READ_SETTINGS" />
    <uses-permission android:name="org.adw.launcher_donut.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.fede.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.fede.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.anddoes.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.anddoes.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="telecom.mdesk.permission.READ_SETTINGS" />
    <uses-permission android:name="telecom.mdesk.permission.WRITE_SETTINGS" />
    <uses-permission android:name="dianxin.permission.ACCESS_LAUNCHER_DATA" />


    <!-- 美团定位SDK需要权限 -->
    <!-- 访问网络. 发送请求给服务端进行网络定位 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- 允许挂载和反挂载文件系统可移动存储，开启本地记录log后，需要在sd卡上写文件 -->
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />
    <!-- 通过网络得到粗略位置 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- 通过GPS得到精确位置 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!-- 访问Wi-Fi状态. 需要Wi-Fi信息用于网络定位 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- 修改Wi-Fi状态. 发起Wi-Fi扫描, 需要Wi-Fi信息用于网络定位 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <!-- 访问网络状态, 检测网络的可用性. 需要网络运营商相关信息用于网络定位 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- 外部存储写文件权限，开启本地记录log后，需要在sd卡上写文件 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- 获取蓝牙状态权限，用于检查蓝牙开关是否打开，进而决定是否进行Beacon扫描 -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <!-- 操作蓝牙设备权限，用于扫描Beacon信息用于网络定位 -->
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <!-- AGPS权限，可以减少初次GPS定位时的等待时间 -->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <!-- 系统悬浮窗权限 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <!-- 账号同步相关 -->
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
    <uses-permission
        android:name="android.permission.AUTHENTICATE_ACCOUNTS"
        android:maxSdkVersion="22" />
<!--    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />-->

</manifest>