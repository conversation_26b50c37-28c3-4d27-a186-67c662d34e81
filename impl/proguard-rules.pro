
-ignorewarnings

-keep class android.support.annotation.Keep

#保证 com.meituan.android.hades.dyadater包及其子包下所有类的公共字段和方法不被混淆
-keep class com.meituan.android.hades.dyadater.** {
    public  <fields>;
    public  <methods>;
}

-keep class com.dianping.sdk.pike.PikeClient {
     *;
}

-keep class com.dianping.sdk.pike.PikeBaseClient {
     *;
}

-keep class com.dianping.sdk.pike.service.RawClient {
     *;
}

-keep class com.dianping.sdk.pike.service.PikeTunnelService {
     *;
}
-keep class com.meituan.android.hades.report.HadesBizEvent {*;}
-keep class com.meituan.android.hades.report.HadesBizEvent$Builder {*;}
#临时fix枚举灰度包NoSuchFieldException
-keepclassmembernames enum com.meituan.android.dynamiclayout.** {*;}
-keepclassmembernames enum com.sankuai.litho.** {*;}
-keepclassmembernames enum com.meituan.android.qtitans.** {*;}
-keepclassmembernames enum com.facebook.yoga.**{*;}
-keep class com.sankuai.meituan.aop.** {
    public  <fields>;
    public  <methods>;
}