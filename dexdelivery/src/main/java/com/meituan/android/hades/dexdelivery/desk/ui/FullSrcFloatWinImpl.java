package com.meituan.android.hades.dexdelivery.desk.ui;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.content.ContextCompat;
import android.text.TextUtils;
import android.util.Pair;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import android.view.Window;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.LottieDrawable;
import com.meituan.android.hades.broadcast.BroadcastReceiverX;
import com.meituan.android.hades.dexdelivery.delivery.helper.DeliveryResourceHelper;
import com.meituan.android.hades.dexdelivery.delivery.helper.FeedbackHelper;
import com.meituan.android.hades.dexdelivery.delivery.reporter.DexDeliveryReporter;
import com.meituan.android.hades.dexdelivery.delivery.reporter.ReportParamsKey;
import com.meituan.android.hades.dexdelivery.desk.ui.view.CircularView;
import com.meituan.android.hades.dexdelivery.desk.ui.view.ScreenShotView;
import com.meituan.android.hades.dexdelivery.desk.ui.view.WalMaiFullScrDialogView;
import com.meituan.android.hades.dexdelivery.model.DeliveryDeskMaterial;
import com.meituan.android.hades.dexdelivery.model.DeliveryResourceDataCache;
import com.meituan.android.hades.dexdelivery.model.DeliveryResourceInfo;
import com.meituan.android.hades.dexdelivery.model.DeskArea;
import com.meituan.android.hades.dexdelivery.utils.DexDeliveryUtils;
import com.meituan.android.hades.dexdelivery.utils.ImageUtils;
import com.meituan.android.hades.dexdelivery.utils.LogUtils;
import com.meituan.android.hades.dyadater.BabelHelperAdapter;
import com.meituan.android.hades.dyadater.PicassoAdapter;
import com.meituan.android.hades.dyadater.StorageHelperAdapter;
import com.meituan.android.hades.dyadater.ad.HadesNotificationKey;
import com.meituan.android.hades.dyadater.desk.CloseButtonPosEnum;
import com.meituan.android.hades.dyadater.desk.DeliveryDataManager;
import com.meituan.android.hades.dyadater.desk.DeskResourceData;
import com.meituan.android.hades.dyadater.desk.DeskSourceEnum;
import com.meituan.android.hades.dyadater.desk.FullScrActivityStatusCallback;
import com.meituan.android.hades.dyadater.desk.feedback.FeedbackExtensions;
import com.meituan.android.hades.dyadater.desk.ui.DeskFloatWinViewAdapter;
import com.meituan.android.hades.dyadater.monitor.LogicCpuTimeCollectorAdapter;
import com.meituan.android.hades.dyadater.utils.HadesUtilsAdapter;
import com.meituan.android.hades.dyadater.utils.LottieUtilsAdapter;
import com.meituan.android.hades.dyadater.utils.QPreloadJobServiceAdapter;
import com.meituan.android.hades.dyadater.utils.QTitansPreloadActivityAdapter;
import com.meituan.android.hades.dyadater.utils.UiUtilsAdapter;
import com.meituan.android.hades.impl.desk.DeskCloseTypeEnum;
import com.meituan.android.hades.impl.desk.DeskTypeEnum;
import com.meituan.android.hades.impl.desk.ScreenShotManager;
import com.meituan.android.hades.impl.model.FullActProductInfo;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;


/**
 *
 *    "templatePage": {
 *             //模版配置，可为空
 *             "templateName": "",
 *             "templateUrl": "",
 *             "pageMap": {
 *               "unlockFullSplice": {
 *                 "foregroundTarget": "https://example.com/foreground",
 *                 "foregroundImageId": "fg_image_123456",
 *                 "foregroundImageUrl": "",
 *                 //前景图片位置,默认居中，center/bottom
 *                 "forgroundPosition": "bottom",
 *                 "backgroundTarget": "https://example.com/background",
 *                 "backgroundImageId": "bg_image_789012",
 *                 "backgroundImageUrl": "",
 *                 //特效效果，flash:闪光特效
 *                 "specialEffects": "flash",
 *                 //随机关闭按钮位置
 *                 "closeButtonPos": 2
 *               }
 *             },
 *             //资源位类型，全屏拼接
 *             "winType": "unlockFullSplice"
 *           }
 *
 */
public class FullSrcFloatWinImpl extends BaseAbsFullSrcFloatWin implements FullScrActivityStatusCallback {
    private static final String TAG = "Dex_FullSrcFloatWin";

    private static final String EXTRA_KEY_ENUM_CODE = "hades_router_enum_code";
    private static final String EXTRA_KEY_RESOURCE_DATA = "hades_router_resource";
    private static final String EXTRA_KEY_REGISTER_SCREEN_SHOT = "hades_router_register_screen_shot_after_focus";

    private DeskResourceData mDeskResourceData = new DeskResourceData();
    private DeskSourceEnum mDeskSourceEnum = DeskSourceEnum.OTHER;

    private final Handler mHandler = new Handler(Looper.getMainLooper());

    private long mExposureTime = 0L;
    private long mExposureUpTime = 0L;
    private boolean mIsLockedWhenShow = true;

    // 是否上报关闭事件
    private boolean mReportClose = false;
    // 是否发生点击行为
    private boolean mClickAction = false;
    // 行为结束时间
    private long mActionEndTime = 0L;
    private long mActionEndUpTime = 0L;
    private boolean showProductDialog;
    // 是否有过焦点
    private boolean mHasFocus = false;

    private WalMaiFullScrDialogView dialogView;
    private RelativeLayout rootView;
    private ImageView deskCenterImageView;
    private ImageView closeImageView;
    private FrameLayout fullContainerView;
    private CircularView feedbackView;
    private CircularView pushSettingView;
    private LottieAnimationView lottieAnimationView;

    private DeskArea<DeliveryDeskMaterial> mDeskInfo;
    private DeliveryResourceInfo mDeliveryResourceInfo;

    boolean registerScreenShotAfterFocus = true;
    private boolean isPreloadActivityStart = false;
    private boolean isWarmup = false;
    private Runnable autoCloseRunnable;
    private BroadcastReceiver unlockReceive;
    private WeakReference<Activity> activityRef = new WeakReference<>(null);

    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        final Context context = HadesUtilsAdapter.getContext();
        activityRef = new WeakReference<>(activity);
        HadesUtilsAdapter.runOnWorkThread(new Runnable() {
            @Override
            public void run() {
                StorageHelperAdapter.clearDspRetainData(context);
            }
        });
        if (!HadesUtilsAdapter.isOrientationPortrait(context)) {
            removeView(DeskCloseTypeEnum.LANDSCAPE.getMessage());
            return;
        }
        if (HadesUtilsAdapter.isScreenOn(context) && !HadesUtilsAdapter.isLocked()) {
            removeView(DeskCloseTypeEnum.SCREEN_SCREEN_ON.getMessage());
            return;
        }

        activity.setContentView(DeskFloatWinViewAdapter.get_hades_full_floatwin());
        mReportClose = false;
        mClickAction = false;
        mExposureTime = System.currentTimeMillis();
        mExposureUpTime = SystemClock.uptimeMillis();

        Intent intent = activity.getIntent();
        if (intent == null) {
            removeView(DeskCloseTypeEnum.PARAM_NULL.getMessage());
            return;
        }

        String resourceDataStr = intent.getStringExtra(EXTRA_KEY_RESOURCE_DATA);
        int sourceEnumCode = intent.getIntExtra(EXTRA_KEY_ENUM_CODE, -1);
        registerScreenShotAfterFocus = intent.getBooleanExtra(EXTRA_KEY_REGISTER_SCREEN_SHOT, true);
        mDeskResourceData = HadesUtilsAdapter.fromJson(resourceDataStr, DeskResourceData.class);
        mDeskSourceEnum = DeskSourceEnum.getBycode(sourceEnumCode);
        if (mDeskResourceData == null || mDeskSourceEnum == null) {
            removeView(DeskCloseTypeEnum.PARAM_NULL.getMessage());
            return;
        }
        initDeliveryResourceData();
        initStatusBarAndNavigationBar(activity);

        if (mDeskResourceData.fullActProductInfo != null
                && mDeskResourceData.fullActProductInfo.actionType == FullActProductInfo.ACTION_TYPE_PAY) {
            long countdownSecond = mDeskResourceData.fullActProductInfo.orderEndTime - System.currentTimeMillis();
            if (countdownSecond / 1000 > 0) {
                mDeskResourceData.fullActProductInfo.countdownSecond = (int) (countdownSecond / 1000);
            } else {
                removeView(DeskCloseTypeEnum.ORDER_INVALID.getMessage());
                return;
            }
        }
        DeliveryDataManager.sViewAttached = true;
        initView(activity);
        if (!HadesUtilsAdapter.isLocked()) {
            mIsLockedWhenShow = false;
        }

        if (!registerScreenShotAfterFocus) {
            // 截屏监听
            registerScreenShotListener(ScreenShotManager.ScreenShotEnum.DESK,
                    mDeskResourceData, mDeskSourceEnum, FeedbackExtensions.SCENE_DESK_PUSH);
        }
        DeliveryDataManager.clearImageResource();
        registerUnlockReceiver();
    }

    private void initStatusBarAndNavigationBar(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            try {
                Window window = activity.getWindow();
                if (DeliveryResourceHelper.isHideNavi(mDeskInfo)) {
                    window.getDecorView().setSystemUiVisibility(
                            View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                                    | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
                    window.setStatusBarColor(ContextCompat.getColor(activity, android.R.color.transparent));
                    window.setNavigationBarColor(android.graphics.Color.TRANSPARENT);
                } else {
                    window.getDecorView().setSystemUiVisibility(
                            View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
                    window.setStatusBarColor(ContextCompat.getColor(activity, android.R.color.transparent));
                }
            } catch (Throwable t) {
            }
        }
    }

    private void registerUnlockReceiver() {
        if (unlockReceive == null) {
            unlockReceive = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (intent == null) {
                        return;
                    }
                    if (Intent.ACTION_USER_PRESENT.equals(intent.getAction())) {
                        log( "Device unlocked");
                        onUserWatch("unlock");
                    }
                }
            };
        }
        BroadcastReceiverX.register(HadesUtilsAdapter.getContext(), unlockReceive, Intent.ACTION_USER_PRESENT);
    }

    private void unregisterUnlockReceiver() {
        if (unlockReceive != null) {
            BroadcastReceiverX.unRegister(HadesUtilsAdapter.getContext(), unlockReceive);
            unlockReceive = null;
        }
    }

    @Override
    public void onActivityStarted() {
    }

    @Override
    public void onActivityResumed() {
        if (getActiveActivity() == null) {
            return;
        }
        final Context context = HadesUtilsAdapter.getContext();
        HadesUtilsAdapter.runOnMainThreadWithDelay(new Runnable() {
            @Override
            public void run() {
                if (HadesUtilsAdapter.isScreenOn(context) && !HadesUtilsAdapter.isLocked()) {
                    log("onActivityResumed onUserWatch");
                    onUserWatch("onActivityResumed");
                }
            }
        }, 1000);

        if (mDeskResourceData != null) {
            if (QTitansPreloadActivityAdapter.canIUse(context, mDeskResourceData.target)
                    && !isPreloadActivityStart
                    && !showProductDialog) { //非挽留
                String pushType = mDeskResourceData.deskType == null ? "" : mDeskResourceData.deskType.name();
                QTitansPreloadActivityAdapter.transParentStartIfCan(context, mDeskResourceData.target, mDeskSourceEnum.name(), pushType);
                isPreloadActivityStart = true;
                return;
            }

            if (HadesUtilsAdapter.enablePreloadWarmUpMainProcess()
                    && !isWarmup
                    && !isPreloadActivityStart) {
                QPreloadJobServiceAdapter.warmupMainProcess(context, mDeskResourceData.target);
                isWarmup = true;
            }
        }
    }

    @Override
    public void onActivityPaused() {
    }

    @Override
    public void onActivityStopped() {
        log("onStop");
        Activity activity = getActiveActivity();
        if (activity == null) {
            return;
        }
        try {
            if (mHasFocus) {
                removeView(DeskCloseTypeEnum.USER_CLOSE.getMessage());
            }
        } catch (Throwable t) {
            activity.finish();
        }
    }

    @Override
    public void onActivityDestroyed() {
        log("onDestroy");
        if (getActiveActivity() == null) {
            return;
        }
        unregisterUnlockReceiver();
        removeView(DeskCloseTypeEnum.USER_CLOSE.getMessage());
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        log("onWindowFocusChanged hasFocus = " + hasFocus);
        if (getActiveActivity() == null) {
            return;
        }
        if (hasFocus) {
            log("onWindowFocusChanged triggerAutoClose");
            onUserWatch("onWindowFocusChanged");
        }
    }

    @Override
    public void onBackPressed() {
        Activity activity = getActiveActivity();
        if (activity == null) {
            return;
        }
        if (mDeskResourceData != null && mDeskResourceData.fullActProductInfo != null) {
            pushClick(mDeskResourceData.fullActProductInfo.leftTarget, mDeskResourceData.fullActProductInfo.leftTargetBack, "back");
        } else {
            removeView("back");
        }
    }

    @Override
    public void onScreenShot() {
        sendBroadcastToCancelNf();
        Activity activity = getActiveActivity();
        if (activity == null) {
            return;
        }
        if (FeedbackExtensions.isDeskScreenShot(mDeskResourceData)) {
            try {
                showScreenShotView(activity, rootView);
            } catch (Throwable t) {
                removeView(DeskCloseTypeEnum.USER_SCREEN_SHOT.getMessage());
            }
        } else {
            removeView(DeskCloseTypeEnum.USER_SCREEN_SHOT.getMessage());
        }
    }

    private void initDeliveryResourceData() {
        if (!TextUtils.isEmpty(mDeskResourceData.sessionId)) {
            mDeliveryResourceInfo = DeliveryResourceDataCache.deliveryResourceInfoMap.get(mDeskResourceData.sessionId);
            mDeskInfo = mDeliveryResourceInfo.deskInfo;
        }
    }

    private void onUserWatch(String from) {
        if (!mHasFocus) {
            log("onUserWatch-" + from);
            mHasFocus = true;
            long exposureExpireTime = mDeskResourceData != null && mDeskResourceData.sceneParam != null ?
                    mDeskResourceData.sceneParam.exposureExpireTime : -1;
            if (exposureExpireTime > 0 && System.currentTimeMillis() > exposureExpireTime) {
                removeView("TIME_OUT");
                return;
            }
            mExposureTime = System.currentTimeMillis();
            mExposureUpTime = SystemClock.uptimeMillis();
            DexDeliveryReporter.reportPushStageSample(mDeskResourceData, ReportParamsKey.STAGE.STAGE_EXPOSURE_SAMPLE, mDeskSourceEnum);
            if (feedbackView != null) {
                feedbackView.onShow();
            }
            if (pushSettingView != null) {
                pushSettingView.onShow();
            }

            triggerAutoClose();
            if (registerScreenShotAfterFocus) {
                // 截屏监听
                registerScreenShotListener(ScreenShotManager.ScreenShotEnum.DESK,
                        mDeskResourceData, mDeskSourceEnum, FeedbackExtensions.SCENE_DESK_PUSH);
            }
        }
    }

    private void triggerAutoClose() {
        if (autoCloseRunnable != null) {
            return;
        }
        long delayMillis = getAutoCloseTime(mDeskResourceData);
        autoCloseRunnable = new Runnable() {
            @Override
            public void run() {
                removeView(DeskCloseTypeEnum.AUTO_CLOSE.getMessage());
            }
        };
        mHandler.postDelayed(autoCloseRunnable, delayMillis);
    }

    /**
     * 发送关闭通知栏的广播
     */
    private void sendBroadcastToCancelNf() {
        try {
            Intent intent = new Intent(HadesNotificationKey.MANAGER.CANCEL_ACTION);
            intent.putExtra(HadesNotificationKey.MANAGER.CANCEL_TYPE, HadesNotificationKey.MANAGER.CANCEL_TYPE_DESK_SHOT);
            HadesUtilsAdapter.getContext().sendBroadcast(intent);
        } catch (Throwable t) {
        }
    }

    private void initView(final Activity activity) {
        final Context context = activity.getApplicationContext();
        showProductDialog = (mDeskResourceData.fullActProductInfo != null);
        final ImageView backgroundView = activity.findViewById(DeskFloatWinViewAdapter.get_full_scr_background());
        View fullMask = activity.findViewById(DeskFloatWinViewAdapter.get_full_scr_mask());
        fullContainerView = activity.findViewById(DeskFloatWinViewAdapter.get_full_container_view());
        deskCenterImageView = activity.findViewById(DeskFloatWinViewAdapter.get_full_desk_center_image());
        closeImageView = activity.findViewById(DeskFloatWinViewAdapter.get_full_close_image());
        rootView = activity.findViewById(DeskFloatWinViewAdapter.get_full_root_layout());
        if (rootView == null) {
            removeView("ROOT_VIEW_NULL");
            return;
        }

        //有背景资源，走拆分展示逻辑
        if (!showProductDialog
                && isCombinationMode()
                && deskCenterImageView != null
                && backgroundView != null
                && fullMask != null) {
            log("initView 1");
            fullMask.setVisibility(View.VISIBLE);
            deskCenterImageView.setVisibility(View.VISIBLE);
            final byte[] backgroundData = DeliveryDataManager
                    .getImageResource(mDeskResourceData.imageIdMap.get(DeskResourceData.BACKGROUND_ID));
            if (backgroundData != null) {
                final int statusBarHeight = UiUtilsAdapter.getStatusBarHeight();
                HadesUtilsAdapter.runOnWorkThread(new Runnable() {
                    @Override
                    public void run() {
                        final byte[] cropImageByteArray = ImageUtils.cropImageByteArray(backgroundData, 0, statusBarHeight);
                        HadesUtilsAdapter.runOnMainThread(new Runnable() {
                            @Override
                            public void run() {
                                loadImage(activity, cropImageByteArray, backgroundView, false, true);
                            }
                        });
                    }
                });
            } else if (!TextUtils.isEmpty(mDeskResourceData.background)) {
                loadImage(activity, mDeskResourceData.background, backgroundView, false, true);
            } else {
                log("removeView IMAGE_NULL 1");
                removeView(DeskCloseTypeEnum.IMAGE_NULL.getMessage());
                return;
            }
            CloseButtonPosEnum closeButtonPos = getCloseButtonPos(mDeskResourceData.closeButtonPos);
            if (isForegroundShowBottom()) {
                initBottomImage(context, closeButtonPos);
            } else {
                initCenterImage(activity, closeButtonPos);
            }
            //无背景资源，走全图逻辑
        } else if (backgroundView != null && isSingleMode()) {
            if (deskCenterImageView != null) {
                deskCenterImageView.setVisibility(View.GONE);
            }
            if (fullMask != null) {
                fullMask.setVisibility(View.GONE);
            }

            HadesUtilsAdapter.runOnWorkThread(new Runnable() {
                @Override
                public void run() {
                    String imageId = mDeskResourceData.imageIdMap.get(DeskResourceData.FULL_IMAGE_ID);
                    byte[] imageData = DeliveryDataManager.getImageResource(imageId);
                    if (showProductDialog) {
                        byte[] LastPageImageData = StorageHelperAdapter.getLastPageData(context);
                        if (LastPageImageData != null && LastPageImageData.length > 0) {
                            imageData = LastPageImageData;
                            DexDeliveryReporter.reportPushStageSample(mDeskResourceData, ReportParamsKey.STAGE.STAGE_USE_DSP_SCREEN_SHOT, mDeskSourceEnum);
                            BabelHelperAdapter.log(ReportParamsKey.STAGE.STAGE_USE_DSP_SCREEN_SHOT, LastPageImageData.length + "");
                            StorageHelperAdapter.removeLastPageData(context);
                        }
                        if (imageData != null) {
                            StorageHelperAdapter.setDspRetainData(context, imageData, imageId);
                        }
                    }
                    if (imageData != null) {
                        loadImage(activity, imageData, backgroundView, showProductDialog, true);
                    } else if (!TextUtils.isEmpty(mDeskResourceData.image)) {
                        loadImage(activity, mDeskResourceData.image, backgroundView, showProductDialog, true);
                    } else {
                        log("removeView IMAGE_NULL 2");
                        HadesUtilsAdapter.runOnMainThread(new Runnable() {
                            @Override
                            public void run() {
                                removeView(DeskCloseTypeEnum.IMAGE_NULL.getMessage());
                            }
                        });
                    }
                }
            });
        } else {
            removeView("resource_error");
            return;
        }

        if (closeImageView != null) {
            if (showProductDialog) {
                closeImageView.setVisibility(View.GONE);
            } else {
                closeImageView.setVisibility(View.VISIBLE);
                closeImageView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        removeView(DeskCloseTypeEnum.USER_X.getMessage());
                    }
                });
                if (!TextUtils.isEmpty(mDeskResourceData.closeImage)) {
                    try {
                        PicassoAdapter.picasso(context, mDeskResourceData.closeImage, closeImageView);
                    } catch (Throwable e) {
                    }
                }
            }
        }

        if (showProductDialog) {
            dialogView = new WalMaiFullScrDialogView(context);
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(
                    RelativeLayout.LayoutParams.MATCH_PARENT,
                    RelativeLayout.LayoutParams.MATCH_PARENT);
            rootView.addView(dialogView, layoutParams);
            initDialogView(context);
        }
        showFeedbackView(context, rootView);

        if (!TextUtils.isEmpty(mDeskResourceData.backgroundTarget)) {
            backgroundView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (TextUtils.equals("close", mDeskResourceData.backgroundTarget)) {
                        removeView("background");
                    } else {
                        pushClick(mDeskResourceData.backgroundTarget, mDeskResourceData.targetBack, "background");
                    }
                }
            });
        }
    }

    private void initCenterImage(final Activity activity, CloseButtonPosEnum closeButtonPos) {
        Context context = activity.getApplicationContext();
        deskCenterImageView.setVisibility(View.VISIBLE);
        if (!TextUtils.isEmpty(mDeskResourceData.target)) {
            deskCenterImageView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    pushClick(mDeskResourceData.target, mDeskResourceData.targetBack, "center");
                }
            });
        }

        //拆分逻辑，走新关闭按钮逻辑
        if (closeButtonPos == CloseButtonPosEnum.LEFT) {
            closeImageView = activity.findViewById(DeskFloatWinViewAdapter.get_full_close_image_left());
        } else if (closeButtonPos == CloseButtonPosEnum.RIGHT) {
            closeImageView = activity.findViewById(DeskFloatWinViewAdapter.get_full_close_image_right());
        } else {
            closeImageView = activity.findViewById(DeskFloatWinViewAdapter.get_full_close_image_bottom());
        }

        byte[] imageData = DeliveryDataManager
                .getImageResource(mDeskResourceData.imageIdMap.get(DeskResourceData.FULL_IMAGE_ID));
        if (imageData != null) {
            loadCenterImage(context, imageData);
        } else if (!TextUtils.isEmpty(mDeskResourceData.image)) {
            loadCenterImage(context, mDeskResourceData.image);
        } else {
            log("removeView IMAGE_NULL 1");
            removeView(DeskCloseTypeEnum.IMAGE_NULL.getMessage());
        }
    }

    private void initBottomImage(final Context context, final CloseButtonPosEnum closeButtonPos) {
        final ImageView bottomImageView = new ImageView(context);
        bottomImageView.setAdjustViewBounds(true);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.BOTTOM;
        fullContainerView.addView(bottomImageView, params);
        byte[] imageData = DeliveryDataManager
                .getImageResource(mDeskResourceData.imageIdMap.get(DeskResourceData.FULL_IMAGE_ID));
        LoadResult loadResult = new LoadResult() {
            @Override
            public void onSuccess() {
                int height = bottomImageView.getHeight();
                if (height > 0) {
                    showBottomCloseView(context, closeButtonPos, height);
                    return;
                }
                ViewTreeObserver viewTreeObserver = bottomImageView.getViewTreeObserver();
                viewTreeObserver.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                    @Override
                    public void onGlobalLayout() {
                        // 移除监听器，以避免多次回调
                        bottomImageView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                        showBottomCloseView(context, closeButtonPos, bottomImageView.getHeight());
                    }
                });
            }
        };
        if (imageData != null) {
            loadBottomImage(context, bottomImageView, imageData, loadResult);
        } else if (!TextUtils.isEmpty(mDeskResourceData.image)) {
            loadBottomImage(context, bottomImageView, mDeskResourceData.image, loadResult);
        } else {
            log("removeView IMAGE_NULL 1");
            removeView(DeskCloseTypeEnum.IMAGE_NULL.getMessage());
        }
        if (!TextUtils.isEmpty(mDeskResourceData.target)) {
            bottomImageView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    pushClick(mDeskResourceData.target, mDeskResourceData.targetBack, "center");
                }
            });
        }
    }

    private void showBottomCloseView(Context context, CloseButtonPosEnum closeButtonPos, int height) {
        if (closeImageView == null) {
            return;
        }
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) closeImageView.getLayoutParams();
        if (closeButtonPos == CloseButtonPosEnum.RIGHT) {
            params.removeRule(RelativeLayout.CENTER_HORIZONTAL);
            params.addRule(RelativeLayout.ALIGN_PARENT_END);
            params.bottomMargin = height - UiUtilsAdapter.pixelOfDp(context, 34);
            params.rightMargin = UiUtilsAdapter.pixelOfDp(context, 10);
            closeImageView.setLayoutParams(params);
        } else {
            params.removeRule(RelativeLayout.CENTER_HORIZONTAL);
            params.removeRule(RelativeLayout.ALIGN_PARENT_END);
            params.addRule(RelativeLayout.ALIGN_PARENT_START);
            params.bottomMargin = height + UiUtilsAdapter.pixelOfDp(context, 10);
            params.leftMargin = UiUtilsAdapter.pixelOfDp(context, 10);
            closeImageView.setLayoutParams(params);
        }
    }

    private boolean isShowFlash() {
        return TextUtils.equals("flash",  DeliveryResourceHelper.getSpecialEffects(mDeskInfo));
    }

    private boolean isForegroundShowBottom() {
        return TextUtils.equals("bottom",  DeliveryResourceHelper.getForegroundPosition(mDeskInfo));
    }

    private void showFlashLottieView(final ViewGroup rootLayout) {
        try {
            if (!isShowFlash()) {
                return;
            }
            if (rootLayout == null) {
                return;
            }
            if (deskCenterImageView == null || deskCenterImageView.getVisibility() == View.GONE) {
                return;
            }
            final Activity activity = getActiveActivity();
            if (activity == null) {
                return;
            }
            int height = deskCenterImageView.getHeight();
            if (height > 0) {
                realShowLottie(activity, rootLayout);
                return;
            }
            ViewTreeObserver viewTreeObserver = deskCenterImageView.getViewTreeObserver();
            viewTreeObserver.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    // 移除监听器，以避免多次回调
                    deskCenterImageView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    realShowLottie(activity, rootLayout);
                }
            });
        } catch (Throwable t) {
            LogUtils.d(TAG, t.getMessage());
        }
    }

    private void realShowLottie(Activity activity, final ViewGroup rootLayout) {
        lottieAnimationView = new LottieAnimationView(activity);
        lottieAnimationView.setVisibility(View.GONE);
        lottieAnimationView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        int height = deskCenterImageView.getHeight();
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                RelativeLayout.LayoutParams.MATCH_PARENT,
                height);
        params.gravity = Gravity.CENTER;
        rootLayout.addView(lottieAnimationView, params);
        // 设置动画为无限循环
        lottieAnimationView.setRepeatCount(LottieDrawable.INFINITE);
        LottieUtilsAdapter.playRemoteLottieAnimation(lottieAnimationView, activity,
                "https://s3plus.meituan.net/v1/mss_1ada830d56584ddeae1b0899c231c552/goku/lottie/data1735097534338.json",
                new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                        super.onAnimationStart(animation);
                        lottieAnimationView.setVisibility(View.VISIBLE);
                    }
                });
    }

    private void showScreenShotView(final Activity activity, final ViewGroup rootLayout) {
        if (rootLayout == null) {
            return;
        }
        Context context = HadesUtilsAdapter.getContext();
        ScreenShotView screenShotView = new ScreenShotView(context, activity, mDeskResourceData, new ScreenShotView.SsViewOnClick() {
            @Override
            public void onClickFeedback() {
                FeedbackHelper.jumpToFeedbackPage(activity.getApplicationContext(),
                        mDeskResourceData.target, mDeskResourceData.marketingType,
                        mDeskResourceData.sessionId, mDeskResourceData.pushResId,
                        FeedbackExtensions.getPsText(mDeskResourceData),
                        FeedbackExtensions.getDeskRiskLevel(mDeskResourceData), true);
                removeView("CLICK_FEED_BACK_SS");
            }

            @Override
            public void onClickNoDisturb() {
                FeedbackHelper.jumpToSilenceSettingActivity(activity.getApplicationContext(),
                        mDeskResourceData.target, mDeskResourceData.marketingType,
                        mDeskResourceData.sessionId, mDeskResourceData.pushResId,
                        FeedbackExtensions.getPsText(mDeskResourceData), true, true);
                removeView("CLICK_NO_DISTURB_SS");
            }

            @Override
            public void onClickPushSetting() {
                FeedbackHelper.jumpToSilenceSettingActivity(activity.getApplicationContext(),
                        mDeskResourceData.target, mDeskResourceData.marketingType,
                        mDeskResourceData.sessionId, mDeskResourceData.pushResId,
                        FeedbackExtensions.getPsText(mDeskResourceData), true, false);
                removeView("CLICK_SETTING_SS");
            }

            @Override
            public void onClickClose(ScreenShotView ssView) {
                ssView.setVisibility(View.GONE);
                rootLayout.removeView(ssView);
            }
        });
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
                RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        params.rightMargin = UiUtilsAdapter.pixelOfDp(context, 10);

        // 设置布局规则，使其位于右侧中间
        params.addRule(RelativeLayout.CENTER_VERTICAL);
        params.addRule(RelativeLayout.ALIGN_PARENT_END);
        rootLayout.addView(screenShotView, params);
        if (autoCloseRunnable != null) {
            mHandler.removeCallbacks(autoCloseRunnable);
        }
        int closeTime = FeedbackExtensions.getDeskScreenShotCloseTime(mDeskResourceData);
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                removeView("SS_AUTO_CLOSE");
            }
        }, closeTime * 1000);
    }

    private void showFeedbackView(final Context context, ViewGroup rootLayout) {
        if (rootLayout == null) {
            return;
        }
        if (!FeedbackExtensions.isDeskHighRiskUser(mDeskResourceData) && !FeedbackExtensions.isDeskMiddleRiskUser(mDeskResourceData)) {
            return;
        }
        // 创建第一个CircularView
        feedbackView = new CircularView(context, mDeskResourceData, CircularView.TYPE_FEEDBACK, new CircularView.OnClickCallback() {
            @Override
            public void onClick() {
                removeView("CLICK_FEED_BACK");
            }
        });
        RelativeLayout.LayoutParams firstParams = new RelativeLayout.LayoutParams(
                UiUtilsAdapter.pixelOfDp(context, 48),
                UiUtilsAdapter.pixelOfDp(context, 48));
        firstParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        firstParams.addRule(RelativeLayout.ALIGN_PARENT_END);
        firstParams.bottomMargin = UiUtilsAdapter.pixelOfDp(context, 47 + 48 + 12); // 底部47dp + 第二个View的高度48dp + 间距12dp
        firstParams.rightMargin = UiUtilsAdapter.pixelOfDp(context, 10); // 右侧间距10dp
        rootLayout.addView(feedbackView, firstParams);
    }

    private void initDialogView(final Context context) {
        dialogView.init(mDeskResourceData, new WalMaiFullScrDialogView.FullSrcDialogListener() {
            @Override
            public void onError() {
                removeView(DeskCloseTypeEnum.EXCEPTION_CLOSE.getMessage());
            }

            @Override
            public void onClickLeft() {
                dialogView.setVisibility(View.GONE);
                if (mDeskResourceData != null && mDeskResourceData.fullActProductInfo != null) {
                    pushClick(mDeskResourceData.fullActProductInfo.leftTarget, mDeskResourceData.fullActProductInfo.leftTargetBack, "leftButton");
                }
            }

            @Override
            public void onClickRight() {
                if (mDeskResourceData != null && mDeskResourceData.fullActProductInfo != null) {
                    pushClick(mDeskResourceData.fullActProductInfo.rightTarget,  mDeskResourceData.fullActProductInfo.rightTargetBack, "rightButton");
                }
            }

            @Override
            public void onClickCloseX() {
                dialogView.setVisibility(View.GONE);
                if (mDeskResourceData != null && mDeskResourceData.fullActProductInfo != null) {
                    pushClick(mDeskResourceData.fullActProductInfo.leftTarget, mDeskResourceData.fullActProductInfo.leftTargetBack, "closeX");
                }
            }

            @Override
            public void onClickMask() {
                dialogView.setVisibility(View.GONE);
                if (mDeskResourceData != null && mDeskResourceData.fullActProductInfo != null) {
                    if (mDeskResourceData.fullActProductInfo.actionType == FullActProductInfo.ACTION_TYPE_PAY
                            || mDeskResourceData.fullActProductInfo.actionType == FullActProductInfo.ACTION_TYPE_SHOP) {
                        pushClick(mDeskResourceData.fullActProductInfo.leftTarget, mDeskResourceData.fullActProductInfo.leftTargetBack, "mask");
                    } else if (mDeskResourceData.fullActProductInfo.actionType == FullActProductInfo.ACTION_TYPE_BROWSE) {
                        pushClick(mDeskResourceData.fullActProductInfo.rightTarget, mDeskResourceData.fullActProductInfo.rightTargetBack, "mask");
                    }
                }
            }

            @Override
            public void onClickContent() {
                if (mDeskResourceData != null && mDeskResourceData.fullActProductInfo != null) {
                    pushClick(mDeskResourceData.fullActProductInfo.rightTarget, mDeskResourceData.fullActProductInfo.rightTargetBack, "content");
                }
            }

            @Override
            public void onCountDownFinish() {
                if (!HadesUtilsAdapter.isScreenOn(context) || HadesUtilsAdapter.isLocked()) {
                    removeView(DeskCloseTypeEnum.ORDER_INVALID.getMessage());
                }
            }
        });
    }

    private void loadCenterImage(final Context context, final Object image) {
        LoadResult loadResult = new LoadResult() {
            @Override
            public void onSuccess() {
                showFlashLottieView(fullContainerView);
            }
        };
        if (image instanceof byte[]) {
            loadImage(context, (byte[]) image, deskCenterImageView, false, false, loadResult);
        } else if (image instanceof String) {
            loadImage(context, (String) image, deskCenterImageView, false, false, loadResult);
        }
    }

    private void loadBottomImage(final Context context, final ImageView view, final Object image, final LoadResult loadResult) {
        if (image instanceof byte[]) {
            loadImage(context, (byte[]) image, view, false, false, loadResult);
        } else if (image instanceof String) {
            loadImage(context, (String) image, view, false, false, loadResult);
        }
    }

    private void loadImage(final Context context, final byte[] imageData, final ImageView view,
                           final boolean needBlurTrans, final boolean needCenterCrop) {
        loadImage(context, imageData, view, needBlurTrans, needCenterCrop, null);
    }

    private void loadImage(final Context context, final String imageUrl, final ImageView view,
                           final boolean needBlurTrans, final boolean needCenterCrop) {
        loadImage(context, imageUrl, view, needBlurTrans, needCenterCrop, null);
    }

    private void loadImage(final Context context, final byte[] imageData, final ImageView view,
                           final boolean needBlurTrans, final boolean needCenterCrop, final LoadResult loadResult) {
        HadesUtilsAdapter.runOnMainThread(new Runnable() {
            @Override
            public void run() {
                try {
                    PicassoAdapter.loadFullImage(context, imageData, view, needBlurTrans, needCenterCrop,
                            new PicassoAdapter.CallbackAdapter() {
                                @Override
                                public void onSuccess() {
                                    if (loadResult != null) {
                                        loadResult.onSuccess();
                                    }
                                    log("image byte load success");
                                }

                                @Override
                                public void onError() {
                                    log("image byte load error");
                                    removeView(DeskCloseTypeEnum.IMAGE_ERROR.getMessage());
                                }
                            });
                } catch (Throwable t) {
                    log("loadImageByte: " + t.getMessage());
                    removeView(DeskCloseTypeEnum.IMAGE_ERROR.getMessage());
                }
            }
        });
    }

    private void loadImage(final Context context, final String imageUrl, final ImageView view,
                           final boolean needBlurTrans, final boolean needCenterCrop, final LoadResult loadResult) {
        HadesUtilsAdapter.runOnMainThread(new Runnable() {
            @Override
            public void run() {
                try {
                    if (context == null || view == null || TextUtils.isEmpty(imageUrl)) {
                        removeView(DeskCloseTypeEnum.IMAGE_ERROR.getMessage());
                        return;
                    }
                    PicassoAdapter.loadFullImage(context, imageUrl, view, needBlurTrans, needCenterCrop,
                            new PicassoAdapter.CallbackAdapter() {
                                @Override
                                public void onSuccess() {
                                    if (loadResult != null) {
                                        loadResult.onSuccess();
                                    }
                                    log("image url load success");
                                }

                                @Override
                                public void onError() {
                                    log("image url load error");
                                    removeView(DeskCloseTypeEnum.IMAGE_ERROR.getMessage());
                                }
                            });
                } catch (Throwable t) {
                    log("loadImageUrl: " + t.getMessage());
                    removeView(DeskCloseTypeEnum.IMAGE_ERROR.getMessage());
                }
            }
        });
    }

    private void reportPushClose(Context context, String deskCloseType) {
        log("mClickAction" + mClickAction + ", mReportClose:" + mReportClose);
        if (!mClickAction && !mReportClose) { // 没有上报过点击事件并且没有上报过关闭事件，则上报关闭事件
            String extraMapStr = "";
            try {
                HashMap<String, String> extraMap = new HashMap<>();
                extraMap.put("isLockWhenShow", mIsLockedWhenShow ? "1" : "0");
                extraMapStr = HadesUtilsAdapter.toJson(extraMap);
            } catch (Throwable t) {
            }
            mReportClose = true;
            if (mDeskResourceData == null) {
                mDeskResourceData = new DeskResourceData(DeskTypeEnum.FULL, "");
            }
            DexDeliveryReporter.reportPushClose(context, DexDeliveryReporter.STAGE_CLOSE, mDeskSourceEnum, deskCloseType,
                    mDeskResourceData, mActionEndTime, mActionEndUpTime, -1, extraMapStr, "");
            LogicCpuTimeCollectorAdapter.logicEnd(mDeskSourceEnum == null ? "" : mDeskSourceEnum.name(), mDeskResourceData == null ? "" : mDeskResourceData.scene);
        }
    }

    private void pushClick(String targetUrl, List<String> targetBackList, final String reportScene) {
        final Context context = HadesUtilsAdapter.getContext();
        mClickAction = true;
        mActionEndTime = System.currentTimeMillis() - mExposureTime;
        mActionEndUpTime = SystemClock.uptimeMillis() - mExposureUpTime;
        mDeskResourceData.pushClickElapsedTime = SystemClock.elapsedRealtime();
        mDeskResourceData.needRouterActBackground = showProductDialog;
        DexDeliveryUtils.jump(
                getRouterScene(),
                context,
                targetUrl,
                targetBackList,
                mDeskResourceData,
                mDeskSourceEnum,
                "",
                new DexDeliveryUtils.JumpCallback() {
                    @Override
                    public void jumpResult(Pair<Boolean, String> result) {
                        if (result.first) {
                            String lch = result.second;
                            DexDeliveryReporter.reportPushClick(context, DexDeliveryReporter.STAGE_CLICK, mDeskSourceEnum,
                                    mActionEndTime, mActionEndUpTime, lch, mDeskResourceData, -1, false, reportScene);
                            boolean needFinish = !showProductDialog;
                            removeView(DeskCloseTypeEnum.USER_CLICK.getMessage(), needFinish);
                        } else {
                            removeView(result.second);
                        }
                    }
                }
        );
    }

    private void removeView(final String deskCloseType) {
        removeView(deskCloseType, true);
    }

    private void removeView(final String deskCloseType, boolean needFinish) {
        Context context = HadesUtilsAdapter.getContext();
        log("removeView:" + deskCloseType + ",screenon:" + HadesUtilsAdapter.isScreenOn(context));
        unRegisterScreenShotListener();
        DeliveryDataManager.clearImageResource();

        DeliveryDataManager.sViewAttached = false;
        if (lottieAnimationView != null && fullContainerView != null) {
            fullContainerView.removeView(lottieAnimationView);
        }

        if (TextUtils.equals(deskCloseType, DeskCloseTypeEnum.USER_CLICK.getMessage())) {
            // 点击行为的remove按照点击发生的时间上报, 不需要更新时间
        } else {
            mActionEndTime = System.currentTimeMillis() - mExposureTime;
            mActionEndUpTime = SystemClock.uptimeMillis() - mExposureUpTime;
            // 非用户点击，后台有预渲染view，kill预渲染页面
            if (isPreloadActivityStart && !mClickAction) {
                QPreloadJobServiceAdapter.finishPreRender(context);
            }
        }
        reportPushClose(context, deskCloseType);
        if (autoCloseRunnable != null) {
            mHandler.removeCallbacks(autoCloseRunnable);
        }
        unregisterUnlockReceiver();
        try {
            Activity activity = getActiveActivity();
            if (activity != null && needFinish) {
                activity.finish();
            }
        } catch (Throwable t) {
            log("remove fail " + t.getMessage());
        }
    }

    @NonNull
    private CloseButtonPosEnum getCloseButtonPos(int closeButtonPos) {
        for (CloseButtonPosEnum pos : CloseButtonPosEnum.values()) {
            if (pos.getCode() == closeButtonPos) {
                return pos;
            }
        }
        return CloseButtonPosEnum.BOTTOM;
    }

    private void log(String info) {
        LogUtils.d(TAG, info);
    }

    private boolean isCombinationMode() {
        return (TextUtils.equals(DeskResourceData.TYPE_FULL_SPLICE, mDeskResourceData.dspFullPattern)
                || TextUtils.equals("dsp_full", mDeskResourceData.dspFullPattern)
                || TextUtils.equals("enormous_delphinus_museum", mDeskResourceData.dspFullPattern))
                && (!TextUtils.isEmpty(mDeskResourceData.background) || !TextUtils.isEmpty(mDeskResourceData.imageIdMap.get(DeskResourceData.BACKGROUND_ID)))
                && (!TextUtils.isEmpty(mDeskResourceData.image) || !TextUtils.isEmpty(mDeskResourceData.imageIdMap.get(DeskResourceData.FULL_IMAGE_ID)));
    }

    private boolean isSingleMode() {
        if (showProductDialog) {
            return !TextUtils.isEmpty(mDeskResourceData.image) || !TextUtils.isEmpty(mDeskResourceData.imageIdMap.get(DeskResourceData.FULL_IMAGE_ID));
        } else {
            return (TextUtils.equals(DeskResourceData.TYPE_FULL_WHOLE, mDeskResourceData.dspFullPattern)
                    || TextUtils.equals("huge_cygnus_aquarium", mDeskResourceData.dspFullPattern))
                    && (!TextUtils.isEmpty(mDeskResourceData.image) || !TextUtils.isEmpty(mDeskResourceData.imageIdMap.get(DeskResourceData.FULL_IMAGE_ID)));
        }
    }

    private long getAutoCloseTime(DeskResourceData deskResourceData) {
        return deskResourceData != null && deskResourceData.closeTime > 0
                ? deskResourceData.closeTime * 1000L : 10000;
    }

    @Nullable
    private Activity getActiveActivity() {
        Activity activity = activityRef.get();
        if (activity != null && !activity.isFinishing() && !activity.isDestroyed()) {
            return activity;
        } else {
            return null;
        }
    }

    private interface LoadResult {
        void onSuccess();
    }
}
