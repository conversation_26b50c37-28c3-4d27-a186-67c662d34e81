
package com.meituan.android.hades.dexdelivery.desk.ui;

// Android系统相关导入
import android.animation.Animator; // 动画器基类，用于控制动画效果
import android.animation.AnimatorListenerAdapter; // 动画监听器适配器，简化动画回调实现
import android.app.Activity; // Activity基类，Android四大组件之一
import android.content.BroadcastReceiver; // 广播接收器，用于接收系统或应用广播
import android.content.Context; // 上下文环境，提供应用相关信息和资源访问
import android.content.Intent; // 意图对象，用于组件间通信和数据传递
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Build; // 系统版本信息，用于版本兼容性判断
import android.os.Bundle; // 数据传递容器，Activity间数据传递
import android.os.Handler; // 消息处理器，用于线程间通信和延时任务
import android.os.Looper; // 消息循环器，处理消息队列
import android.os.SystemClock; // 系统时钟，提供系统运行时间
import android.support.annotation.NonNull; // 非空注解，标记参数或返回值不能为null
import android.support.annotation.Nullable; // 可空注解，标记参数或返回值可以为null
import android.support.v4.content.ContextCompat; // 兼容性上下文工具，处理不同版本API差异
import android.text.TextUtils; // 文本工具类，提供字符串操作方法
import android.util.Pair; // 键值对工具类，用于返回两个相关值
import android.view.Gravity; // 视图重力定位，控制子视图在父容器中的位置
import android.view.View; // 视图基类，所有UI组件的父类
import android.view.ViewGroup; // 视图组基类，可包含子视图的容器
import android.view.ViewTreeObserver; // 视图树观察器，监听视图树变化
import android.widget.FrameLayout; // 框架布局，简单的容器布局
import android.view.Window; // 窗口对象，控制Activity窗口属性
import android.widget.ImageView; // 图片视图，用于显示图片
import android.widget.RelativeLayout; // 相对布局，根据相对位置排列子视图

// 第三方库导入
import com.airbnb.lottie.LottieAnimationView; // Lottie动画视图，显示矢量动画
import com.airbnb.lottie.LottieDrawable; // Lottie动画绘制器，控制动画属性

// 内部模块导入 - 广播相关
import com.meituan.android.hades.broadcast.BroadcastReceiverX; // 扩展广播接收器，提供额外功能
// 内部模块导入 - 投放相关
import com.meituan.android.hades.dexdelivery.delivery.helper.DeliveryResourceHelper; // 投放资源帮助类，处理资源配置
import com.meituan.android.hades.dexdelivery.delivery.helper.FeedbackHelper; // 反馈帮助类，处理用户反馈相关功能
import com.meituan.android.hades.dexdelivery.delivery.reporter.DexDeliveryReporter; // 投放上报器，统计和上报数据
import com.meituan.android.hades.dexdelivery.delivery.reporter.ReportParamsKey; // 上报参数键定义，标准化上报字段
// 内部模块导入 - UI视图组件
import com.meituan.android.hades.dexdelivery.desk.ui.view.CircularView; // 圆形视图，用于反馈按钮等圆形UI
import com.meituan.android.hades.dexdelivery.desk.ui.view.ScreenShotView; // 截屏视图，处理截屏相关UI
import com.meituan.android.hades.dexdelivery.desk.ui.view.WalMaiFullScrDialogView; // 外卖全屏对话框视图，显示产品信息
// 内部模块导入 - 数据模型
import com.meituan.android.hades.dexdelivery.model.DeliveryDeskMaterial; // 投放桌面素材模型
import com.meituan.android.hades.dexdelivery.model.DeliveryResourceDataCache; // 投放资源数据缓存，提高性能
import com.meituan.android.hades.dexdelivery.model.DeliveryResourceInfo; // 投放资源信息，包含完整资源数据
import com.meituan.android.hades.dexdelivery.model.DeskArea; // 桌面区域，定义显示区域和配置
// 内部模块导入 - 工具类
import com.meituan.android.hades.dexdelivery.utils.DexDeliveryUtils; // 投放工具类，提供通用功能
import com.meituan.android.hades.dexdelivery.utils.ImageUtils; // 图片工具类，处理图片裁剪、压缩等
import com.meituan.android.hades.dexdelivery.utils.LogUtils; // 日志工具类，统一日志输出格式
// 内部模块导入 - 适配器类
import com.meituan.android.hades.dyadater.BabelHelperAdapter; // Babel帮助适配器，处理多语言和埋点
import com.meituan.android.hades.dyadater.PicassoAdapter; // Picasso图片加载适配器，封装图片加载逻辑
import com.meituan.android.hades.dyadater.StorageHelperAdapter; // 存储帮助适配器，处理本地数据存储
import com.meituan.android.hades.dyadater.ad.HadesNotificationKey; // 通知键定义，标准化通知相关常量
import com.meituan.android.hades.dyadater.container.adapter.QtitansFlexCardViewAdapter;
import com.meituan.android.hades.dyadater.desk.CloseButtonPosEnum; // 关闭按钮位置枚举，定义按钮显示位置
import com.meituan.android.hades.dyadater.desk.DeliveryDataManager; // 投放数据管理器，管理投放相关数据
import com.meituan.android.hades.dyadater.desk.DeskResourceData; // 桌面资源数据，包含展示所需的所有信息
import com.meituan.android.hades.dyadater.desk.DeskSourceEnum; // 桌面来源枚举，标识推送来源渠道
import com.meituan.android.hades.dyadater.desk.FullScrActivityStatusCallback; // 全屏Activity状态回调接口
import com.meituan.android.hades.dyadater.desk.IFlexViewClickEventListenerAdapter;
import com.meituan.android.hades.dyadater.desk.IFlexViewShowStatusListenerAdapter;
import com.meituan.android.hades.dyadater.desk.feedback.FeedbackExtensions; // 反馈扩展，处理反馈相关业务逻辑
import com.meituan.android.hades.dyadater.desk.ui.DeskFloatWinViewAdapter; // 桌面浮窗视图适配器，获取布局资源ID
import com.meituan.android.hades.dyadater.desk.ui.QQFlexEvent;
import com.meituan.android.hades.dyadater.monitor.LogicCpuTimeCollectorAdapter; // CPU时间收集适配器，性能监控
import com.meituan.android.hades.dyadater.utils.HadesUtilsAdapter; // 工具适配器，提供通用工具方法
import com.meituan.android.hades.dyadater.utils.LottieUtilsAdapter; // Lottie工具适配器，处理动画相关功能
import com.meituan.android.hades.dyadater.utils.QPreloadJobServiceAdapter; // 预加载任务服务适配器，优化页面加载性能
import com.meituan.android.hades.dyadater.utils.QTitansPreloadActivityAdapter; // Titans预加载Activity适配器，透明预加载
import com.meituan.android.hades.dyadater.utils.UiUtilsAdapter; // UI工具适配器，提供UI相关工具方法
import com.meituan.android.hades.impl.desk.DeskCloseTypeEnum; // 桌面关闭类型枚举，定义各种关闭原因
import com.meituan.android.hades.impl.desk.DeskTypeEnum; // 桌面类型枚举，定义不同的展示类型
import com.meituan.android.hades.impl.desk.ScreenShotManager; // 截屏管理器，处理截屏检测和响应
import com.meituan.android.hades.impl.model.FullActProductInfo; // 全屏Activity产品信息，包含产品展示相关数据
import com.sankuai.meituan.mbc.module.Json;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.Target;

// Java标准库导入
import java.io.ByteArrayOutputStream;
import java.lang.ref.WeakReference; // 弱引用，避免内存泄漏
import java.util.HashMap; // 哈希映射，键值对数据结构
import java.util.List; // 列表接口，有序集合
import java.util.Map;


/**

 *
 * 配置示例（JSON格式）：
 * {
 *   "templatePage": {
 *     "templateName": "模版名称",
 *     "templateUrl": "模版地址",
 *     "pageMap": {
 *       "unlockFullSplice": {
 *         "foregroundTarget": "https://example.com/foreground",       // 前景图点击跳转链接
 *         "foregroundImageId": "fg_image_123456",                     // 前景图资源ID
 *         "foregroundImageUrl": "前景图片URL地址",
 *         "forgroundPosition": "bottom",                              // 前景图位置：center/bottom
 *         "backgroundTarget": "https://example.com/background",       // 背景图点击跳转链接
 *         "backgroundImageId": "bg_image_789012",                     // 背景图资源ID
 *         "backgroundImageUrl": "背景图片URL地址",
 *         "specialEffects": "flash",                                  // 特效：flash闪光特效
 *         "closeButtonPos": 2                                         // 关闭按钮位置：0-底部，1-左侧，2-右侧
 *       }
 *     },
 *     "winType": "unlockFullSplice"                                   // 资源位类型：全屏拼接
 *   }
 * }
 *
 */
public class FullSrcFloatFlexWinImpl extends BaseAbsFullSrcFloatWin implements FullScrActivityStatusCallback {

    // ========================== 常量定义 ==========================

    /** 日志标签，用于调试和错误追踪 */
    private static final String TAG = "Dex_FullSrcFloatWin";

    /** Intent传递参数的键值定义 */
    private static final String EXTRA_KEY_ENUM_CODE = "hades_router_enum_code";                          // 来源枚举代码键
    private static final String EXTRA_KEY_RESOURCE_DATA = "hades_router_resource";                       // 资源数据JSON字符串键
    private static final String EXTRA_KEY_REGISTER_SCREEN_SHOT = "hades_router_register_screen_shot_after_focus"; // 截屏监听注册配置键

    // ========================== 核心数据对象 ==========================

    /** 桌面资源数据，包含所有展示相关的配置信息、图片资源、跳转链接等 */
    private DeskResourceData mDeskResourceData = new DeskResourceData();

    /** 桌面推送来源枚举，标识推送的来源渠道（如通知栏、锁屏等） */
    private DeskSourceEnum mDeskSourceEnum = DeskSourceEnum.OTHER;

    /** 主线程消息处理器，用于处理UI更新和延时任务 */
    private final Handler mHandler = new Handler(Looper.getMainLooper());

    // ========================== 时间记录相关变量 ==========================

    /** 用户开始观看的时间戳（系统绝对时间，毫秒） */
    private long mExposureTime = 0L;

    /** 用户开始观看的时间戳（系统启动时间，毫秒，不受系统时间调整影响） */
    private long mExposureUpTime = 0L;

    /** 浮窗显示时设备是否处于锁屏状态 */
    private boolean mIsLockedWhenShow = true;

    // ========================== 用户行为状态标记 ==========================

    /** 是否已上报关闭事件，防止重复上报 */
    private boolean mReportClose = false;

    /** 是否发生了点击行为，用于区分主动关闭和点击跳转 */
    private boolean mClickAction = false;

    /** 用户行为结束时间（相对于曝光开始时间的毫秒数） */
    private long mActionEndTime = 0L;

    /** 用户行为结束时间（基于系统启动时间计算的相对毫秒数） */
    private long mActionEndUpTime = 0L;

    /** 是否显示产品对话框（外卖挽留弹窗等） */
    private boolean showProductDialog;

    /** 用户是否获得过焦点，即是否真正看到过此浮窗界面 */
    private boolean mHasFocus = false;

    // ========================== UI组件引用 ==========================

    /** 外卖全屏对话框视图，用于显示产品信息和用户操作选项 */
    private WalMaiFullScrDialogView dialogView;

    /** 根布局容器，所有UI元素的父容器 */
    private RelativeLayout rootView;

    /** 中心图片视图，在拆分模式下显示前景图片 */
    private ImageView deskCenterImageView;

    /** 关闭按钮视图，用户点击关闭浮窗的按钮 */
    private ImageView closeImageView;

    /** 全屏容器视图，承载主要内容的框架布局 */
    private FrameLayout fullContainerView;

    /** 反馈圆形按钮视图，高风险用户的反馈入口 */
    private CircularView feedbackView;

    /** 推送设置圆形按钮视图，推送设置入口（预留） */
    private CircularView pushSettingView;

    /** Lottie动画视图，用于显示闪光特效等动画效果 */
    private LottieAnimationView lottieAnimationView;

    // ========================== 业务数据对象 ==========================

    /** 桌面区域信息，包含具体的配置参数和素材数据 */
    private DeskArea<DeliveryDeskMaterial> mDeskInfo;

    /** 投放资源完整信息，包含所有相关的资源和配置数据 */
    private DeliveryResourceInfo mDeliveryResourceInfo;

    // ========================== 功能控制变量 ==========================

    /** 是否在获得焦点后注册截屏监听器，用于控制截屏检测的时机 */
    boolean registerScreenShotAfterFocus = true;

    /** 是否已启动预加载Activity，避免重复启动 */
    private boolean isPreloadActivityStart = false;

    /** 是否已进行预热处理，避免重复预热 */
    private boolean isWarmup = false;

    /** 自动关闭任务的Runnable对象，用于定时关闭浮窗 */
    private Runnable autoCloseRunnable;

    /** 设备解锁广播接收器，监听用户解锁行为 */
    private BroadcastReceiver unlockReceive;

    /** Activity的弱引用，避免内存泄漏同时保持对Activity的访问能力 */
    private WeakReference<Activity> activityRef = new WeakReference<>(null);

    private byte[] backgroundData;//字节流背景图片数据
    private String backgroundImageUrl;//url背景图片数据
    private byte[] fullImageData;//字节流主图片数据
    private String fullImageUrl;//url背景图片数据

    private HashMap<String,Object> dialogData;


    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        // 获取应用全局上下文，避免Activity内存泄漏
        final Context context = HadesUtilsAdapter.getContext();
        // 使用弱引用保存Activity，防止内存泄漏
        activityRef = new WeakReference<>(activity);

        // 在后台线程清理DSP（Dynamic Screen Projection）保留数据，避免影响UI线程
        HadesUtilsAdapter.runOnWorkThread(new Runnable() {
            @Override
            public void run() {
                StorageHelperAdapter.clearDspRetainData(context);
            }
        });

        // 检查屏幕方向：只支持竖屏展示，横屏时直接关闭
        if (!HadesUtilsAdapter.isOrientationPortrait(context)) {
            removeView(DeskCloseTypeEnum.LANDSCAPE.getMessage());
            return;
        }

        // 检查设备状态：只在锁屏或屏幕关闭时展示，如果屏幕亮着且未锁屏则关闭
        if (HadesUtilsAdapter.isScreenOn(context) && !HadesUtilsAdapter.isLocked()) {
            removeView(DeskCloseTypeEnum.SCREEN_SCREEN_ON.getMessage());
            return;
        }

        // 设置Activity的内容视图为全屏浮窗布局
        activity.setContentView(DeskFloatWinViewAdapter.get_hades_full_floatwin());
        // 初始化行为状态标记
        mReportClose = false;                           // 重置关闭上报标记
        mClickAction = false;                           // 重置点击行为标记
        mExposureTime = System.currentTimeMillis();    // 记录曝光开始时间（绝对时间）
        mExposureUpTime = SystemClock.uptimeMillis();   // 记录曝光开始时间（系统运行时间）

        // 获取启动Intent并验证参数有效性
        Intent intent = activity.getIntent();
        if (intent == null) {
            removeView(DeskCloseTypeEnum.PARAM_NULL.getMessage());
            return;
        }

        // 从Intent中提取关键参数
        String resourceDataStr = intent.getStringExtra(EXTRA_KEY_RESOURCE_DATA);           // 资源数据JSON字符串
        int sourceEnumCode = intent.getIntExtra(EXTRA_KEY_ENUM_CODE, -1);                  // 来源枚举代码
        registerScreenShotAfterFocus = intent.getBooleanExtra(EXTRA_KEY_REGISTER_SCREEN_SHOT, true); // 截屏监听配置

        // 解析JSON数据为业务对象
        mDeskResourceData = HadesUtilsAdapter.fromJson(resourceDataStr, DeskResourceData.class);
        mDeskSourceEnum = DeskSourceEnum.getBycode(sourceEnumCode);

        // 验证解析结果，如果关键数据为空则关闭浮窗
        if (mDeskResourceData == null || mDeskSourceEnum == null) {
            removeView(DeskCloseTypeEnum.PARAM_NULL.getMessage());
            return;
        }

        // 初始化投放资源相关数据
        initDeliveryResourceData();
        // 初始化状态栏和导航栏样式（沉浸式效果）
        initStatusBarAndNavigationBar(activity);
        // 标记视图已成功附加，用于全局状态管理
        DeliveryDataManager.sViewAttached = true;
        // 初始化UI界面和各种视图组件
        initView(activity);

        // 检查当前锁屏状态并更新标记
        if (!HadesUtilsAdapter.isLocked()) {
            mIsLockedWhenShow = false;
        }
        // 清理图片资源缓存，释放内存
        DeliveryDataManager.clearImageResource();
        // 注册设备解锁广播接收器，监听用户解锁行为
        registerUnlockReceiver();
    }

    /**
     * 初始化状态栏和导航栏样式配置
     *
     * 根据配置决定是否隐藏导航栏，设置沉浸式全屏效果。
     * 支持两种模式：
     * 1. 完全沉浸式：隐藏状态栏和导航栏，用于最大化内容展示区域
     * 2. 半沉浸式：只隐藏状态栏，保留导航栏，兼顾用户操作习惯
     *
     * @param activity 当前Activity实例，用于获取Window对象
     */
    private void initStatusBarAndNavigationBar(Activity activity) {
        // 只有Android 5.0及以上版本才支持设置状态栏和导航栏样式
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            try {
                Window window = activity.getWindow();

                // 检查配置是否需要隐藏导航栏
                if (DeliveryResourceHelper.isHideNavi(mDeskInfo)) {
                    // 设置完全沉浸式模式：同时隐藏状态栏和导航栏
                    window.getDecorView().setSystemUiVisibility(
                            View.SYSTEM_UI_FLAG_LAYOUT_STABLE          // 保持布局稳定，避免系统栏显示隐藏时布局跳动
                                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION    // 布局延伸到导航栏区域
                                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN        // 布局延伸到状态栏区域
                                    | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION           // 隐藏导航栏
                                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);       // 粘性沉浸模式，用户滑动后自动隐藏系统栏

                    // 设置状态栏和导航栏背景为透明
                    window.setStatusBarColor(ContextCompat.getColor(activity, android.R.color.transparent));
                    window.setNavigationBarColor(android.graphics.Color.TRANSPARENT);
                } else {
                    // 设置半沉浸式模式：只隐藏状态栏，保留导航栏
                    window.getDecorView().setSystemUiVisibility(
                            View.SYSTEM_UI_FLAG_LAYOUT_STABLE          // 保持布局稳定
                                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);      // 布局延伸到状态栏区域

                    // 设置状态栏背景为透明
                    window.setStatusBarColor(ContextCompat.getColor(activity, android.R.color.transparent));
                }
            } catch (Throwable t) {
                // 忽略设置过程中的异常，确保程序不会因为样式设置失败而崩溃
                // 这里不做具体异常处理，因为样式设置失败不应影响核心功能
            }
        }
    }

    /**
     * 注册设备解锁广播接收器
     *
     * 监听系统解锁广播（ACTION_USER_PRESENT），当用户解锁设备时：
     * 1. 触发用户观看逻辑，开始曝光统计
     * 2. 激活自动关闭计时器
     * 3. 显示反馈按钮等交互元素
     *
     * 这个机制确保只有当用户真正能看到浮窗时才开始统计和交互
     */
    private void registerUnlockReceiver() {
        // 如果广播接收器尚未创建，则创建新的接收器实例
        if (unlockReceive == null) {
            unlockReceive = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    // 验证Intent的有效性
                    if (intent == null) {
                        return;
                    }

                    // 监听用户解锁事件，ACTION_USER_PRESENT在用户解锁后触发
                    if (Intent.ACTION_USER_PRESENT.equals(intent.getAction())) {
                        log("Device unlocked");
                        // 触发用户观看逻辑，标记为解锁触发
                        onUserWatch("unlock");
                    }
                }
            };
        }

        // 向系统注册广播接收器，监听解锁事件
        BroadcastReceiverX.register(HadesUtilsAdapter.getContext(), unlockReceive, Intent.ACTION_USER_PRESENT);
    }

    /**
     * 注销设备解锁广播接收器
     *
     * 在Activity销毁时调用，清理资源避免内存泄漏。
     * 如果不及时注销，广播接收器会持续监听系统广播，造成内存泄漏
     */
    private void unregisterUnlockReceiver() {
        if (unlockReceive != null) {
            // 从系统中注销广播接收器
            BroadcastReceiverX.unRegister(HadesUtilsAdapter.getContext(), unlockReceive);
            // 清空引用，帮助垃圾回收器及时回收内存
            unlockReceive = null;
        }
    }

    /**
     * Activity开始生命周期回调方法
     *
     * 此方法在Activity对用户可见但尚未获得焦点时调用。
     * 当前实现为空，因为浮窗的主要逻辑在onActivityCreated和onActivityResumed中处理。
     */
    @Override
    public void onActivityStarted() {
        // 当前无需特殊处理，浮窗的初始化已在onActivityCreated中完成
    }

    /**
     * Activity恢复生命周期回调方法
     *
     * 处理Activity获得焦点后的逻辑，主要包括：
     * 1. 延时检测用户观看状态，确保用户真正看到浮窗
     * 2. 启动预加载优化，提升后续页面加载性能
     * 3. 预热主进程，为页面跳转做准备
     */
    @Override
    public void onActivityResumed() {
        // 检查Activity是否仍然有效
        if (getActiveActivity() == null) {
            return;
        }

        final Context context = HadesUtilsAdapter.getContext();
        // 延迟1秒检查用户观看状态，避免Activity快速切换导致的误判
        HadesUtilsAdapter.runOnMainThreadWithDelay(new Runnable() {
            @Override
            public void run() {
                // 确认屏幕亮着且未锁屏时才触发用户观看逻辑
                if (HadesUtilsAdapter.isScreenOn(context) && !HadesUtilsAdapter.isLocked()) {
                    log("onActivityResumed onUserWatch");
                    onUserWatch("onActivityResumed");
                }
            }
        }, 1000);

        // 处理预加载和性能优化逻辑
        if (mDeskResourceData != null) {
            // 尝试启动Titans透明预加载Activity
            if (QTitansPreloadActivityAdapter.canIUse(context, mDeskResourceData.target)
                    && !isPreloadActivityStart
                    && !showProductDialog) { // 非挽留弹窗才进行预加载
                // 获取推送类型用于预加载标识
                String pushType = mDeskResourceData.deskType == null ? "" : mDeskResourceData.deskType.name();
                // 启动透明预加载Activity，在后台准备目标页面
                QTitansPreloadActivityAdapter.transParentStartIfCan(context, mDeskResourceData.target, mDeskSourceEnum.name(), pushType);
                isPreloadActivityStart = true;
                return;
            }

            // 如果不能使用Titans预加载，则尝试主进程预热
            if (HadesUtilsAdapter.enablePreloadWarmUpMainProcess()
                    && !isWarmup
                    && !isPreloadActivityStart) {
                // 预热主进程，提升后续页面启动速度
                QPreloadJobServiceAdapter.warmupMainProcess(context, mDeskResourceData.target);
                isWarmup = true;
            }
        }
    }

    /**
     * Activity暂停生命周期回调方法
     *
     * 当Activity失去焦点但仍可见时调用。
     * 对于全屏浮窗，此场景较少出现，当前无需特殊处理。
     */
    @Override
    public void onActivityPaused() {
        // 当前无需特殊处理
    }

    /**
     * Activity停止生命周期回调方法
     *
     * 当Activity完全不可见时调用。对于浮窗来说，通常表示：
     * 1. 用户按Home键返回桌面
     * 2. 用户启动其他应用
     * 3. 系统回收Activity
     *
     * 如果用户曾经看到过浮窗（mHasFocus=true），则记录为用户主动关闭
     */
    @Override
    public void onActivityStopped() {
        log("onStop");
        Activity activity = getActiveActivity();
        if (activity == null) {
            return;
        }

        try {
            // 如果用户曾经获得过焦点（真正看到了浮窗），则记录为用户关闭行为
            if (mHasFocus) {
                removeView(DeskCloseTypeEnum.USER_CLOSE.getMessage());
            }
        } catch (Throwable t) {
            // 发生异常时直接结束Activity，避免异常状态
            activity.finish();
        }
    }

    /**
     * Activity销毁生命周期回调方法
     *
     * Activity被销毁时的最终清理工作：
     * 1. 注销广播接收器，避免内存泄漏
     * 2. 清理资源并上报关闭事件
     * 3. 确保所有引用被正确释放
     */
    @Override
    public void onActivityDestroyed() {
        log("onDestroy");
        if (getActiveActivity() == null) {
            return;
        }

        // 注销解锁广播接收器，避免内存泄漏
        unregisterUnlockReceiver();
        // 清理视图资源并上报关闭事件
        removeView(DeskCloseTypeEnum.USER_CLOSE.getMessage());
    }

    /**
     * 窗口焦点变化回调方法
     *
     * 当Activity窗口获得或失去焦点时调用。获得焦点时触发用户观看逻辑：
     * 1. 开始曝光统计
     * 2. 显示反馈按钮
     * 3. 启动自动关闭计时器
     *
     * @param hasFocus true表示获得焦点，false表示失去焦点
     */
    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        log("onWindowFocusChanged hasFocus = " + hasFocus);
        if (getActiveActivity() == null) {
            return;
        }

        // 只有获得焦点时才触发用户观看逻辑
        if (hasFocus) {
            log("onWindowFocusChanged triggerAutoClose");
            onUserWatch("onWindowFocusChanged");
        }
    }

    /**
     * 返回键按下回调方法
     *
     * 处理用户按下返回键的行为：
     * 1. 如果是产品对话框模式，执行左侧按钮的跳转逻辑
     * 2. 否则直接关闭浮窗
     *
     * 返回键通常被视为用户的取消或退出操作
     */
    @Override
    public void onBackPressed() {
        Activity activity = getActiveActivity();
        if (activity == null) {
            return;
        }

        // 如果存在产品信息（如外卖挽留弹窗），执行左侧按钮逻辑
        if (mDeskResourceData != null && mDeskResourceData.fullActProductInfo != null) {
            pushClick(mDeskResourceData.fullActProductInfo.leftTarget, mDeskResourceData.fullActProductInfo.leftTargetBack, "back");
        } else {
            // 普通浮窗直接关闭
            removeView("back");
        }
    }

    /**
     * 截屏事件回调方法
     *
     * 当检测到用户截屏行为时调用，处理截屏后的反馈逻辑：
     * 1. 发送广播取消相关通知
     * 2. 根据配置决定是否显示截屏反馈界面
     * 3. 如果不支持截屏反馈，直接关闭浮窗
     *
     * 截屏反馈是用户体验优化的重要组成部分，让用户可以快速反馈问题
     */
    @Override
    public void onScreenShot() {
        // 发送广播取消相关通知，避免通知栏冗余
        sendBroadcastToCancelNf();

        Activity activity = getActiveActivity();
        if (activity == null) {
            return;
        }

        // 检查是否启用了桌面截屏反馈功能
        if (FeedbackExtensions.isDeskScreenShot(mDeskResourceData)) {
            try {
                // 显示截屏反馈界面，让用户选择反馈类型
                showScreenShotView(activity, rootView);
            } catch (Throwable t) {
                // 显示反馈界面失败，直接关闭浮窗
                removeView(DeskCloseTypeEnum.USER_SCREEN_SHOT.getMessage());
            }
        } else {
            // 未启用截屏反馈，直接关闭浮窗
            removeView(DeskCloseTypeEnum.USER_SCREEN_SHOT.getMessage());
        }
    }

    /**
     * 初始化投放资源数据
     *
     * 根据sessionId从缓存中获取完整的投放资源信息：
     * 1. 从缓存映射中获取DeliveryResourceInfo对象
     * 2. 提取桌面区域配置信息
     *
     * 这个方法确保所有相关的资源数据都能被正确加载和使用
     */
    private void initDeliveryResourceData() {
        // 检查sessionId是否有效，sessionId是资源数据的唯一标识
        if (!TextUtils.isEmpty(mDeskResourceData.sessionId)) {
            // 从全局缓存中获取完整的投放资源信息
            mDeliveryResourceInfo = DeliveryResourceDataCache.deliveryResourceInfoMap.get(mDeskResourceData.sessionId);

            // 提取桌面区域配置信息，包含展示相关的所有配置参数
            mDeskInfo = mDeliveryResourceInfo.deskInfo;
        }
    }

    /**
     * 用户观看逻辑处理方法
     *
     * 当用户真正能看到浮窗时触发，负责：
     * 1. 检查曝光是否过期
     * 2. 开始曝光统计和数据上报
     * 3. 显示交互元素（反馈按钮等）
     * 4. 启动自动关闭计时器
     * 5. 注册截屏监听器
     *
     * @param from 触发来源标识，用于日志追踪（如"unlock"、"onWindowFocusChanged"等）
     */
    private void onUserWatch(String from) {
        // 只有首次获得焦点时才执行逻辑，避免重复处理
        if (!mHasFocus) {
            log("onUserWatch-" + from);
            // 标记用户已经看到浮窗
            mHasFocus = true;

            // 检查曝光过期时间，防止过期展示
            long exposureExpireTime = mDeskResourceData != null && mDeskResourceData.sceneParam != null ?
                    mDeskResourceData.sceneParam.exposureExpireTime : -1;
            if (exposureExpireTime > 0 && System.currentTimeMillis() > exposureExpireTime) {
                // 曝光已过期，直接关闭浮窗
                removeView("TIME_OUT");
                return;
            }

            // 重新记录真实的曝光开始时间（用户实际看到的时间）
            mExposureTime = System.currentTimeMillis();
            mExposureUpTime = SystemClock.uptimeMillis();

            // 上报曝光采样事件，用于数据分析
            DexDeliveryReporter.reportPushStageSample(mDeskResourceData, ReportParamsKey.STAGE.STAGE_EXPOSURE_SAMPLE, mDeskSourceEnum);

            // 显示反馈按钮，让用户可以进行反馈操作
            if (feedbackView != null) {
                feedbackView.onShow();
            }

            // 显示推送设置按钮（如果存在）
            if (pushSettingView != null) {
                pushSettingView.onShow();
            }

            // 启动自动关闭计时器，防止浮窗长时间停留
            triggerAutoClose();

            // 根据配置决定是否注册截屏监听器
            if (registerScreenShotAfterFocus) {
                // 注册截屏监听器，检测用户截屏行为
                registerScreenShotListener(ScreenShotManager.ScreenShotEnum.DESK,
                        mDeskResourceData, mDeskSourceEnum, FeedbackExtensions.SCENE_DESK_PUSH);
            }
        }
    }

    /**
     * 启动自动关闭计时器
     *
     * 为防止浮窗长时间停留影响用户体验，设置自动关闭机制：
     * 1. 检查是否已存在计时器，避免重复创建
     * 2. 根据配置获取关闭延时时间
     * 3. 创建延时任务并提交到主线程Handler
     */
    private void triggerAutoClose() {
        // 如果自动关闭任务已存在，不重复创建
        if (autoCloseRunnable != null) {
            return;
        }

        // 获取自动关闭延时时间（毫秒），默认10秒
        long delayMillis = getAutoCloseTime(mDeskResourceData);

        // 创建自动关闭任务
        autoCloseRunnable = new Runnable() {
            @Override
            public void run() {
                // 触发自动关闭，标记为超时关闭
                removeView(DeskCloseTypeEnum.AUTO_CLOSE.getMessage());
            }
        };

        // 将任务提交到主线程Handler，延时执行
        mHandler.postDelayed(autoCloseRunnable, delayMillis);
    }

    /**
     * 发送关闭通知栏的广播
     *
     * 当检测到截屏行为时，发送广播通知系统取消相关通知：
     * 1. 避免通知栏出现多余的截屏相关通知
     * 2. 统一管理截屏后的通知状态
     * 3. 提供更好的用户体验
     */
    private void sendBroadcastToCancelNf() {
        try {
            // 创建取消通知的Intent
            Intent intent = new Intent(HadesNotificationKey.MANAGER.CANCEL_ACTION);
            // 设置取消类型为桌面截屏类型
            intent.putExtra(HadesNotificationKey.MANAGER.CANCEL_TYPE, HadesNotificationKey.MANAGER.CANCEL_TYPE_DESK_SHOT);
            // 发送广播给系统或其他监听组件
            HadesUtilsAdapter.getContext().sendBroadcast(intent);
        } catch (Throwable t) {
            // 忽略广播发送过程中的异常，不影响主要流程
        }
    }

    /**
     * 初始化UI界面和各种视图组件
     */
    private void initView(final Activity activity) {
        // 获取应用上下文，避免Activity引用导致的内存泄漏
        final Context context = activity.getApplicationContext();
        // 判断是否需要显示产品对话框（基于是否有产品信息）
        showProductDialog = (mDeskResourceData.fullActProductInfo != null);
        // 获取布局中的主容器视图
        fullContainerView = activity.findViewById(DeskFloatWinViewAdapter.get_full_container_view());
        rootView = activity.findViewById(DeskFloatWinViewAdapter.get_full_root_layout());
        // 检查根视图是否成功获取，如果为空则说明布局加载失败
        if (rootView == null) {
            removeView("ROOT_VIEW_NULL");
            return;
        }

        // 条件：非产品对话框 && 拆分模式 && 全屏容器存在 && 属于bottom布局
        if (!showProductDialog
                && isCombinationMode()
                && fullContainerView != null
                && isForegroundShowBottom()){
            //限定拼接+bottom模式，进入布局创建
            log("initFlexBottomView 1");
            //初始化动态布局并加入
            initFlexBottomImage(context,fullContainerView);
        } else {
            // 不符合对应参数，资源配置有误
            removeView("resource_error");
            return;
        }

        // 显示反馈按钮（高风险用户）
        showFeedbackView(context, rootView);
    }



    /**
     * 初始化flex底部图片显示逻辑（拆分模式-前景图在底部）
     */
    private void initFlexBottomImage(final Context context,final FrameLayout fullContainerView) {
        // 创建底部图片视图，动态添加到容器中
        final ImageView bottomImageView = new ImageView(context);
        // 设置图片视图自动调整边界，保持宽高比
        bottomImageView.setAdjustViewBounds(true);

        // 配置布局参数：宽度充满父容器，高度自适应，位置在底部
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.BOTTOM;  // 设置重力为底部对齐

        // 将底部图片视图添加到全屏容器中
        fullContainerView.addView(bottomImageView, params);


        //组装flex数据
        initFlexData();
        // 尝试从缓存中获取背景图片数据（字节数组格式）
        backgroundData = DeliveryDataManager
                .getImageResource(mDeskResourceData.imageIdMap.get(DeskResourceData.BACKGROUND_ID));
        backgroundImageUrl = mDeskResourceData.background;
        //主图
        fullImageData = DeliveryDataManager
                .getImageResource(mDeskResourceData.imageIdMap.get(DeskResourceData.FULL_IMAGE_ID));
        fullImageUrl = mDeskResourceData.image;

        if(backgroundData == null){
            backgroundData = backgroundImageUrl.getBytes();
        }
        if(fullImageData == null){
            fullImageData = fullImageUrl.getBytes();
        }

        if (mDeskResourceData != null
                && mDeskResourceData.bottomFloatingInfo != null
                && mDeskResourceData.bottomFloatingInfo.get("templateUrl") != null){
            //添加背景图数据
            initFlexDialogView(context,fullContainerView, backgroundData,fullImageData);
            //initFlexDialogView(context,fullContainerView, fullImageData);
        }



    }
    private void initFlexData(){
        //组装flex数据


    }

    private void initFlexDialogView(final Context context,final FrameLayout dialogContainerView, final byte[] backgroundData , final  byte[] fullImageData){
        //进行
        QQFlexEvent event_background  = new QQFlexEvent();
        event_background.action = "imageParseReady";
        event_background.imageData = backgroundData;
        DeskFloatWinViewAdapter.sendEvent(dialogContainerView.getContext(), event_background);

        QQFlexEvent event_full  = new QQFlexEvent();
        event_full.action = "imageParseReady";
        event_full.imageData = fullImageData;
        DeskFloatWinViewAdapter.sendEvent(dialogContainerView.getContext(), event_full);


        DeskFloatWinViewAdapter.addBottomDialogContent(dialogContainerView, dialogData, new IFlexViewClickEventListenerAdapter() {
            @Override
            public void handleClickEvent(String action, Map<String, Object> data) {

            }
        }, new IFlexViewShowStatusListenerAdapter() {
            @Override
            public void onShow() {

            }

            @Override
            public void onFailed() {

            }
        });



    }

    /**
     * 调整底部关闭按钮的位置和布局参数
     *
     * 根据关闭按钮位置配置和底部图片高度，动态调整关闭按钮的位置：
     * 1. 右侧位置：按钮位于图片右侧，距离图片顶部一定距离
     * 2. 左侧位置：按钮位于图片左侧，距离图片底部一定距离
     *
     * @param context 应用上下文，用于像素转换
     * @param closeButtonPos 关闭按钮位置枚举
     * @param height 底部图片的实际高度（像素）
     */
    private void showBottomCloseView(Context context, CloseButtonPosEnum closeButtonPos, int height) {
        // 检查关闭按钮视图是否存在
        if (closeImageView == null) {
            return;
        }

        // 获取当前的布局参数
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) closeImageView.getLayoutParams();

        if (closeButtonPos == CloseButtonPosEnum.RIGHT) {
            // 右侧关闭按钮：移除水平居中，设置为右对齐
            params.removeRule(RelativeLayout.CENTER_HORIZONTAL);
            params.addRule(RelativeLayout.ALIGN_PARENT_END);
            // 设置底部边距：图片高度减去按钮大小的一半（34dp），使按钮位于图片顶部附近
            params.bottomMargin = height - UiUtilsAdapter.pixelOfDp(context, 34);
            // 设置右侧边距为10dp
            params.rightMargin = UiUtilsAdapter.pixelOfDp(context, 10);
            closeImageView.setLayoutParams(params);
        } else {
            // 左侧关闭按钮：移除水平居中和右对齐，设置为左对齐
            params.removeRule(RelativeLayout.CENTER_HORIZONTAL);
            params.removeRule(RelativeLayout.ALIGN_PARENT_END);
            params.addRule(RelativeLayout.ALIGN_PARENT_START);
            // 设置底部边距：图片高度加上10dp间距，使按钮位于图片下方
            params.bottomMargin = height + UiUtilsAdapter.pixelOfDp(context, 10);
            // 设置左侧边距为10dp
            params.leftMargin = UiUtilsAdapter.pixelOfDp(context, 10);
            closeImageView.setLayoutParams(params);
        }
    }

    /**
     * 检查是否需要显示闪光特效
     *
     * 通过配置信息判断是否启用闪光动画效果
     *
     * @return true表示需要显示闪光特效，false表示不显示
     */
    private boolean isShowFlash() {
        return TextUtils.equals("flash",  DeliveryResourceHelper.getSpecialEffects(mDeskInfo));
    }

    /**
     * 检查前景图是否应该显示在底部
     *
     * 通过配置信息判断前景图的显示位置
     *
     * @return true表示前景图显示在底部，false表示显示在中心
     */
    private boolean isForegroundShowBottom() {
        return TextUtils.equals("bottom",  DeliveryResourceHelper.getForegroundPosition(mDeskInfo));
    }

    /**
     * 显示闪光特效Lottie动画视图
     *
     * 根据配置在中心图片上添加闪光动画效果：
     * 1. 检查是否启用闪光特效配置
     * 2. 验证必要的视图组件存在
     * 3. 等待图片布局完成后显示动画
     *
     * @param rootLayout 根布局容器，用于添加Lottie动画视图
     */
    private void showFlashLottieView(final ViewGroup rootLayout) {
        try {
            // 检查是否启用闪光特效，如果未启用则直接返回
            if (!isShowFlash()) {
                return;
            }
            // 检查根布局容器是否存在
            if (rootLayout == null) {
                return;
            }
            // 检查中心图片视图是否存在且可见
            if (deskCenterImageView == null || deskCenterImageView.getVisibility() == View.GONE) {
                return;
            }
            // 获取当前Activity实例
            final Activity activity = getActiveActivity();
            if (activity == null) {
                return;
            }

            // 获取中心图片的实际高度，用于设置动画视图尺寸
            int height = deskCenterImageView.getHeight();
            if (height > 0) {
                // 如果高度已确定，直接显示Lottie动画
                realShowLottie(activity, rootLayout);
                return;
            }

            // 如果高度未确定，监听布局完成事件
            ViewTreeObserver viewTreeObserver = deskCenterImageView.getViewTreeObserver();
            viewTreeObserver.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    // 移除监听器，避免多次回调导致重复处理
                    deskCenterImageView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    // 布局完成后显示Lottie动画
                    realShowLottie(activity, rootLayout);
                }
            });
        } catch (Throwable t) {
            // 记录异常信息，但不影响主流程
            LogUtils.d(TAG, t.getMessage());
        }
    }

    /**
     * 实际创建和显示Lottie闪光动画
     *
     * 在确定了中心图片尺寸后，创建Lottie动画视图并播放闪光效果：
     * 1. 创建LottieAnimationView实例
     * 2. 设置动画视图的布局参数和显示属性
     * 3. 从远程URL加载Lottie动画资源并播放
     *
     * @param activity 当前Activity实例，用于创建视图
     * @param rootLayout 根布局容器，用于添加动画视图
     */
    private void realShowLottie(Activity activity, final ViewGroup rootLayout) {
        // 创建Lottie动画视图实例
        lottieAnimationView = new LottieAnimationView(activity);
        // 初始设置为不可见，等动画开始后再显示
        lottieAnimationView.setVisibility(View.GONE);
        // 设置图片缩放类型为居中裁剪，保证动画效果填充整个区域
        lottieAnimationView.setScaleType(ImageView.ScaleType.CENTER_CROP);

        // 获取中心图片的高度，用于设置动画视图的高度
        int height = deskCenterImageView.getHeight();
        // 创建布局参数：宽度充满父容器，高度与中心图片一致
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                RelativeLayout.LayoutParams.MATCH_PARENT,
                height);
        // 设置动画视图在容器中居中显示
        params.gravity = Gravity.CENTER;
        // 将Lottie动画视图添加到根布局容器中
        rootLayout.addView(lottieAnimationView, params);

        // 设置动画为无限循环播放
        lottieAnimationView.setRepeatCount(LottieDrawable.INFINITE);
        // 从远程URL加载Lottie动画资源并播放闪光特效
        LottieUtilsAdapter.playRemoteLottieAnimation(lottieAnimationView, activity,
                "https://s3plus.meituan.net/v1/mss_1ada830d56584ddeae1b0899c231c552/goku/lottie/data1735097534338.json",
                new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                        super.onAnimationStart(animation);
                        // 动画开始播放时，将动画视图设置为可见
                        lottieAnimationView.setVisibility(View.VISIBLE);
                    }
                });
    }

    /**
     * 显示截屏反馈界面
     *
     * 当用户截屏后显示反馈选项界面，包含多种反馈操作：
     * 1. 创建截屏反馈视图并设置各种点击回调
     * 2. 设置视图布局参数，显示在屏幕右侧中间
     * 3. 取消自动关闭计时器，设置截屏反馈的自动关闭时间
     *
     * @param activity 当前Activity实例，用于页面跳转
     * @param rootLayout 根布局容器，用于添加截屏反馈视图
     */
    private void showScreenShotView(final Activity activity, final ViewGroup rootLayout) {
        // 检查根布局容器是否存在
        if (rootLayout == null) {
            return;
        }

        // 获取应用上下文
        Context context = HadesUtilsAdapter.getContext();
        // 创建截屏反馈视图，设置各种点击回调监听器
        ScreenShotView screenShotView = new ScreenShotView(context, activity, mDeskResourceData, new ScreenShotView.SsViewOnClick() {
            @Override
            public void onClickFeedback() {
                // 用户点击反馈按钮，跳转到反馈页面
                FeedbackHelper.jumpToFeedbackPage(activity.getApplicationContext(),
                        mDeskResourceData.target, mDeskResourceData.marketingType,
                        mDeskResourceData.sessionId, mDeskResourceData.pushResId,
                        FeedbackExtensions.getPsText(mDeskResourceData),
                        FeedbackExtensions.getDeskRiskLevel(mDeskResourceData), true);
                // 关闭浮窗，标记为点击反馈关闭
                removeView("CLICK_FEED_BACK_SS");
            }

            @Override
            public void onClickNoDisturb() {
                // 用户点击免打扰按钮，跳转到免打扰设置页面
                FeedbackHelper.jumpToSilenceSettingActivity(activity.getApplicationContext(),
                        mDeskResourceData.target, mDeskResourceData.marketingType,
                        mDeskResourceData.sessionId, mDeskResourceData.pushResId,
                        FeedbackExtensions.getPsText(mDeskResourceData), true, true);
                // 关闭浮窗，标记为点击免打扰关闭
                removeView("CLICK_NO_DISTURB_SS");
            }

            @Override
            public void onClickPushSetting() {
                // 用户点击推送设置按钮，跳转到推送设置页面
                FeedbackHelper.jumpToSilenceSettingActivity(activity.getApplicationContext(),
                        mDeskResourceData.target, mDeskResourceData.marketingType,
                        mDeskResourceData.sessionId, mDeskResourceData.pushResId,
                        FeedbackExtensions.getPsText(mDeskResourceData), true, false);
                // 关闭浮窗，标记为点击设置关闭
                removeView("CLICK_SETTING_SS");
            }

            @Override
            public void onClickClose(ScreenShotView ssView) {
                // 用户点击关闭按钮，隐藏截屏反馈视图
                ssView.setVisibility(View.GONE);
                // 从根布局中移除截屏反馈视图
                rootLayout.removeView(ssView);
            }
        });

        // 设置截屏反馈视图的布局参数
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
                RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        // 设置右侧边距为10dp
        params.rightMargin = UiUtilsAdapter.pixelOfDp(context, 10);

        // 设置布局规则，使其位于右侧中间
        params.addRule(RelativeLayout.CENTER_VERTICAL);     // 垂直居中
        params.addRule(RelativeLayout.ALIGN_PARENT_END);    // 对齐到父容器右侧
        // 将截屏反馈视图添加到根布局中
        rootLayout.addView(screenShotView, params);

        // 取消原有的自动关闭计时器，因为截屏反馈有自己的关闭时间
        if (autoCloseRunnable != null) {
            mHandler.removeCallbacks(autoCloseRunnable);
        }

        // 获取截屏反馈的自动关闭时间配置（秒）
        int closeTime = FeedbackExtensions.getDeskScreenShotCloseTime(mDeskResourceData);
        // 设置截屏反馈界面的自动关闭计时器
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                // 超时自动关闭截屏反馈界面
                removeView("SS_AUTO_CLOSE");
            }
        }, closeTime * 1000);  // 转换为毫秒
    }

    /**
     * 显示反馈按钮视图（高风险和中风险用户）
     *
     * 根据用户风险等级显示反馈入口按钮：
     * 1. 检查根布局容器是否存在
     * 2. 验证用户是否为高风险或中风险用户
     * 3. 创建圆形反馈按钮并设置布局参数
     * 4. 将按钮添加到屏幕右下角位置
     *
     * @param context 应用上下文，用于创建视图和像素转换
     * @param rootLayout 根布局容器，用于添加反馈按钮视图
     */
    private void showFeedbackView(final Context context, ViewGroup rootLayout) {
        // 检查根布局容器是否存在
        if (rootLayout == null) {
            return;
        }
        // 检查用户是否为高风险或中风险用户，只有这两类用户才显示反馈按钮
        if (!FeedbackExtensions.isDeskHighRiskUser(mDeskResourceData) && !FeedbackExtensions.isDeskMiddleRiskUser(mDeskResourceData)) {
            return;
        }

        // 创建圆形反馈按钮视图
        feedbackView = new CircularView(context, mDeskResourceData, CircularView.TYPE_FEEDBACK, new CircularView.OnClickCallback() {
            @Override
            public void onClick() {
                // 用户点击反馈按钮时关闭浮窗
                removeView("CLICK_FEED_BACK");
            }
        });

        // 设置反馈按钮的布局参数（48dp x 48dp的圆形按钮）
        RelativeLayout.LayoutParams firstParams = new RelativeLayout.LayoutParams(
                UiUtilsAdapter.pixelOfDp(context, 48),  // 宽度48dp
                UiUtilsAdapter.pixelOfDp(context, 48)); // 高度48dp

        // 设置布局规则：对齐到父容器底部和右侧
        firstParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        firstParams.addRule(RelativeLayout.ALIGN_PARENT_END);

        // 设置底部边距：底部47dp + 预留空间48dp + 间距12dp = 107dp
        firstParams.bottomMargin = UiUtilsAdapter.pixelOfDp(context, 47 + 48 + 12);
        // 设置右侧边距为10dp
        firstParams.rightMargin = UiUtilsAdapter.pixelOfDp(context, 10);

        // 将反馈按钮添加到根布局中
        rootLayout.addView(feedbackView, firstParams);
    }

    /**
     * 初始化产品对话框的交互逻辑和监听器
     *
     * 为外卖全屏对话框设置各种用户交互回调，处理不同场景下的用户操作：
     * 1. 异常处理 - 对话框初始化失败时关闭浮窗
     * 2. 左侧按钮 - 通常为取消或返回操作
     * 3. 右侧按钮 - 通常为确认或跳转操作
     * 4. 关闭按钮 - X号关闭按钮，执行左侧按钮逻辑
     * 5. 遮罩点击 - 根据不同业务类型执行相应操作
     * 6. 内容点击 - 点击对话框内容区域跳转
     * 7. 倒计时结束 - 订单过期处理
     *
     * @param context 应用上下文，用于状态检查和页面跳转
     */
    private void initDialogView(final Context context) {
        // 初始化对话框并设置各种交互监听器
        dialogView.init(mDeskResourceData, new WalMaiFullScrDialogView.FullSrcDialogListener() {
            @Override
            public void onError() {
                // 对话框初始化或运行过程中发生错误，关闭浮窗
                removeView(DeskCloseTypeEnum.EXCEPTION_CLOSE.getMessage());
            }

            @Override
            public void onClickLeft() {
                // 用户点击左侧按钮（通常为取消/返回操作）
                // 隐藏对话框但不关闭整个浮窗
                dialogView.setVisibility(View.GONE);
                // 检查产品信息是否存在，执行左侧按钮的跳转逻辑
                if (mDeskResourceData != null && mDeskResourceData.fullActProductInfo != null) {
                    pushClick(mDeskResourceData.fullActProductInfo.leftTarget, mDeskResourceData.fullActProductInfo.leftTargetBack, "leftButton");
                }
            }

            @Override
            public void onClickRight() {
                // 用户点击右侧按钮（通常为确认/跳转操作）
                // 检查产品信息是否存在，执行右侧按钮的跳转逻辑
                if (mDeskResourceData != null && mDeskResourceData.fullActProductInfo != null) {
                    pushClick(mDeskResourceData.fullActProductInfo.rightTarget,  mDeskResourceData.fullActProductInfo.rightTargetBack, "rightButton");
                }
            }

            @Override
            public void onClickCloseX() {
                // 用户点击X关闭按钮，通常等同于取消操作
                // 隐藏对话框但不关闭整个浮窗
                dialogView.setVisibility(View.GONE);
                // 检查产品信息是否存在，执行左侧按钮（取消）的跳转逻辑
                if (mDeskResourceData != null && mDeskResourceData.fullActProductInfo != null) {
                    pushClick(mDeskResourceData.fullActProductInfo.leftTarget, mDeskResourceData.fullActProductInfo.leftTargetBack, "closeX");
                }
            }

            @Override
            public void onClickMask() {
                // 用户点击对话框外的遮罩区域
                // 隐藏对话框但不关闭整个浮窗
                dialogView.setVisibility(View.GONE);
                // 检查产品信息是否存在
                if (mDeskResourceData != null && mDeskResourceData.fullActProductInfo != null) {
                    // 根据不同的业务类型执行相应的跳转逻辑
                    if (mDeskResourceData.fullActProductInfo.actionType == FullActProductInfo.ACTION_TYPE_PAY
                            || mDeskResourceData.fullActProductInfo.actionType == FullActProductInfo.ACTION_TYPE_SHOP) {
                        // 支付类型和购物类型：点击遮罩执行左侧（取消）逻辑
                        pushClick(mDeskResourceData.fullActProductInfo.leftTarget, mDeskResourceData.fullActProductInfo.leftTargetBack, "mask");
                    } else if (mDeskResourceData.fullActProductInfo.actionType == FullActProductInfo.ACTION_TYPE_BROWSE) {
                        // 浏览类型：点击遮罩执行右侧（确认）逻辑
                        pushClick(mDeskResourceData.fullActProductInfo.rightTarget, mDeskResourceData.fullActProductInfo.rightTargetBack, "mask");
                    }
                }
            }

            @Override
            public void onClickContent() {
                // 用户点击对话框内容区域，通常执行确认/跳转操作
                // 检查产品信息是否存在，执行右侧按钮的跳转逻辑
                if (mDeskResourceData != null && mDeskResourceData.fullActProductInfo != null) {
                    pushClick(mDeskResourceData.fullActProductInfo.rightTarget, mDeskResourceData.fullActProductInfo.rightTargetBack, "content");
                }
            }

            @Override
            public void onCountDownFinish() {
                // 倒计时结束回调，通常用于订单过期处理
                // 检查设备状态：如果屏幕关闭或处于锁屏状态，认为订单已无效
                if (!HadesUtilsAdapter.isScreenOn(context) || HadesUtilsAdapter.isLocked()) {
                    // 订单已过期无效，关闭浮窗
                    removeView(DeskCloseTypeEnum.ORDER_INVALID.getMessage());
                }
            }
        });
    }

    /**
     * 加载中心图片并在成功后显示闪光特效
     *
     * 为拆分模式的前景图片加载提供统一处理，支持字节数组和URL两种数据源：
     * 1. 创建加载成功的回调，触发闪光特效显示
     * 2. 根据图片数据类型选择合适的加载方法
     * 3. 加载完成后自动显示Lottie闪光动画
     *
     * @param context 应用上下文，用于图片加载
     * @param image 图片数据，可能是byte[]或String类型
     */
    private void loadCenterImage(final Context context, final Object image) {
        // 创建图片加载成功的回调，用于显示闪光特效
        LoadResult loadResult = new LoadResult() {
            @Override
            public void onSuccess() {
                // 图片加载成功后，显示闪光特效动画
                showFlashLottieView(fullContainerView);
            }
        };

        // 根据图片数据类型选择对应的加载方法
        if (image instanceof byte[]) {
            // 字节数组数据加载：不需要模糊，不需要居中裁剪
            loadImage(context, (byte[]) image, deskCenterImageView, false, false, loadResult);
        } else if (image instanceof String) {
            // URL字符串数据加载：不需要模糊，不需要居中裁剪
            loadImage(context, (String) image, deskCenterImageView, false, false, loadResult);
        }
    }

    /**
     * 加载底部图片的统一处理方法
     *
     * 为底部前景图片加载提供统一接口，支持多种数据源：
     * 1. 检查图片数据类型
     * 2. 调用对应的图片加载方法
     * 3. 执行加载完成回调
     *
     * @param context 应用上下文，用于图片加载
     * @param view 目标图片视图
     * @param image 图片数据，可能是byte[]或String类型
     * @param loadResult 加载完成回调
     */
    private void loadBottomImage(final Context context, final ImageView view, final Object image, final LoadResult loadResult) {
        // 根据图片数据类型选择对应的加载方法
        if (image instanceof byte[]) {
            // 字节数组数据加载：不需要模糊，不需要居中裁剪
            loadImage(context, (byte[]) image, view, false, false, loadResult);
        } else if (image instanceof String) {
            // URL字符串数据加载：不需要模糊，不需要居中裁剪
            loadImage(context, (String) image, view, false, false, loadResult);
        }
    }

    /**
     * 加载字节数组图片（简化版本，无加载结果回调）
     *
     * @param context 应用上下文
     * @param imageData 图片字节数组数据
     * @param view 目标ImageView
     * @param needBlurTrans 是否需要模糊变换
     * @param needCenterCrop 是否需要居中裁剪
     */
    private void loadImage(final Context context, final byte[] imageData, final ImageView view,
                           final boolean needBlurTrans, final boolean needCenterCrop) {
        // 调用完整版本的loadImage方法，不传入加载结果回调
        loadImage(context, imageData, view, needBlurTrans, needCenterCrop, null);
    }

    /**
     * 加载URL图片（简化版本，无加载结果回调）
     *
     * @param context 应用上下文
     * @param imageUrl 图片URL地址
     * @param view 目标ImageView
     * @param needBlurTrans 是否需要模糊变换
     * @param needCenterCrop 是否需要居中裁剪
     */
    private void loadImage(final Context context, final String imageUrl, final ImageView view,
                           final boolean needBlurTrans, final boolean needCenterCrop) {
        // 调用完整版本的loadImage方法，不传入加载结果回调
        loadImage(context, imageUrl, view, needBlurTrans, needCenterCrop, null);
    }

    /**
     * 加载字节数组图片（完整版本，带加载结果回调）
     *
     * 使用Picasso加载字节数组格式的图片到ImageView中：
     * 1. 切换到主线程执行UI操作
     * 2. 配置图片变换选项（模糊、裁剪）
     * 3. 处理加载成功和失败的回调
     *
     * @param context 应用上下文，用于图片加载
     * @param imageData 图片字节数组数据
     * @param view 目标ImageView
     * @param needBlurTrans 是否需要应用模糊变换效果
     * @param needCenterCrop 是否需要居中裁剪适配
     * @param loadResult 加载完成回调接口
     */
    private void loadImage(final Context context, final byte[] imageData, final ImageView view,
                           final boolean needBlurTrans, final boolean needCenterCrop, final LoadResult loadResult) {
        // 确保图片加载在主线程执行，避免UI操作异常
        HadesUtilsAdapter.runOnMainThread(new Runnable() {
            @Override
            public void run() {
                try {
                    // 使用Picasso适配器加载全屏图片，支持模糊和裁剪变换
                    PicassoAdapter.loadFullImage(context, imageData, view, needBlurTrans, needCenterCrop,
                            new PicassoAdapter.CallbackAdapter() {
                                @Override
                                public void onSuccess() {
                                    // 图片加载成功，执行回调通知
                                    if (loadResult != null) {
                                        loadResult.onSuccess();
                                    }
                                    // 记录成功日志
                                    log("image byte load success");
                                }

                                @Override
                                public void onError() {
                                    // 图片加载失败，记录错误日志
                                    log("image byte load error");
                                    // 关闭浮窗，标记为图片错误
                                    removeView(DeskCloseTypeEnum.IMAGE_ERROR.getMessage());
                                }
                            });
                } catch (Throwable t) {
                    // 加载过程中发生异常，记录异常信息
                    log("loadImageByte: " + t.getMessage());
                    // 关闭浮窗，标记为图片错误
                    removeView(DeskCloseTypeEnum.IMAGE_ERROR.getMessage());
                }
            }
        });
    }

    /**
     * 加载URL图片（完整版本，带加载结果回调）
     *
     * 使用Picasso加载URL格式的图片到ImageView中：
     * 1. 切换到主线程执行UI操作
     * 2. 验证参数有效性
     * 3. 配置图片变换选项并处理回调
     *
     * @param context 应用上下文，用于图片加载
     * @param imageUrl 图片URL地址，不能为空
     * @param view 目标ImageView，不能为null
     * @param needBlurTrans 是否需要应用模糊变换效果
     * @param needCenterCrop 是否需要居中裁剪适配
     * @param loadResult 加载完成回调接口
     */
    private void loadImage(final Context context, final String imageUrl, final ImageView view,
                           final boolean needBlurTrans, final boolean needCenterCrop, final LoadResult loadResult) {
        // 确保图片加载在主线程执行，避免UI操作异常
        HadesUtilsAdapter.runOnMainThread(new Runnable() {
            @Override
            public void run() {
                try {
                    // 验证关键参数的有效性，任一为空都无法继续加载
                    if (context == null || view == null || TextUtils.isEmpty(imageUrl)) {
                        removeView(DeskCloseTypeEnum.IMAGE_ERROR.getMessage());
                        return;
                    }

                    // 使用Picasso适配器加载URL图片，支持模糊和裁剪变换
                    PicassoAdapter.loadFullImage(context, imageUrl, view, needBlurTrans, needCenterCrop,
                            new PicassoAdapter.CallbackAdapter() {
                                @Override
                                public void onSuccess() {
                                    // 图片加载成功，执行回调通知
                                    if (loadResult != null) {
                                        loadResult.onSuccess();
                                    }
                                    // 记录成功日志
                                    log("image url load success");
                                }

                                @Override
                                public void onError() {
                                    // 图片加载失败，记录错误日志
                                    log("image url load error");
                                    // 关闭浮窗，标记为图片错误
                                    removeView(DeskCloseTypeEnum.IMAGE_ERROR.getMessage());
                                }
                            });
                } catch (Throwable t) {
                    // 加载过程中发生异常，记录异常信息
                    log("loadImageUrl: " + t.getMessage());
                    // 关闭浮窗，标记为图片错误
                    removeView(DeskCloseTypeEnum.IMAGE_ERROR.getMessage());
                }
            }
        });
    }

    /**
     * 上报推送关闭事件
     *
     * 当浮窗关闭时上报统计数据，包含关闭原因、用户行为时间等信息：
     * 1. 检查是否已经上报过，避免重复上报
     * 2. 构造额外参数，记录锁屏状态等信息
     * 3. 调用上报接口发送统计数据
     * 4. 结束性能监控逻辑时间收集
     *
     * @param context 应用上下文，用于数据上报
     * @param deskCloseType 关闭类型，如用户主动关闭、超时关闭等
     */
    private void reportPushClose(Context context, String deskCloseType) {
        // 记录当前状态的调试日志
        log("mClickAction" + mClickAction + ", mReportClose:" + mReportClose);

        // 只有在未发生点击行为且未上报过关闭事件时才上报
        // 避免点击跳转和关闭事件的重复上报
        if (!mClickAction && !mReportClose) {
            // 构造额外参数字符串，用于传递补充信息
            String extraMapStr = "";
            try {
                // 创建额外参数映射表
                HashMap<String, String> extraMap = new HashMap<>();
                // 记录浮窗显示时设备的锁屏状态，用于分析用户行为模式
                extraMap.put("isLockWhenShow", mIsLockedWhenShow ? "1" : "0");
                // 将参数映射转换为JSON字符串
                extraMapStr = HadesUtilsAdapter.toJson(extraMap);
            } catch (Throwable t) {
                // 忽略JSON转换异常，使用空字符串作为默认值
            }

            // 标记已上报关闭事件，防止重复上报
            mReportClose = true;

            // 确保桌面资源数据对象不为空，创建默认对象
            if (mDeskResourceData == null) {
                mDeskResourceData = new DeskResourceData(DeskTypeEnum.FULL, "");
            }

            // 调用上报接口，发送推送关闭事件统计数据
            DexDeliveryReporter.reportPushClose(context, DexDeliveryReporter.STAGE_CLOSE, mDeskSourceEnum, deskCloseType,
                    mDeskResourceData, mActionEndTime, mActionEndUpTime, -1, extraMapStr, "");

            // 结束性能监控的逻辑时间收集，用于分析处理性能
            LogicCpuTimeCollectorAdapter.logicEnd(mDeskSourceEnum == null ? "" : mDeskSourceEnum.name(),
                    mDeskResourceData == null ? "" : mDeskResourceData.scene);
        }
    }

    /**
     * 处理推送点击跳转逻辑
     *
     * 当用户点击浮窗内容时调用，负责页面跳转和数据上报：
     * 1. 记录点击行为和时间统计
     * 2. 设置跳转相关参数
     * 3. 执行页面跳转并处理回调
     * 4. 上报点击统计数据
     *
     * @param targetUrl 目标跳转URL
     * @param targetBackList 跳转失败时的备选URL列表
     * @param reportScene 上报场景标识，用于区分点击来源
     */
    private void pushClick(String targetUrl, List<String> targetBackList, final String reportScene) {
        // 获取应用上下文，用于页面跳转和数据上报
        final Context context = HadesUtilsAdapter.getContext();
        // 标记发生了点击行为，用于区分点击跳转和主动关闭
        mClickAction = true;
        // 计算用户行为结束时间（相对于曝光开始的毫秒数）
        mActionEndTime = System.currentTimeMillis() - mExposureTime;
        // 计算用户行为结束时间（基于系统启动时间的相对毫秒数，不受系统时间调整影响）
        mActionEndUpTime = SystemClock.uptimeMillis() - mExposureUpTime;
        // 记录点击发生的系统运行时间，用于性能分析
        mDeskResourceData.pushClickElapsedTime = SystemClock.elapsedRealtime();
        // 标记是否需要在后台路由Activity（产品对话框模式需要）
        mDeskResourceData.needRouterActBackground = showProductDialog;

        // 执行页面跳转，使用工具类统一处理各种跳转逻辑
        DexDeliveryUtils.jump(
                getRouterScene(),        // 获取路由场景标识
                context,                 // 应用上下文
                targetUrl,               // 目标URL
                targetBackList,          // 备选URL列表
                mDeskResourceData,       // 桌面资源数据
                mDeskSourceEnum,         // 推送来源枚举
                "",                      // 额外参数（空字符串）
                new DexDeliveryUtils.JumpCallback() {
                    @Override
                    public void jumpResult(Pair<Boolean, String> result) {
                        // 检查跳转是否成功
                        if (result.first) {
                            // 跳转成功：获取跳转结果标识
                            String lch = result.second;
                            // 上报点击成功统计事件
                            DexDeliveryReporter.reportPushClick(context, DexDeliveryReporter.STAGE_CLICK, mDeskSourceEnum,
                                    mActionEndTime, mActionEndUpTime, lch, mDeskResourceData, -1, false, reportScene);
                            // 产品对话框模式不需要立即结束Activity，普通模式需要结束
                            boolean needFinish = !showProductDialog;
                            // 移除浮窗视图，标记为用户点击关闭
                            removeView(DeskCloseTypeEnum.USER_CLICK.getMessage(), needFinish);
                        } else {
                            // 跳转失败：直接移除浮窗，使用失败原因作为关闭类型
                            removeView(result.second);
                        }
                    }
                }
        );
    }

    /**
     * 移除浮窗视图（简化版本）
     *
     * 默认需要结束Activity的浮窗移除操作
     *
     * @param deskCloseType 关闭类型标识
     */
    private void removeView(final String deskCloseType) {
        // 调用完整版本的removeView方法，默认需要结束Activity
        removeView(deskCloseType, true);
    }

    /**
     * 移除浮窗视图并清理相关资源（完整版本）
     *
     * 当浮窗需要关闭时调用，负责完整的清理工作：
     * 1. 注销监听器和广播接收器
     * 2. 清理图片资源和动画视图
     * 3. 处理预渲染页面的清理
     * 4. 上报关闭统计数据
     * 5. 结束Activity（可选）
     *
     * @param deskCloseType 关闭类型标识，用于统计分析
     * @param needFinish 是否需要结束当前Activity
     */
    private void removeView(final String deskCloseType, boolean needFinish) {
        // 获取应用上下文，用于各种清理操作
        Context context = HadesUtilsAdapter.getContext();
        // 记录移除操作的调试日志，包含关闭类型和屏幕状态
        log("removeView:" + deskCloseType + ",screenon:" + HadesUtilsAdapter.isScreenOn(context));

        // 注销截屏监听器，避免内存泄漏
        unRegisterScreenShotListener();
        // 清理图片资源缓存，释放内存
        DeliveryDataManager.clearImageResource();

        // 标记视图已分离，更新全局状态
        DeliveryDataManager.sViewAttached = false;
        // 清理Lottie动画视图，避免视图泄漏
        if (lottieAnimationView != null && fullContainerView != null) {
            fullContainerView.removeView(lottieAnimationView);
        }

        // 根据关闭类型决定时间统计的处理方式
        if (TextUtils.equals(deskCloseType, DeskCloseTypeEnum.USER_CLICK.getMessage())) {
            // 点击行为的remove按照点击发生的时间上报，不需要更新时间
        } else {
            // 非点击关闭：重新计算行为结束时间
            mActionEndTime = System.currentTimeMillis() - mExposureTime;
            mActionEndUpTime = SystemClock.uptimeMillis() - mExposureUpTime;

            // 非用户点击且已启动预渲染：清理预渲染页面
            if (isPreloadActivityStart && !mClickAction) {
                QPreloadJobServiceAdapter.finishPreRender(context);
            }
        }

        // 上报推送关闭统计事件
        reportPushClose(context, deskCloseType);

        // 取消自动关闭计时器，避免重复执行
        if (autoCloseRunnable != null) {
            mHandler.removeCallbacks(autoCloseRunnable);
        }

        // 注销解锁广播接收器，避免内存泄漏
        unregisterUnlockReceiver();

        try {
            // 获取当前活跃的Activity
            Activity activity = getActiveActivity();
            // 根据需要决定是否结束Activity
            if (activity != null && needFinish) {
                activity.finish();
            }
        } catch (Throwable t) {
            // 记录Activity结束过程中的异常
            log("remove fail " + t.getMessage());
        }
    }

    /**
     * 获取关闭按钮位置枚举
     *
     * 根据整型配置值获取对应的关闭按钮位置枚举：
     * 1. 遍历所有可能的位置枚举值
     * 2. 匹配配置代码返回对应枚举
     * 3. 未匹配时返回默认的底部位置
     *
     * @param closeButtonPos 关闭按钮位置配置代码
     * @return 对应的位置枚举，默认为底部位置
     */
    @NonNull
    private CloseButtonPosEnum getCloseButtonPos(int closeButtonPos) {
        // 遍历所有可能的关闭按钮位置枚举
        for (CloseButtonPosEnum pos : CloseButtonPosEnum.values()) {
            // 匹配配置代码，返回对应的枚举值
            if (pos.getCode() == closeButtonPos) {
                return pos;
            }
        }
        // 未找到匹配的配置时，返回默认的底部位置
        return CloseButtonPosEnum.BOTTOM;
    }

    /**
     * 记录调试日志的统一方法
     *
     * 使用统一的日志标签输出调试信息，便于问题排查和性能分析
     *
     * @param info 要记录的日志信息
     */
    private void log(String info) {
        // 使用工具类输出带有统一标签的调试日志
        LogUtils.d(TAG, info);
    }

    /**
     * 判断是否为拆分展示模式
     *
     * 检查当前配置是否满足拆分模式的条件：
     * 1. 模式标识符匹配（拆分相关的模式）
     * 2. 背景图资源存在（URL或缓存ID）
     * 3. 前景图资源存在（URL或缓存ID）
     *
     * @return true表示拆分模式，false表示非拆分模式
     */
    private boolean isCombinationMode() {
        return (TextUtils.equals(DeskResourceData.TYPE_FULL_SPLICE, mDeskResourceData.dspFullPattern)     // 标准拆分模式
                || TextUtils.equals("dsp_full", mDeskResourceData.dspFullPattern)                         // DSP全屏模式
                || TextUtils.equals("enormous_delphinus_museum", mDeskResourceData.dspFullPattern))       // 特殊拆分模式
                && (!TextUtils.isEmpty(mDeskResourceData.background) || !TextUtils.isEmpty(mDeskResourceData.imageIdMap.get(DeskResourceData.BACKGROUND_ID)))  // 背景图存在
                && (!TextUtils.isEmpty(mDeskResourceData.image) || !TextUtils.isEmpty(mDeskResourceData.imageIdMap.get(DeskResourceData.FULL_IMAGE_ID)));      // 前景图存在
    }

    /**
     * 判断是否为整图展示模式
     *
     * 根据是否显示产品对话框，采用不同的判断逻辑：
     * - 产品对话框模式：只需要有图片资源即可
     * - 普通模式：需要模式标识符匹配且图片资源存在
     *
     * @return true表示整图模式，false表示非整图模式
     */
    private boolean isSingleMode() {
        if (showProductDialog) {
            // 产品对话框模式：只要有图片资源就是整图模式
            return !TextUtils.isEmpty(mDeskResourceData.image) || !TextUtils.isEmpty(mDeskResourceData.imageIdMap.get(DeskResourceData.FULL_IMAGE_ID));
        } else {
            // 普通模式：需要整图模式标识符且有图片资源
            return (TextUtils.equals(DeskResourceData.TYPE_FULL_WHOLE, mDeskResourceData.dspFullPattern)    // 标准整图模式
                    || TextUtils.equals("huge_cygnus_aquarium", mDeskResourceData.dspFullPattern))           // 特殊整图模式
                    && (!TextUtils.isEmpty(mDeskResourceData.image) || !TextUtils.isEmpty(mDeskResourceData.imageIdMap.get(DeskResourceData.FULL_IMAGE_ID))); // 图片资源存在
        }
    }

    /**
     * 获取自动关闭时间配置
     *
     * 从资源配置中获取自动关闭延时时间，提供默认值保障：
     * 1. 优先使用配置中的关闭时间
     * 2. 将秒转换为毫秒
     * 3. 无配置时使用默认10秒
     *
     * @param deskResourceData 桌面资源数据配置
     * @return 自动关闭延时时间（毫秒）
     */
    private long getAutoCloseTime(DeskResourceData deskResourceData) {
        // 检查配置是否存在且关闭时间大于0，转换秒为毫秒；否则使用默认10秒
        return deskResourceData != null && deskResourceData.closeTime > 0
                ? deskResourceData.closeTime * 1000L : 10000;
    }

    /**
     * 获取当前活跃的Activity实例
     *
     * 安全地获取Activity引用，避免内存泄漏和状态异常：
     * 1. 从弱引用中获取Activity
     * 2. 检查Activity是否处于有效状态
     * 3. 确保Activity未销毁且未正在结束
     *
     * @return 有效的Activity实例，如果无效则返回null
     */
    @Nullable
    private Activity getActiveActivity() {
        // 从弱引用中获取Activity实例
        Activity activity = activityRef.get();
        // 检查Activity是否存在且处于有效状态（未结束且未销毁）
        if (activity != null && !activity.isFinishing() && !activity.isDestroyed()) {
            return activity;
        } else {
            // Activity无效或已销毁，返回null
            return null;
        }
    }

    /**
     * 图片加载结果回调接口
     *
     * 用于处理图片加载完成后的后续操作，如显示动画效果、调整布局等
     */
    private interface LoadResult {
        /**
         * 图片加载成功回调方法
         *
         * 在图片成功加载并显示后调用，用于执行依赖于图片尺寸的后续操作
         */
        void onSuccess();
    }
}
