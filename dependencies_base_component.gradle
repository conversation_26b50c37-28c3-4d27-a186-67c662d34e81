dependencies {
    implementation('com.android.support:support-v4:26.0.2') {
        force = true
    }
    implementation('com.android.support:appcompat-v7:26.0.2') {
        force = true
    }
    implementation('com.android.support:support-annotations:26.0.2') {
        force = true
    }
    implementation('com.android.support:recyclerview-v7:26.0.2') {
        force = true
    }
    implementation('com.android.support:gridlayout-v7:26.0.2') {
        force = true
    }
    implementation('com.android.support:design:26.0.2') {
        force = true
    }
    implementation('com.android.support:support-vector-drawable:26.0.2') {
        force = true
    }
    implementation('com.android.support:cardview-v7:26.0.2') {
        force = true
    }
    implementation('com.android.support:gridlayout-v7:26.0.2') {
        force = true
    }
    implementation('com.android.support:support-compat:26.0.2') {
        force = true
    }
    implementation('com.android.support:support-core-utils:26.0.2') {
        force = true
    }
    implementation('com.android.support:support-core-ui:26.0.2') {
        force = true
    }
    implementation('com.android.support:support-media-compat:26.0.2') {
        force = true
    }
    implementation('com.android.support:support-fragment:26.0.2') {
        force = true
    }
}