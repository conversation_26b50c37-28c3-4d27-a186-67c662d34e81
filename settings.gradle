include ':dexeat'
include ':dexsubscribe'
include ':dexdelivery'
include ':dexreport'
include ':dexability'
include ':app'
include ':annotationprocessor'
include ':dexpose'
include ':dexmenu'
include ':dexcentral'
include ':dexcontainer'

include ':bridge'
include ':devtools'
include ':interface'
include ':impl'
include ':dydex'
include ':dexwidget'


rootProject.name = "pin"

//这段逻辑用来决定是否添加本地mt-pin-base库源码依赖，方便调试；本地调试时请解开这段注释
//if (IS_USE_PIN_BASE_LOCAL.equalsIgnoreCase('true')) {
//    include ':library'
//    project(':library').projectDir = new File("${PIN_BASE_LOCATION}/library")
//    include ':qqdynloader'
//    project(':qqdynloader').projectDir = new File("${PIN_BASE_LOCATION}/qqdynloader")
//}
//
//if (SODA_LOCAL_ENABLED.equalsIgnoreCase('true')) {
//    include ':soda'
//    project(':soda').projectDir = new File("$SODA_LOCATION/soda")
//}

include ':dexdrink'
include ':dexbase'
include ':eat'
include ':dexbaseeat'
include ':dexcoffee'

include ':dexsport'