package com.meituan.android.hades.dexdelivery.desk.ui;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.KeyguardManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.wifi.ScanResult;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Pair;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meituan.android.hades.broadcast.BroadcastReceiverX;
import com.meituan.android.hades.dexdelivery.delivery.helper.DeliveryResourceHelper;
import com.meituan.android.hades.dexdelivery.delivery.reporter.DexDeliveryReporter;
import com.meituan.android.hades.dexdelivery.desk.BottomDialogFloatWinStatusCallback;
import com.meituan.android.hades.dexdelivery.desk.callback.ScrollContainerCallback;
import com.meituan.android.hades.dexdelivery.model.DeliveryDeskMaterial;
import com.meituan.android.hades.dexdelivery.model.DeliveryResourceInfo;
import com.meituan.android.hades.dexdelivery.model.DeskArea;
import com.meituan.android.hades.dexdelivery.utils.DexDeliveryUtils;
import com.meituan.android.hades.dexdelivery.desk.ui.view.ScrollContainerView;
import com.meituan.android.hades.dexdelivery.model.DeliveryResourceData;
import com.meituan.android.hades.dexdelivery.model.DeliveryResourceDataCache;
import com.meituan.android.hades.dexdelivery.model.WifiCheckInfo;
import com.meituan.android.hades.dexdelivery.utils.LogUtils;
import com.meituan.android.hades.dexdelivery.wifi.WifiUtil;
import com.meituan.android.hades.dyadater.SntpClockAdapter;
import com.meituan.android.hades.dyadater.desk.DeliveryDataManager;
import com.meituan.android.hades.dyadater.desk.DeskResourceData;
import com.meituan.android.hades.dyadater.desk.DeskSourceEnum;
import com.meituan.android.hades.dyadater.desk.IFlexViewClickEventListenerAdapter;
import com.meituan.android.hades.dyadater.desk.IFlexViewShowStatusListenerAdapter;
import com.meituan.android.hades.dyadater.desk.ui.DeskFloatWinViewAdapter;
import com.meituan.android.hades.dyadater.monitor.LogicCpuTimeCollectorAdapter;
import com.meituan.android.hades.dyadater.utils.HadesUtilsAdapter;
import com.meituan.android.hades.dyadater.utils.QPreloadJobServiceAdapter;
import com.meituan.android.hades.impl.desk.DeskCloseTypeEnum;
import com.meituan.android.hades.impl.desk.DeskTypeEnum;

import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

/**
 * 锁屏浮窗组件
 *
 * 这个类负责在设备锁屏状态下显示推广内容的浮窗，主要功能包括：
 * 1. 监听设备解锁和屏幕状态变化
 * 2. 显示时间、日期等基础信息
 * 3. 处理用户交互（点击、滑动、关闭等）
 * 4. 管理浮窗的生命周期和自动关闭机制
 * 5. 进行WiFi环境检测和内容有效性校验
 * 6. 上报曝光、点击、关闭等行为数据
 *
 * 继承自BaseAbsBottomDialogFloatWin，实现了BottomDialogFloatWinStatusCallback接口
 * 用于处理底部对话框类型的锁屏浮窗展示和用户交互
 */
public class LockDeskFloatWin extends BaseAbsBottomDialogFloatWin implements BottomDialogFloatWinStatusCallback {

    /** 日志标签，用于打印调试信息 */
    private static final String TAG = "Dex_LockDeskFloatWin";

    /** 用户解锁类型常量，用于标识解锁操作 */
    private static final String TYPE_USER_UNLOCK = "1";
    /** 用户屏幕点亮类型常量，用于标识屏幕点亮操作 */
    private static final String TYPE_USER_SCREEN_ON = "2";
    /** 用户滑动关闭类型常量，用于标识滑动关闭操作 */
    private static final String TYPE_USER_SCROLL_CLOSE = "3";
    /** 向上滑动类型常量，用于标识向上滑动操作 */
    private static final String TYPE_SLIDE_UP = "4";

    /** 桌面区域信息，包含投放素材相关数据 */
    private DeskArea<DeliveryDeskMaterial> mDeskInfo;
    /** 投放资源信息，包含配置和参数 */
    private DeliveryResourceInfo mDeliveryResourceInfo;
    /** 桌面资源数据，包含浮窗展示所需的所有数据 */
    protected DeskResourceData mDeskResourceData = new DeskResourceData();
    /** 桌面来源枚举，标识浮窗的来源类型 */
    private DeskSourceEnum mDeskSourceEnum = DeskSourceEnum.OTHER;

    /** 主线程Handler，用于在主线程执行UI相关操作 */
    private final Handler mHandler = new Handler(Looper.getMainLooper());

    /** 浮窗曝光开始时间（系统时间戳） */
    private long mExposureTime = 0L;
    /** 浮窗曝光开始时间（系统运行时间） */
    private long mExposureUpTime = 0L;

    /** 是否已上报关闭事件的标志位，避免重复上报 */
    private boolean mReportClose = false;
    /** 是否发生点击行为的标志位，用于区分点击和关闭事件 */
    private boolean mClickAction = false;
    /** 用户行为结束时间（相对于曝光开始时间的毫秒数） */
    private long mActionEndTime = 0L;
    /** 用户行为结束时间（相对于曝光开始的系统运行时间毫秒数） */
    private long mActionEndUpTime = 0L;
    /** 是否已预热主进程的标志位，避免重复预热 */
    private boolean isWarmup = false;
    /** 浮窗是否获得过焦点的标志位，用于判断用户是否真正看到了浮窗 */
    private boolean mHasFocus = false;
    /** 是否已请求WiFi信息的标志位，避免重复请求 */
    private boolean mHasRequestWifi = false;
    /** 上次窗口焦点状态，用于比较焦点变化 */
    private boolean mLastFocus = false;

    /** 自动关闭浮窗的Runnable任务 */
    private Runnable autoCloseRunnable;
    /** Activity的弱引用，避免内存泄漏 */
    private WeakReference<Activity> activityRef = new WeakReference<>(null);

    /** 根布局容器 */
    private RelativeLayout rootLayout;
    /** 时间显示文本组件 */
    TextView mTimeTextView;
    /** 日期显示文本组件 */
    TextView mDateTextView;

    /** 定时器，用于定时更新时间显示和检查过期时间 */
    private Timer timer;

    /** 解锁和屏幕状态广播接收器 */
    private BroadcastReceiver unlockReceive;
    /** WiFi扫描结果列表，用于WiFi环境检测 */
    private List<ScanResult> mScanResults;

    /**
     * Activity创建时的回调方法
     *
     * 这个方法在Activity被创建时调用，负责初始化锁屏浮窗的所有组件和状态
     * 包括检查设备状态、设置窗口标志、初始化UI组件和注册监听器
     *
     * @param activity 当前活动的Activity实例
     * @param savedInstanceState Activity的保存状态（通常为null）
     * @param deskResourceData 桌面资源数据，包含浮窗显示所需的所有信息
     * @param deskSourceEnum 桌面来源枚举，标识浮窗的来源类型
     */
    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState, @NonNull DeskResourceData deskResourceData, @NonNull DeskSourceEnum deskSourceEnum) {
        log("onActivityCreated");
        final Context context = HadesUtilsAdapter.getContext();

        // 保存Activity的弱引用，避免内存泄漏
        activityRef = new WeakReference<>(activity);

        // 创建自动关闭浮窗的任务，在指定时间后自动关闭浮窗
        autoCloseRunnable = new Runnable() {
            @Override
            public void run() {
                removeView(DeskCloseTypeEnum.AUTO_CLOSE.getMessage());
            }
        };

        // 检查屏幕方向，如果不是竖屏则关闭浮窗
        if (!HadesUtilsAdapter.isOrientationPortrait(context)) {
            removeView(DeskCloseTypeEnum.LANDSCAPE.getMessage());
            return;
        }

        // 检查直接唤醒条件：如果条件为0且屏幕已点亮且设备未锁定，则关闭浮窗
        if (deskResourceData.sceneParam.directAwakeCondition == 0
                && HadesUtilsAdapter.isScreenOn(context) && !HadesUtilsAdapter.isLocked()) {
            removeView(DeskCloseTypeEnum.SCREEN_SCREEN_ON.getMessage());
            return;
        }

        // 初始化各种状态标志位
        mReportClose = false;     // 重置关闭事件上报状态
        mClickAction = false;     // 重置点击行为状态
        mExposureTime = System.currentTimeMillis();    // 记录曝光开始的系统时间
        mExposureUpTime = SystemClock.uptimeMillis();  // 记录曝光开始的系统运行时间
        mDeskResourceData = deskResourceData;          // 保存桌面资源数据
        mDeskSourceEnum = deskSourceEnum;              // 保存桌面来源枚举

        // 标记视图已附加状态，用于全局状态管理
        DeliveryDataManager.sViewAttached = true;
        DeliveryDataManager.sLockTopViewAttached = true;

        // 设置窗口标志：在锁屏上显示并防止截屏
        activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                | WindowManager.LayoutParams.FLAG_SECURE);

        // 如果需要点亮屏幕，则添加点亮屏幕的标志
        if (needLightScreen()) {
            activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        }

        // 设置Activity的布局
        activity.setContentView(DeskFloatWinViewAdapter.get_hades_lock_desk_act());

        // 初始化各个组件
        initDeliveryResourceData();  // 初始化投放资源数据
        initView(activity);          // 初始化视图组件
        registerUnlockReceiver();    // 注册解锁和屏幕状态监听器
    }

    /**
     * 检查是否需要点亮屏幕
     *
     * 从底部浮窗配置信息中获取是否需要点亮屏幕的设置
     *
     * @return true表示需要点亮屏幕，false表示不需要
     */
    private boolean needLightScreen() {
        Object lightScreen = mDeskResourceData.bottomFloatingInfo.get("lightScreen");
        return lightScreen instanceof Boolean && (Boolean) lightScreen;
    }

    /**
     * 注册设备解锁和屏幕状态变化的广播接收器
     *
     * 监听以下系统广播事件：
     * 1. ACTION_USER_PRESENT：用户解锁设备
     * 2. ACTION_SCREEN_ON：屏幕点亮
     * 3. ACTION_SCREEN_OFF：屏幕熄灭
     */
    private void registerUnlockReceiver() {
        if (unlockReceive == null) {
            unlockReceive = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (intent == null) {
                        return;
                    }
                    // 处理用户解锁事件
                    if (Intent.ACTION_USER_PRESENT.equals(intent.getAction())) {
                        log( "Device unlocked");
                        if (canJump(TYPE_USER_UNLOCK)) {
                            closeClick("USER_UNLOCK");  // 如果可以跳转，则执行关闭点击
                        } else {
                            removeView("USER_UNLOCK");  // 否则直接移除视图
                        }
                    }
                    // 处理屏幕点亮事件
                    else if (Intent.ACTION_SCREEN_ON.equals(intent.getAction())) {
                        log( "Device SCREEN_ON 1");
                        // 延迟100ms后检查浮窗是否获得过焦点
                        HadesUtilsAdapter.runOnMainThreadWithDelay(new Runnable() {
                            @Override
                            public void run() {
                                if (!mHasFocus) {
                                    log( "Device SCREEN_ON");
                                    if (canJump(TYPE_USER_SCREEN_ON)) {
                                        closeClick("USER_SCREEN_ON");  // 如果可以跳转，则执行关闭点击
                                    } else {
                                        removeView("USER_SCREEN_ON");  // 否则直接移除视图
                                    }
                                }
                            }
                        }, 100);
                    }
                    // 处理屏幕熄灭事件（暂无具体处理逻辑）
                    else if (Intent.ACTION_SCREEN_OFF.equals(intent.getAction())) {
                        // 暂不处理屏幕熄灭事件
                    }
                }
            };
        }
        // 注册广播接收器，监听指定的系统广播
        BroadcastReceiverX.register(HadesUtilsAdapter.getContext(), unlockReceive, Intent.ACTION_USER_PRESENT, Intent.ACTION_SCREEN_ON, Intent.ACTION_SCREEN_OFF);
    }

    /**
     * 取消注册解锁和屏幕状态广播接收器
     *
     * 在组件销毁时调用，防止内存泄漏
     */
    private void unregisterUnlockReceiver() {
        if (unlockReceive != null) {
            BroadcastReceiverX.unRegister(HadesUtilsAdapter.getContext(), unlockReceive);
            unlockReceive = null;
        }
    }

    /**
     * 初始化视图组件
     *
     * 这个方法负责初始化锁屏浮窗的所有UI组件，包括：
     * 1. 查找和绑定各种布局和控件
     * 2. 设置手势监听器处理用户滑动
     * 3. 创建滚动容器处理拖拽关闭
     * 4. 启动定时器更新时间显示
     * 5. 添加浮窗内容
     *
     * @param activity 当前活动的Activity实例
     */
    @SuppressLint("ClickableViewAccessibility")
    private void initView(final Activity activity) {
        // 查找并绑定布局控件
        rootLayout = activity.findViewById(DeskFloatWinViewAdapter.get_hades_lock_root_layout());
        FrameLayout touchView = activity.findViewById(DeskFloatWinViewAdapter.get_hades_lock_win_background());
        final FrameLayout containerView = activity.findViewById(DeskFloatWinViewAdapter.get_hades_lock_win_container());
        mTimeTextView = activity.findViewById(DeskFloatWinViewAdapter.get_time_text_view());
        mDateTextView = activity.findViewById(DeskFloatWinViewAdapter.get_date_text_view());

        // 启动定时器，定期更新时间显示和检查过期状态
        startTimer();

        // 创建手势检测器，处理用户的滑动手势
        final GestureDetector gestureDetector = new GestureDetector(activity, new GestureListener(this));
        touchView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                // 将触摸事件传递给手势检测器处理
                boolean result = gestureDetector.onTouchEvent(event);
                log("onTouch: " + result);
                return true; // 返回true表示消费了触摸事件
            }
        });

        // 创建可滚动容器，支持用户拖拽关闭浮窗
        ScrollContainerView scrollContainerView = new ScrollContainerView(activity, new ScrollContainerCallback() {
            @Override
            public void animateViewOutEnd() {
                // 当拖拽动画结束时的回调处理
                if (canJump(TYPE_USER_SCROLL_CLOSE)) {
                    closeClick("USER_SCROLL_CLOSE");  // 可跳转则执行关闭点击
                } else {
                    removeView("USER_SCROLL_CLOSE");  // 否则直接移除视图
                }
            }
        });

        // 设置滚动容器的布局参数
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                );
        scrollContainerView.setLayoutParams(layoutParams);
        containerView.addView(scrollContainerView);

        // 添加具体的浮窗内容
        addContent(activity, scrollContainerView);
    }

    /**
     * 更新日期时间显示
     *
     * 获取当前系统时间和日期，并格式化显示在对应的TextView中
     * 时间格式：HH:mm
     * 日期格式：M月d日 EEEE（如：12月25日 星期一）
     */
    private void updateDateTime() {
        // 检查TextView是否已初始化
        if (mTimeTextView == null || mDateTextView == null) {
            return;
        }
        try {
            // 获取当前时间
            Calendar calendar = Calendar.getInstance();

            // 格式化时间显示（24小时制，如：14:30）
            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm", Locale.getDefault());
            String currentTime = timeFormat.format(calendar.getTime());

            // 格式化日期显示（如：12月25日 星期一）
            SimpleDateFormat dateFormat = new SimpleDateFormat("M月d日 EEEE", Locale.getDefault());
            String currentDate = dateFormat.format(calendar.getTime());

            // 设置文本到对应的TextView
            mTimeTextView.setText(currentTime);
            mDateTextView.setText(currentDate);
        } catch (Throwable t) {
            // 忽略格式化异常，避免崩溃
        }
    }

    /**
     * 添加浮窗具体内容
     *
     * 这个方法负责向滚动容器中添加实际的浮窗内容，并设置相应的事件监听器
     * 处理点击和关闭事件，以及内容显示状态的回调
     *
     * @param activity 当前活动的Activity实例
     * @param containerView 容器视图，用于承载浮窗内容
     */
    protected void addContent(Activity activity, FrameLayout containerView) {
        DeskFloatWinViewAdapter.addBottomDialogContent(containerView, mDeskResourceData.bottomFloatingInfo,
                new IFlexViewClickEventListenerAdapter() {
                    @Override
                    public void handleClickEvent(String action, Map<String, Object> data) {
                        log("handleClickEvent action" + action);
                        // 检查参数有效性
                        if (TextUtils.isEmpty(action) || data == null) {
                            removeView(DeskCloseTypeEnum.PARAM_NULL.getMessage());
                            return;
                        }
                        // 处理点击事件
                        if (TextUtils.equals("click", action)) {
                            try {
                                Object urlObj = data.get("targetUrl");
                                if (urlObj != null && !TextUtils.isEmpty(urlObj.toString())) {
                                    String targetUrl = urlObj.toString();
                                    String redirectUrl = data.get("kkRedirect") instanceof String ? (String) data.get("kkRedirect") : "";
                                    String extraInfo = HadesUtilsAdapter.toJson(data);
                                    // 开始执行点击跳转
                                    pushClickStart(targetUrl, mDeskResourceData.targetBack, extraInfo, redirectUrl);
                                } else {
                                    removeView("targetUrl_null");
                                }
                            } catch (Throwable t) {
                                removeView(DeskCloseTypeEnum.PARAM_NULL.getMessage());
                            }
                        }
                        // 处理关闭事件
                        else if ("close".equals(action)) {
                            try {
                                Object closeTypeObj = data.get("closeType");
                                if (closeTypeObj != null && !TextUtils.isEmpty(closeTypeObj.toString())) {
                                    removeView(closeTypeObj.toString());
                                } else {
                                    removeView("closeType_null");
                                }
                            } catch (Throwable t) {
                                removeView("click_exception");
                            }
                        }
                    }
                }, new IFlexViewShowStatusListenerAdapter() {
                    @Override
                    public void onShow() {
                        // 内容成功显示的回调（暂无处理逻辑）
                    }

                    @Override
                    public void onFailed() {
                        // 内容显示失败时移除浮窗
                        removeView("show_fail");
                    }
                });
    }

    /**
     * Activity启动时的回调方法
     */
    @Override
    public void onActivityStarted() {
        log("onActivityStarted");
    }

    /**
     * Activity恢复时的回调方法
     *
     * 在Activity恢复到前台时执行以下操作：
     * 1. 更新时间显示
     * 2. 检查设备锁屏状态并触发用户观看事件
     * 3. 根据配置预热主进程
     */
    @Override
    public void onActivityResumed() {
        log("onActivityResumed");
        if (getActiveActivity() == null) {
            return;
        }
        // 更新时间显示
        updateDateTime();

        final Context context = HadesUtilsAdapter.getContext();
        // 延迟1秒检查屏幕和锁屏状态，如果屏幕亮起且设备未锁定则触发用户观看事件
        HadesUtilsAdapter.runOnMainThreadWithDelay(new Runnable() {
            @Override
            public void run() {
                if (HadesUtilsAdapter.isScreenOn(context) && !HadesUtilsAdapter.isLocked()) {
                    onUserWatch("onActivityResumed");
                }
            }
        }, 1000);

        // 根据配置预热主进程，提升后续页面打开速度
        if (mDeskResourceData != null) {
            if (HadesUtilsAdapter.enablePreloadWarmUpMainProcess() && !isWarmup) {
                QPreloadJobServiceAdapter.warmupMainProcess(context, mDeskResourceData.target);
                isWarmup = true;
            }
        }
    }

    /**
     * Activity暂停时的回调方法
     */
    @Override
    public void onActivityPaused() {
        log("onActivityPaused");
    }

    /**
     * Activity停止时的回调方法
     */
    @Override
    public void onActivityStopped() {
        log("onActivityStopped");
    }

    /**
     * Activity销毁时的回调方法
     *
     * 清理所有资源和状态，包括：
     * 1. 重置全局状态标志位
     * 2. 清理缓存数据
     * 3. 取消注册广播接收器
     * 4. 停止定时器
     * 5. 移除视图并结束Activity
     */
    @Override
    public void onActivityDestroyed() {
        log("onActivityDestroyed");
        // 重置全局状态标志位
        DeliveryDataManager.sViewAttached = false;
        DeliveryDataManager.sLockTopViewAttached = false;

        // 清理缓存数据
        DeliveryResourceDataCache.deliveryResourceInfoMap.remove(mDeskResourceData.sessionId);

        // 取消注册广播接收器
        unregisterUnlockReceiver();

        // 停止定时器
        if (timer != null) {
            timer.cancel();
        }

        try {
            // 移除视图并上报关闭事件
            removeView(DeskCloseTypeEnum.USER_CLOSE.getMessage());
        } catch (Throwable t) {
            log(t.getMessage());
        }
    }

    /**
     * 窗口焦点变化时的回调方法
     *
     * 当浮窗获得或失去焦点时被调用，用于处理WiFi检查、用户观看统计和自动关闭逻辑
     *
     * @param hasFocus true表示获得焦点，false表示失去焦点
     */
    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        Activity activity = getActiveActivity();
        if (activity == null) {
            return;
        }
        log("onWindowFocusChanged hasFocus: " + hasFocus);

        if (hasFocus) {
            // 检查是否需要进行WiFi信息获取
            boolean needCheckWifiInfo = DeliveryResourceHelper.needCheckWifiInfo(mDeliveryResourceInfo);
            if (needCheckWifiInfo && !mHasRequestWifi) {
                mHasRequestWifi = true;
                getWifiInfo();
            } else if (!mLastFocus) {
                // 如果之前没有焦点，现在获得焦点，则启动自动关闭计时器
                triggerAutoClose();
            }
            // 触发用户观看统计
            onUserWatch("onWindowFocusChanged");
        } else {
            // 失去焦点时检查WiFi信息并取消自动关闭
            checkWifiInfo();
            if (autoCloseRunnable != null) {
                mHandler.removeCallbacks(autoCloseRunnable);
            }
        }
        mLastFocus = hasFocus;
    }

    /**
     * 用户观看浮窗的处理方法
     *
     * 当确认用户真正看到浮窗时被调用，负责：
     * 1. 检查内容是否过期
     * 2. 更新曝光时间
     * 3. 上报曝光事件
     *
     * @param from 调用来源标识，用于区分不同的触发场景
     */
    private void onUserWatch(String from) {
        if (!mHasFocus) {
            log("onUserWatch" + from);
            mHasFocus = true;

            // 检查内容是否已过期
            long exposureExpireTime = mDeliveryResourceInfo != null && mDeliveryResourceInfo.sceneParam != null ?
                    mDeliveryResourceInfo.sceneParam.exposureExpireTime : -1;
            if (exposureExpireTime > 0 && System.currentTimeMillis() > exposureExpireTime) {
                removeView("TIME_OUT");
                return;
            }

            // 更新曝光时间
            mExposureTime = System.currentTimeMillis();
            mExposureUpTime = SystemClock.uptimeMillis();

            // 上报曝光事件
            DexDeliveryReporter.reportPushExposure(
                    HadesUtilsAdapter.getContext(), DexDeliveryReporter.STAGE_EXPOSURE, mDeskSourceEnum, mDeskResourceData, from);
        }
    }

    /**
     * 用户按返回键时的回调方法
     *
     * 目前已注释，不处理返回键事件
     */
    @Override
    public void onBackPressed() {
//        try {
//            String backTargetUrl = (String) mDeskResourceData.bottomFloatingInfo.get("backTarget");
//            if (!TextUtils.isEmpty(backTargetUrl)) {
//                pushClick(backTargetUrl, mDeskResourceData.targetBack, "back", "");
//            } else {
//                removeView(DeskCloseTypeEnum.USER_BACK.getMessage());
//            }
//        } catch (Throwable t) {
//            removeView(DeskCloseTypeEnum.USER_BACK.getMessage());
//        }
    }

    /**
     * 接收到新Intent时的回调方法
     *
     * 当同一个浮窗需要被新的内容替换时调用，负责：
     * 1. 检查设备状态和方向
     * 2. 上报当前浮窗关闭事件
     * 3. 重新初始化浮窗状态和内容
     *
     * @param intent 新的Intent对象
     * @param deskResourceData 新的桌面资源数据
     * @param deskSourceEnum 新的桌面来源枚举
     */
    @Override
    public void onNewIntent(Intent intent, @NonNull DeskResourceData deskResourceData, @NonNull DeskSourceEnum deskSourceEnum) {
        log("onNewIntent");
        final Context context = HadesUtilsAdapter.getContext();

        // 检查屏幕方向，非竖屏则直接返回
        if (!HadesUtilsAdapter.isOrientationPortrait(context)) {
            return;
        }

        // 检查直接唤醒条件
        if (deskResourceData.sceneParam.directAwakeCondition == 0
                && HadesUtilsAdapter.isScreenOn(context) && !HadesUtilsAdapter.isLocked()) {
            return;
        }

        Activity activity = getActiveActivity();
        if (activity == null) {
            removeView("activity_null");
            return;
        }

        // 计算当前浮窗的显示时长并上报关闭事件
        mActionEndTime = System.currentTimeMillis() - mExposureTime;
        mActionEndUpTime = SystemClock.uptimeMillis() - mExposureUpTime;
        reportPushClose(context, "replace");

        // 重新创建自动关闭任务
        if (autoCloseRunnable != null) {
            mHandler.removeCallbacks(autoCloseRunnable);
        }
        autoCloseRunnable = new Runnable() {
            @Override
            public void run() {
                removeView(DeskCloseTypeEnum.AUTO_CLOSE.getMessage());
            }
        };

        // 重新设置窗口标志
        activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                | WindowManager.LayoutParams.FLAG_SECURE);

        // 重新设置布局并重置状态变量
        activity.setContentView(DeskFloatWinViewAdapter.get_hades_lock_desk_act());
        mReportClose = false;      // 重置关闭上报状态
        mClickAction = false;      // 重置点击行为状态
        isWarmup = false;          // 重置预热状态
        mHasFocus = false;         // 重置焦点状态
        mLastFocus = false;        // 重置上次焦点状态
        mHasRequestWifi = false;   // 重置WiFi请求状态
        mActionEndTime = 0;        // 重置行为结束时间
        mActionEndUpTime = 0;      // 重置行为结束运行时间

        // 更新时间和数据
        mExposureTime = System.currentTimeMillis();
        mExposureUpTime = SystemClock.uptimeMillis();
        mDeskResourceData = deskResourceData;
        mDeskSourceEnum = deskSourceEnum;

        // 更新全局状态
        DeliveryDataManager.sViewAttached = true;
        DeliveryDataManager.sLockTopViewAttached = true;

        // 重新启动定时器
        if (timer != null) {
            timer.cancel();
            timer = null;
        }

        // 重新初始化视图
        initView(activity);
    }

    /**
     * 截屏事件回调方法
     *
     * 当检测到用户截屏时调用（目前无具体实现）
     */
    @Override
    public void onScreenShot() {

    }

    /**
     * 触发自动关闭浮窗的方法
     *
     * 根据配置的自动关闭时间，延迟执行浮窗关闭操作
     */
    private void triggerAutoClose() {
        log("triggerAutoClose");
        if (autoCloseRunnable != null) {
            long delayMillis = getAutoCloseTime(mDeskResourceData);
            mHandler.removeCallbacks(autoCloseRunnable);
            mHandler.postDelayed(autoCloseRunnable, delayMillis);
        }
    }

    /**
     * 开始执行点击跳转处理
     *
     * 这个方法是点击事件的入口，负责解锁屏幕并执行实际的跳转逻辑
     *
     * @param targetUrl 目标URL地址
     * @param targetBackList 返回堆栈列表
     * @param reportScene 上报场景标识
     * @param redirectUrl 重定向URL地址
     */
    protected void pushClickStart(String targetUrl, List<String> targetBackList, String reportScene, String redirectUrl) {
        Activity activity = getActiveActivity();
        if (activity != null) {
            // 尝试解锁屏幕
            showAuthenticationScreen(activity);
            // 执行实际的点击跳转
            pushClick(targetUrl, targetBackList, reportScene, redirectUrl);
        } else {
            removeView("click_error");
        }
    }

    /**
     * 显示身份验证屏幕（解锁屏幕）
     *
     * 当用户点击浮窗内容时，尝试请求解锁屏幕以便跳转到目标页面
     * 只在Android O以上版本且屏幕锁定状态下执行
     *
     * @param activity 当前Activity实例
     */
    private void showAuthenticationScreen(Activity activity) {
        try {
            KeyguardManager keyguardManager = (KeyguardManager) HadesUtilsAdapter.getContext().getSystemService(Context.KEYGUARD_SERVICE);
            if (keyguardManager.isKeyguardLocked() && Build.VERSION.SDK_INT > Build.VERSION_CODES.O) {
                log("isKeyguardLocked");
                // 请求解锁屏幕
                keyguardManager.requestDismissKeyguard(activity, new KeyguardManager.KeyguardDismissCallback() {
                    @Override
                    public void onDismissError() {
                        super.onDismissError();
                        log("onDismissError");
                    }

                    @Override
                    public void onDismissCancelled() {
                        super.onDismissCancelled();
                        log("onDismissCancelled");
                    }

                    @Override
                    public void onDismissSucceeded() {
                        super.onDismissSucceeded();
                        log("onDismissSucceeded");
                    }
                });
            }
        } catch (Throwable t) {
            // 忽略解锁异常，避免崩溃
        }
    }

    /**
     * 处理关闭类型的点击事件
     *
     * 从配置中获取关闭目标URL并执行跳转，主要用于解锁或屏幕点亮等场景
     *
     * @param reportScene 上报场景标识，用于数据统计
     */
    private void closeClick(final String reportScene) {
        Object closeTarget = mDeskResourceData.bottomFloatingInfo.get("closeTarget");
        if (closeTarget instanceof String && !TextUtils.isEmpty((String) closeTarget)) {
            String targetUrl = (String) closeTarget;
            List<String> targetBackList = mDeskResourceData.targetBack;
            // 执行点击跳转
            pushClick(targetUrl, targetBackList, reportScene, "");
        } else {
            // 如果没有配置关闭目标，则直接移除浮窗
            removeView(reportScene + "_closeTarget_null");
        }
    }

    /**
     * 执行点击跳转逻辑
     *
     * 这个方法负责实际的页面跳转和数据上报，包括：
     * 1. 设置点击行为标志位
     * 2. 计算用户交互时长
     * 3. 执行页面跳转
     * 4. 根据跳转结果上报数据
     *
     * @param targetUrl 目标URL地址
     * @param targetBackList 返回堆栈列表
     * @param reportScene 上报场景标识
     * @param redirectUrl 重定向URL地址
     */
    private void pushClick(final String targetUrl, List<String> targetBackList, final String reportScene, String redirectUrl) {
        final Context context = HadesUtilsAdapter.getContext();
        // 设置点击行为标志，避免重复上报关闭事件
        mClickAction = true;
        // 计算用户从曝光到点击的时长
        mActionEndTime = System.currentTimeMillis() - mExposureTime;
        mActionEndUpTime = SystemClock.uptimeMillis() - mExposureUpTime;
        // 记录点击时间戳，用于性能统计
        mDeskResourceData.pushClickElapsedTime = SystemClock.elapsedRealtime();

        // 执行页面跳转
        DexDeliveryUtils.jump(
                getRouterScene(),
                context,
                targetUrl,
                targetBackList,
                mDeskResourceData,
                mDeskSourceEnum,
                redirectUrl,
                new DexDeliveryUtils.JumpCallback() {
                    @Override
                    public void jumpResult(Pair<Boolean, String> result) {
                        if (result.first) {
                            // 跳转成功，上报点击事件
                            String lch = result.second;
                            DexDeliveryReporter.reportPushClick(context, DexDeliveryReporter.STAGE_CLICK, mDeskSourceEnum,
                                    mActionEndTime, mActionEndUpTime, lch, mDeskResourceData, -1, false, reportScene);
                            removeView(DeskCloseTypeEnum.USER_CLICK.getMessage());
                        } else {
                            // 跳转失败，移除浮窗
                            removeView(result.second);
                        }
                    }
                }
        );
    }

    /**
     * 移除浮窗视图并清理资源
     *
     * 这个方法负责浮窗的清理工作，包括：
     * 1. 隐藏视图
     * 2. 上报关闭事件
     * 3. 清理定时器和监听器
     * 4. 结束Activity
     *
     * @param deskCloseType 关闭类型，用于数据统计
     */
    protected void removeView(final String deskCloseType) {
        Context context = HadesUtilsAdapter.getContext();
        log("removeView:" + deskCloseType + ",screenon:" + HadesUtilsAdapter.isScreenOn(context));

        // 隐藏根布局
        if (rootLayout != null) {
            rootLayout.setVisibility(View.GONE);
        }

        // 处理数据上报逻辑
        if (TextUtils.equals(DeskCloseTypeEnum.USER_CLICK.getMessage(), deskCloseType)) {
            // 点击行为的remove按照点击发生的时间上报, 不需要更新时间
        } else {
            // 计算浮窗显示时长并上报关闭事件
            mActionEndTime = System.currentTimeMillis() - mExposureTime;
            mActionEndUpTime = SystemClock.uptimeMillis() - mExposureUpTime;
            reportPushClose(context, deskCloseType);
        }

        // 清理资源
        if (timer != null) {
            timer.cancel();
        }
        unregisterUnlockReceiver();
        mHandler.removeCallbacks(autoCloseRunnable);

        try {
            // 结束Activity
            Activity activity = getActiveActivity();
            if (activity != null) {
                activity.finish();
            }
        } catch (Throwable t) {
            log("remove fail " + t.getMessage());
        }
    }

    /**
     * 上报浮窗关闭事件
     *
     * 避免重复上报：只在没有点击行为且没有上报过关闭事件时才上报
     *
     * @param context 上下文对象
     * @param deskCloseType 关闭类型标识
     */
    private void reportPushClose(Context context, String deskCloseType) {
        log("mClickAction" + mClickAction + ", mReportClose:" + mReportClose);
        // 没有上报过点击事件并且没有上报过关闭事件，则上报关闭事件
        if (!mClickAction && !mReportClose) {
            mReportClose = true;
            if (mDeskResourceData == null) {
                mDeskResourceData = new DeskResourceData(DeskTypeEnum.BOTTOM_DIALOG, "");
            }
            // 上报关闭事件数据
            DexDeliveryReporter.reportPushClose(context, DexDeliveryReporter.STAGE_CLOSE, mDeskSourceEnum, deskCloseType,
                    mDeskResourceData, mActionEndTime, mActionEndUpTime, -1, null, "");
            // 结束性能统计
            LogicCpuTimeCollectorAdapter.logicEnd(mDeskSourceEnum == null ? "" : mDeskSourceEnum.name(), mDeskResourceData == null ? "" : mDeskResourceData.scene);
        }
    }

    /**
     * 获取自动关闭时间
     *
     * 从配置中读取自动关闭时间，如果没有配置则使用默认的10秒
     *
     * @param deskResourceData 桌面资源数据
     * @return 自动关闭时间（毫秒）
     */
    private long getAutoCloseTime(DeskResourceData deskResourceData) {
        return deskResourceData != null && deskResourceData.closeTime > 0
                ? deskResourceData.closeTime * 1000L : 10000;
    }

    /**
     * 获取当前有效的Activity实例
     *
     * 检查Activity是否还存在且未被销毁，确保UI操作的安全性
     *
     * @return 有效的Activity实例，如果Activity已销毁则返回null
     */
    @Nullable
    private Activity getActiveActivity() {
        Activity activity = activityRef.get();
        if (activity != null && !activity.isFinishing() && !activity.isDestroyed()) {
            return activity;
        } else {
            return null;
        }
    }

    /**
     * 启动定时器
     *
     * 每30秒执行一次定时任务，用于：
     * 1. 检查内容是否过期
     * 2. 更新时间显示
     */
    private void startTimer() {
        if (timer == null) {
            timer = new Timer();
        }
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                log("Timer triggered");
                // 在主线程执行UI更新
                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        showTimelyCheck();  // 检查过期时间
                        updateDateTime();   // 更新时间显示
                    }
                });
            }
        }, 0, 30000); // 立即执行，然后每30秒执行一次
    }

    /**
     * 定时检查内容是否过期
     *
     * 如果内容已过期，则自动关闭浮窗
     */
    private void showTimelyCheck() {
        long exposureExpireTime = mDeskResourceData.sceneParam.exposureExpireTime;
        if (exposureExpireTime > 0 && SntpClockAdapter.currentTimeMillis() > exposureExpireTime) {
            removeView("SHOW_TIME_CHECK");
        }
    }

    /**
     * 异步获取WiFi信息
     *
     * 在工作线程中扫描WiFi列表，用于后续的WiFi环境校验
     */
    private void getWifiInfo() {
        HadesUtilsAdapter.runOnWorkThread(new Runnable() {
            @Override
            public void run() {
                log("scan start");
                Context context = HadesUtilsAdapter.getContext();
                // 扫描WiFi列表，超时时间5秒
                List<ScanResult> scanResults = WifiUtil.getInstance().getWifiList(context, 5000, TimeUnit.MILLISECONDS);
                log("scan end");
                if (scanResults != null) {
                    log("scanResults:" + scanResults.size());
                    mScanResults = scanResults;
                }
            }
        });
    }

    /**
     * 检查WiFi环境是否满足要求
     *
     * 根据配置的WiFi名称和信号强度要求，检查当前WiFi环境
     * 如果不满足要求，则关闭浮窗
     */
    private void checkWifiInfo() {
        boolean needCheckWifiInfo = DeliveryResourceHelper.needCheckWifiInfo(mDeliveryResourceInfo);
        if (needCheckWifiInfo && mScanResults != null) {
            WifiCheckInfo wifiCheckInfo = mDeliveryResourceInfo.sceneParam.wifiCheckInfo;
            String wifiName = wifiCheckInfo.wfName;       // 要求的WiFi名称
            int wifiLevel = wifiCheckInfo.wfLevel;        // 要求的信号强度

            if (!TextUtils.isEmpty(wifiName) && wifiLevel > -200) {
                // 构建WiFi名称和信号强度的映射表
                HashMap<String, Integer> scanResultMap = new HashMap<>();
                for (ScanResult scanResult : mScanResults) {
                    if (scanResultMap.containsKey(scanResult.SSID)) {
                        // 如果存在同名WiFi，保留信号强度更高的
                        Integer scanLevel = scanResultMap.get(scanResult.SSID);
                        if (scanLevel != null && scanLevel < scanResult.level) {
                            scanResultMap.put(scanResult.SSID, scanResult.level);
                        }
                    } else {
                        scanResultMap.put(scanResult.SSID, scanResult.level);
                    }
                }

                // 检查要求的WiFi是否存在
                if (!scanResultMap.containsKey(wifiName)) {
                    removeView("NO_WIFI_NAME");
                }
                // 检查WiFi信号强度是否满足要求
                else if (scanResultMap.containsKey(wifiName)
                        && scanResultMap.get(wifiName) != null
                        && scanResultMap.get(wifiName) < wifiLevel) {
                    removeView("WIFI_LEVEL_LOW");
                }
            }
        }
    }

    /**
     * 初始化投放资源数据
     *
     * 从缓存中获取完整的投放资源信息和桌面区域数据
     */
    private void initDeliveryResourceData() {
        if (!TextUtils.isEmpty(mDeskResourceData.sessionId)) {
            mDeliveryResourceInfo = DeliveryResourceDataCache.deliveryResourceInfoMap.get(mDeskResourceData.sessionId);
            mDeskInfo = mDeliveryResourceInfo.deskInfo;
        }
    }

    /**
     * 判断是否可以执行跳转
     *
     * 根据配置的openType参数，判断在特定关闭场景下是否允许跳转
     *
     * @param closeType 关闭类型标识
     * @return true表示可以跳转，false表示不能跳转
     */
    private boolean canJump(String closeType) {
        Object openType = mDeskResourceData.bottomFloatingInfo.get("openType");
        if (openType instanceof String) {
            return ((String) openType).contains(closeType);
        }
        return false;
    }

    /**
     * 手势监听器内部类
     *
     * 负责检测用户在锁屏浮窗上的滑动手势，特别是向上滑动的解锁手势
     * 使用弱引用避免内存泄漏，继承SimpleOnGestureListener简化实现
     */
    private static class GestureListener extends GestureDetector.SimpleOnGestureListener {
        /** 向上滑动的距离阈值（像素），超过此值认为是有效滑动 */
        private static final int SWIPE_THRESHOLD = 100;
        /** 向上滑动的速度阈值（像素/秒），超过此值认为是快速滑动 */
        private static final int SWIPE_VELOCITY_THRESHOLD = 100;

        /** 锁屏浮窗对象的弱引用，避免内存泄漏 */
        private final WeakReference<LockDeskFloatWin> lockDeskFloatWinRef;

        /**
         * 构造方法
         *
         * @param lockDeskFloatWin 锁屏浮窗实例
         */
        public GestureListener(LockDeskFloatWin lockDeskFloatWin) {
            // 使用弱引用持有浮窗对象，防止内存泄漏
            this.lockDeskFloatWinRef = new WeakReference<>(lockDeskFloatWin);
        }

        /**
         * 处理快速滑动手势
         *
         * 检测用户的向上滑动手势，当满足距离和速度阈值时，触发解锁和跳转操作
         *
         * @param e1 手势开始时的MotionEvent
         * @param e2 手势结束时的MotionEvent
         * @param velocityX X轴方向的滑动速度
         * @param velocityY Y轴方向的滑动速度
         * @return true表示手势已处理，false表示手势未处理
         */
        @Override
        public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
            // 计算滑动的距离
            float diffY = e2.getY() - e1.getY();  // Y轴滑动距离（负值表示向上滑动）
            float diffX = e2.getX() - e1.getX();  // X轴滑动距离

            // 获取浮窗对象引用
            LockDeskFloatWin lockDeskFloatWin = lockDeskFloatWinRef.get();
            if (lockDeskFloatWin == null) {
                return false;
            }

            // 记录滑动参数用于调试
            lockDeskFloatWin.log("diffY: " + diffY + ", diffX: " + diffX +  ", velocityY: "+ velocityY);

            // 判断是否为主要的垂直滑动（Y轴滑动距离大于X轴）
            if (Math.abs(diffY) > Math.abs(diffX)) {
                // 检查是否为向上滑动且满足距离和速度阈值
                if (diffY < -SWIPE_THRESHOLD && Math.abs(velocityY) > SWIPE_VELOCITY_THRESHOLD) {
                    // 检测到有效的向上滑动手势
                    Activity activity = lockDeskFloatWin.getActiveActivity();
                    if (activity != null) {
                        // 尝试解锁屏幕
                        lockDeskFloatWin.showAuthenticationScreen(activity);

                        // 根据配置决定是否执行跳转
                        if (lockDeskFloatWin.canJump(TYPE_SLIDE_UP)) {
                            lockDeskFloatWin.closeClick("SLIDE_UP");    // 执行跳转
                        } else {
                            lockDeskFloatWin.removeView("SLIDE_UP");    // 直接关闭浮窗
                        }
                    }
                    return true; // 表示手势已处理
                }
            }
            return false; // 手势未处理
        }
    }

    /**
     * 获取日志标签
     *
     * @return 日志标签字符串
     */
    protected String getTag() {
        return TAG;
    }

    /**
     * 记录调试日志
     *
     * 统一的日志记录方法，使用类的TAG标签
     *
     * @param info 要记录的日志信息
     */
    protected void log(String info) {
        LogUtils.d(getTag(), info);
    }
}
