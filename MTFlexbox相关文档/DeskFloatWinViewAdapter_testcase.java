package com.meituan.android.hades.dyadater.desk.ui;

import android.support.annotation.Keep;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.meituan.android.dynamiclayout.controller.presenter.TemplateData;
import com.meituan.android.hades.dyadater.desk.IFlexViewClickEventListenerAdapter;
import com.meituan.android.hades.dyadater.desk.IFlexViewShowStatusListenerAdapter;
import com.oid.hades.impl.R;
import com.meituan.android.qtitans.container.qqflex.IFlexCardShowStatusListener;
import com.meituan.android.qtitans.container.qqflex.QtitansFlexCardView;
import com.meituan.crashreporter.CrashReporter;
import com.meituan.crashreporter.CrashReporterConfig;

import org.json.JSONObject;

import java.util.HashMap;

/**
 * 桌面浮窗视图适配器
 *
 * 这个类是一个工具适配器，用于提供各种类型浮窗所需的布局、资源ID和通用方法。
 * 主要功能包括：
 * 1. 提供顶部浮窗相关的布局和控件ID
 * 2. 提供底部浮窗相关的布局和控件ID
 * 3. 提供中心浮窗、全屏浮窗、胶囊浮窗等各种浮窗类型的资源
 * 4. 提供浮窗内容的动态加载和事件处理方法
 * 5. 支持多种品牌手机的浮窗适配（华为、小米、VIVO、OPPO等）
 *
 * 使用 @Keep 注解保护类不被混淆，确保在混淆后仍能正常工作
 * 所有方法均为静态方法，提供统一的资源访问接口
 */
@Keep
public class DeskFloatWinViewAdapter {

    // ============================================
    // TopFloatWin 顶部浮窗相关资源和ID
    // ============================================

    /**
     * 获取顶部向上滑出动画资源ID
     *
     * @return 顶部浮窗消失时使用的向上滑出动画资源ID
     */
    public static int get_slide_out_to_top() {
        return R.anim.slide_out_to_top;
    }

    /**
     * 获取顶部浮窗图标控件ID
     *
     * @return 顶部浮窗中显示应用图标的ImageView控件ID
     */
    public static int get_top_floatwin_icon() {
        return R.id.top_floatwin_icon;
    }

    /**
     * 获取顶部浮窗插图控件ID
     *
     * @return 顶部浮窗中显示插图内容的ImageView控件ID
     */
    public static int get_top_floatwin_illustration() {
        return R.id.top_floatwin_illustration;
    }

    /**
     * 获取顶部浮窗应用名称控件ID
     *
     * @return 顶部浮窗中显示应用名称的TextView控件ID
     */
    public static int get_top_floatwin_name() {
        return R.id.top_floatwin_name;
    }

    /**
     * 获取顶部浮窗标题控件ID
     *
     * @return 顶部浮窗中显示标题的TextView控件ID
     */
    public static int get_top_floatwin_title() {
        return R.id.top_floatwin_title;
    }

    /**
     * 获取顶部浮窗内容控件ID
     *
     * @return 顶部浮窗中显示主要内容的TextView控件ID
     */
    public static int get_top_floatwin_content() {
        return R.id.top_floatwin_content;
    }

    /**
     * 获取浮窗基础布局控件ID
     *
     * @return 浮窗的基础容器布局控件ID
     */
    public static int get_layout_floatwin_base() {
        return R.id.layout_floatwin_base;
    }

    /**
     * 获取华为荣耀手机适配的顶部多元素浮窗布局资源ID
     *
     * @return 针对华为荣耀手机优化的顶部多元素浮窗布局资源ID
     */
    public static int get_hades_top_floatwin_multi_element_honor() {
        return R.layout.hades_top_floatwin_multi_element_honor;
    }

    /**
     * 获取华为手机适配的顶部多元素浮窗布局资源ID
     *
     * @return 针对华为手机优化的顶部多元素浮窗布局资源ID
     */
    public static int get_hades_top_floatwin_multi_element_huawei() {
        return R.layout.hades_top_floatwin_multi_element_huawei;
    }

    /**
     * 获取小米手机适配的顶部多元素浮窗布局资源ID
     *
     * @return 针对小米手机优化的顶部多元素浮窗布局资源ID
     */
    public static int get_hades_top_floatwin_multi_element_xiaomi() {
        return R.layout.hades_top_floatwin_multi_element_xiaomi;
    }

    /**
     * 获取VIVO手机适配的顶部多元素浮窗布局资源ID
     *
     * @return 针对VIVO手机优化的顶部多元素浮窗布局资源ID
     */
    public static int get_hades_top_floatwin_multi_element_vivo() {
        return R.layout.hades_top_floatwin_multi_element_vivo;
    }

    /**
     * 获取OPPO手机适配的顶部多元素浮窗布局资源ID
     *
     * @return 针对OPPO手机优化的顶部多元素浮窗布局资源ID
     */
    public static int get_hades_top_floatwin_multi_element_oppo() {
        return R.layout.hades_top_floatwin_multi_element_oppo;
    }

    /**
     * 获取顶部可滚动浮窗布局资源ID
     *
     * @return 支持水平滚动的顶部浮窗布局资源ID
     */
    public static int get_hades_top_scroll_floatwin() {
        return R.layout.hades_top_scroll_floatwin;
    }

    /**
     * 获取顶部垂直滚动浮窗布局资源ID
     *
     * @return 支持垂直滚动的顶部浮窗布局资源ID
     */
    public static int get_hades_top_scroll_y_floatwin() {
        return R.layout.hades_top_scroll_y_floatwin;
    }

    /**
     * 获取基础顶部浮窗布局资源ID
     *
     * @return 基础的顶部浮窗布局资源ID
     */
    public static int get_hades_top_floatwin() {
        return R.layout.hades_top_floatwin;
    }

    /**
     * 获取关闭推送按钮控件ID
     *
     * @return 用于关闭推送消息的按钮控件ID
     */
    public static int get_close_push() {
        return R.id.close_push;
    }

    /**
     * 获取关闭推送按钮背景控件ID
     *
     * @return 关闭推送按钮的背景容器控件ID
     */
    public static int get_close_push_background() {
        return R.id.close_push_background;
    }

    /**
     * 获取关闭推送桌面图像控件ID
     *
     * @return 关闭推送时显示的桌面图像控件ID
     */
    public static int get_close_push_desk_image() {
        return R.id.close_push_desk_image;
    }

    /**
     * 获取关闭推送多元素控件ID
     *
     * @return 关闭推送时的多元素容器控件ID
     */
    public static int close_push_multi_element() {
        return R.id.close_push_multi_element;
    }

    /**
     * 获取多元素线性布局控件ID
     *
     * @return 用于包含多个元素的LinearLayout控件ID
     */
    public static int get_ll_multi_element() {
        return R.id.ll_multi_element;
    }

    /**
     * 获取浮窗根视图控件ID
     *
     * @return 浮窗的根容器视图控件ID
     */
    public static int getFloatWinRootViewId() {
        return R.id.floatwin_root;
    }

    /**
     * 获取桌面图像视图控件ID
     *
     * @return 显示桌面相关图像的ImageView控件ID
     */
    public static int getDeskImageViewId() {
        return R.id.floatwin_desk_image;
    }

    /**
     * 获取浮窗关闭按钮背景图片资源ID
     *
     * @return 浮窗关闭按钮的背景图片资源ID
     */
    public static int get_float_win_close_background() {
        return R.drawable.float_win_close_background;
    }

    /**
     * 获取浮窗关闭后的背景图片资源ID
     *
     * @return 浮窗关闭后显示的背景图片资源ID
     */
    public static int get_float_win_close_after_background() {
        return R.drawable.float_win_close_after_background;
    }

    // ============================================
    // BottomFloatWin 底部浮窗相关资源和ID
    // ============================================

    /**
     * 获取底部浮窗布局资源ID
     *
     * @return 底部浮窗的主要布局资源ID
     */
    public static int get_hades_bottom_floatwin() {
        return R.layout.hades_bottom_floatwin;
    }

    /**
     * 获取浮窗X关闭按钮控件ID
     *
     * @return 浮窗右上角X形关闭按钮的控件ID
     */
    public static int get_floatwin_x_btn() {
        return R.id.floatwin_x_btn;
    }

    /**
     * 获取浮窗通用关闭按钮控件ID
     *
     * @return 浮窗通用关闭按钮的控件ID，当前返回0表示暂未使用
     */
    public static int get_floatwin_close_btn() {
        return 0;
    }

    // ============================================
    // CenterFloatWin 中心浮窗相关资源和ID
    // ============================================

    /**
     * 获取中心浮窗布局资源ID
     *
     * @return 显示在屏幕中央的浮窗布局资源ID
     */
    public static int get_hades_center_floatwin() {
        return R.layout.hades_center_floatwin;
    }

    // ============================================
    // FullSrcFloatWin 全屏浮窗相关资源和ID
    // ============================================

    /**
     * 获取全屏浮窗布局资源ID
     *
     * @return 全屏显示的浮窗Activity布局资源ID
     */
    public static int get_hades_full_floatwin() {
        return R.layout.hades_full_act_layout;
    }

    /**
     * 获取全屏浮窗背景控件ID
     *
     * @return 全屏浮窗的背景容器控件ID
     */
    public static int get_full_scr_background() {
        return R.id.full_scr_background;
    }

    /**
     * 获取全屏浮窗遮罩控件ID
     *
     * @return 全屏浮窗的半透明遮罩层控件ID
     */
    public static int get_full_scr_mask() {
        return R.id.full_scr_mask;
    }

    /**
     * 获取全屏浮窗中心图像控件ID
     *
     * @return 全屏浮窗中央显示的图像控件ID
     */
    public static int get_full_desk_center_image() {
        return R.id.desk_center_image;
    }

    /**
     * 获取全屏浮窗底部图像控件ID
     *
     * @return 全屏浮窗底部显示的图像控件ID
     */
    public static int get_full_desk_bottom_image() {
        return R.id.desk_bottom_image;
    }

    /**
     * 获取全屏浮窗容器视图控件ID
     *
     * @return 全屏浮窗的主要内容容器控件ID
     */
    public static int get_full_container_view() {
        return R.id.full_container_view;
    }

    /**
     * 获取全屏浮窗操作区域控件ID
     *
     * @return 全屏浮窗中可以进行操作的区域控件ID
     */
    public static int get_full_action_area() {
        return R.id.action_area;
    }

    /**
     * 获取全屏浮窗非操作区域控件ID
     *
     * @return 全屏浮窗中不可操作的区域控件ID
     */
    public static int get_full_no_action_area() {
        return R.id.no_action_area;
    }

    /**
     * 获取全屏浮窗关闭图像控件ID
     *
     * @return 全屏浮窗的关闭按钮图像控件ID
     */
    public static int get_full_close_image() {
        return R.id.close_image;
    }

    /**
     * 获取全屏浮窗根布局控件ID
     *
     * @return 全屏浮窗的根布局容器控件ID
     */
    public static int get_full_root_layout() {
        return R.id.full_scr_root_layout;
    }

    // 已注释的全屏对话框ID，保留以备将来使用
    // public static int get_full_scr_dialog() {
    //     return R.id.full_scr_dialog;
    // }

    /**
     * 获取全屏浮窗左侧关闭图像控件ID
     *
     * @return 全屏浮窗左侧关闭按钮的控件ID
     */
    public static int get_full_close_image_left() {
        return R.id.close_image_left;
    }

    /**
     * 获取全屏浮窗右侧关闭图像控件ID
     *
     * @return 全屏浮窗右侧关闭按钮的控件ID
     */
    public static int get_full_close_image_right() {
        return R.id.close_image_right;
    }

    /**
     * 获取全屏浮窗底部关闭图像控件ID
     *
     * @return 全屏浮窗底部关闭按钮的控件ID
     */
    public static int get_full_close_image_bottom() {
        return R.id.close_image_bottom;
    }

    /**
     * 获取全屏浮窗右下角关闭图像控件ID
     *
     * @return 全屏浮窗右下角关闭按钮的控件ID
     */
    public static int get_full_close_image_bottom_right() {
        return R.id.close_image_bottom_right;
    }

    /**
     * 获取全屏浮窗左下角关闭图像控件ID
     *
     * @return 全屏浮窗左下角关闭按钮的控件ID
     */
    public static int get_full_close_image_bottom_left() {
        return R.id.close_image_bottom_left;
    }

    /**
     * 获取关闭图标资源ID
     *
     * @return 通用的关闭图标drawable资源ID
     */
    public static int get_ic_close() {
        return R.drawable.hades_ic_close_center;
    }

    // ============================================
    // AbsFloatWin 抽象浮窗相关资源和ID
    // ============================================

    // 以下是已注释的抽象浮窗相关ID，保留以备将来扩展使用
    // public static int get_floatwin_close_stub() {
    //     return R.id.floatwin_close_stub;
    // }
    //
    // public static int get_floatwin_close_layout() {
    //     return R.id.floatwin_close_layout;
    // }
    //
    // public static int get_text_close_temp() {
    //     return R.id.text_close_temp;
    // }
    //
    // public static int get_text_close_7days() {
    //     return R.id.text_close_7days;
    // }
    //
    // public static int get_text_feedback() {
    //     return R.id.text_feedback;
    // }

    /**
     * 获取QQ样式缩放动画资源ID
     *
     * @return QQ风格的缩放动画资源ID，用于浮窗的显示效果
     */
    public static int get_qq_scale_anim() {
        return R.anim.qq_scale_anim;
    }

    /**
     * 获取粘性浮窗布局资源ID
     *
     * @return 粘性（置顶）浮窗的布局资源ID
     */
    public static int get_hades_sticky_floatwin() {
        return R.layout.hades_sticky_floatwin;
    }

    /**
     * 获取从顶部滑入动画资源ID
     *
     * @return 浮窗从屏幕顶部滑入的动画资源ID
     */
    public static int get_slide_in_from_top() {
        return R.anim.slide_in_from_top;
    }

    // ============================================
    // CapsuleFloatWin 胶囊浮窗相关资源和ID
    // ============================================

    /**
     * 获取胶囊浮窗屏幕背景控件ID
     *
     * @return 胶囊浮窗的屏幕背景容器控件ID
     */
    public static int get_capsule_scr_background() {
        return R.id.capsule_scr_background;
    }

    /**
     * 获取黄色胶囊控件ID
     *
     * @return 黄色胶囊样式的浮窗控件ID
     */
    public static int get_yellow_capsule() {
        return R.id.yellow_capsule;
    }

    /**
     * 获取胶囊图像控件ID
     *
     * @return 胶囊浮窗中显示的图像控件ID
     */
    public static int get_capsule_image() {
        return R.id.capsule_image;
    }

    /**
     * 获取等待支付文本控件ID
     *
     * @return 显示"等待支付"文本的TextView控件ID
     */
    public static int get_wait_pay_text() {
        return R.id.wait_pay_text;
    }

    /**
     * 获取等待支付时间控件ID
     *
     * @return 显示支付倒计时的TextView控件ID
     */
    public static int get_wait_pay_time() {
        return R.id.wait_pay_time;
    }

    /**
     * 获取底部窗口控件ID
     *
     * @return 胶囊浮窗底部弹窗的控件ID
     */
    public static int get_bottom_win() {
        return R.id.bottom_win;
    }

    /**
     * 获取关闭提醒控件ID
     *
     * @return 关闭提醒按钮的容器控件ID
     */
    public static int get_close_remind() {
        return R.id.close_remind;
    }

    /**
     * 获取关闭提醒文本控件ID
     *
     * @return 关闭提醒的文本显示控件ID
     */
    public static int get_tv_close_remind() {
        return R.id.tv_close_remind;
    }

    /**
     * 获取关闭提醒图标控件ID
     *
     * @return 关闭提醒的图标显示控件ID
     */
    public static int get_iv_close_remind() {
        return R.id.iv_close_remind;
    }

    /**
     * 获取查看订单控件ID
     *
     * @return 查看订单按钮的容器控件ID
     */
    public static int get_check_order() {
        return R.id.check_order;
    }

    /**
     * 获取查看订单文本控件ID
     *
     * @return 查看订单的文本显示控件ID
     */
    public static int get_check_order_tv() {
        return R.id.check_order_tv;
    }

    /**
     * 获取查看订单图标控件ID
     *
     * @return 查看订单的图标显示控件ID
     */
    public static int get_check_order_iv() {
        return R.id.check_order_iv;
    }

    /**
     * 获取胶囊间距控件ID
     *
     * @return 胶囊元素之间的间距控件ID
     */
    public static int get_capsule_space() {
        return R.id.capsule_space;
    }

    /**
     * 获取胶囊Activity布局资源ID
     *
     * @return 胶囊浮窗Activity的主布局资源ID
     */
    public static int get_hades_capsule_act_layout() {
        return R.layout.hades_capsule_act_layout;
    }

    // ============================================
    // TransFloatWin 透明浮窗相关资源和ID
    // ============================================

    /**
     * 获取透明浮窗布局资源ID
     *
     * @return 透明浮窗的主布局资源ID
     */
    public static int get_hades_floatwin_trans() {
        return R.layout.hades_floatwin_trans;
    }

    /**
     * 获取顶部区域线性布局控件ID
     *
     * @return 透明浮窗顶部区域的LinearLayout控件ID
     */
    public static int get_ll_top_area() {
        return R.id.ll_top_area;
    }

    /**
     * 获取热点区域线性布局控件ID
     *
     * @return 透明浮窗热点操作区域的LinearLayout控件ID
     */
    public static int get_ll_hot_area() {
        return R.id.ll_hot_area;
    }

    /**
     * 获取底部区域线性布局控件ID
     *
     * @return 透明浮窗底部区域的LinearLayout控件ID
     */
    public static int get_ll_bottom_area() {
        return R.id.ll_bottom_area;
    }

    // ============================================
    // ScreenShotView 截屏视图相关资源和ID
    // ============================================

    /**
     * 获取截屏弹窗布局资源ID
     *
     * @return 截屏检测弹窗的主布局资源ID
     */
    public static int get_hades_screen_shot_layout() {
        return R.layout.screen_shot_popup_layout;
    }

    /**
     * 获取截屏弹窗根布局控件ID
     *
     * @return 截屏弹窗的根容器布局控件ID
     */
    public static int get_hades_screen_shot_root() {
        return R.id.ss_root;
    }

    /**
     * 获取截屏图像显示控件ID
     *
     * @return 用于显示截屏内容的ImageView控件ID
     */
    public static int get_screen_shot_image() {
        return R.id.screenShotImage;
    }

    /**
     * 获取反馈按钮控件ID
     *
     * @return 截屏弹窗中反馈按钮的控件ID
     */
    public static int get_ss_feedback() {
        return R.id.ss_feedback;
    }

    /**
     * 获取免打扰按钮控件ID
     *
     * @return 截屏弹窗中设置免打扰的按钮控件ID
     */
    public static int get_ss_no_disturb() {
        return R.id.ss_no_disturb;
    }

    /**
     * 获取推送设置按钮控件ID
     *
     * @return 截屏弹窗中推送设置按钮的控件ID
     */
    public static int get_ss_push_setting() {
        return R.id.ss_push_setting;
    }

    /**
     * 获取截屏弹窗关闭按钮控件ID
     *
     * @return 截屏弹窗中关闭按钮的控件ID
     */
    public static int get_ss_close() {
        return R.id.ss_close;
    }

    // ============================================
    // BottomDialogFloatWin 底部对话框浮窗相关资源和ID
    // ============================================

    /**
     * 获取底部对话框浮窗Activity布局资源ID
     *
     * @return 底部对话框类型浮窗的Activity布局资源ID
     */
    public static int get_hades_bottom_dialog_floatwin() {
        return R.layout.hades_act_wc_layout;
    }

    /**
     * 获取对话框根布局控件ID
     *
     * @return 底部对话框浮窗的根布局容器控件ID
     */
    public static int get_hades_dialog_root_layout() {
        return R.id.dialog_root_layout;
    }

    /**
     * 获取关闭区域视图控件ID
     *
     * @return 底部对话框浮窗顶部关闭区域的控件ID
     */
    public static int get_hades_close_view() {
        return R.id.top_close_area;
    }

    /**
     * 获取中心图像控件ID
     *
     * @return 底部对话框浮窗中央图像显示的控件ID
     */
    public static int get_hades_center_image() {
        return R.id.lock_center_image;
    }

    /**
     * 获取底部对话框视图控件ID
     *
     * @return 底部对话框浮窗主要内容视图的控件ID
     */
    public static int get_hades_bottom_dialog_view() {
        return R.id.bottom_dialog_view;
    }

    /**
     * 获取对话框打开动画资源ID
     *
     * @return 底部对话框浮窗打开时使用的动画资源ID
     */
    public static int get_dialog_open_anim() {
        return R.anim.dialog_open;
    }

    /**
     * 获取锁屏桌面Activity布局资源ID
     *
     * @return 锁屏状态下桌面浮窗的Activity布局资源ID
     */
    public static int get_hades_lock_desk_act() {
        return R.layout.hades_lock_desk_act;
    }

    /**
     * 获取锁屏根布局控件ID
     *
     * @return 锁屏浮窗的根布局容器控件ID
     */
    public static int get_hades_lock_root_layout() {
        return R.id.lock_root_layout;
    }

    /**
     * 获取锁屏浮窗背景控件ID
     *
     * @return 锁屏浮窗的背景容器控件ID
     */
    public static int get_hades_lock_win_background() {
        return R.id.lock_win_background;
    }

    /**
     * 获取锁屏浮窗容器控件ID
     *
     * @return 锁屏浮窗的主要内容容器控件ID
     */
    public static int get_hades_lock_win_container() {
        return R.id.lock_win_container;
    }

    /**
     * 获取时间显示文本控件ID
     *
     * @return 锁屏浮窗中显示时间的TextView控件ID
     */
    public static int get_time_text_view() {
        return R.id.timeTextView;
    }

    /**
     * 获取日期显示文本控件ID
     *
     * @return 锁屏浮窗中显示日期的TextView控件ID
     */
    public static int get_date_text_view() {
        return R.id.dateTextView;
    }

    /**
     * 添加底部对话框浮窗的内容（简化版本）
     *
     * 这个方法用于向底部对话框浮窗中动态添加内容。它创建一个基于Flex布局的卡片视图，
     * 根据传入的数据配置显示内容，并设置点击事件监听器。
     *
     * 主要功能：
     * 1. 初始化崩溃上报器，确保异常情况下的稳定性
     * 2. 创建QtitansFlexCardView来承载动态内容
     * 3. 设置布局参数为MATCH_PARENT宽度和WRAP_CONTENT高度
     * 4. 将卡片视图添加到容器中
     * 5. 转换数据格式并设置到卡片视图
     * 6. 设置点击事件监听器处理用户交互
     *
     * @param containerView 承载浮窗内容的FrameLayout容器
     * @param dialogData 包含浮窗显示数据的HashMap，将被转换为JSON格式
     * @param listener 处理用户点击事件的监听器适配器
     */
    public static void addBottomDialogContent(FrameLayout containerView, HashMap<String, Object> dialogData, IFlexViewClickEventListenerAdapter listener) {
        // 初始化崩溃上报器，使用默认配置确保运行时异常能被捕获和上报
        CrashReporter.getInstance().init(containerView.getContext(), new CrashReporterConfig(){});

        // 创建Flex卡片视图，用于显示动态布局内容
        QtitansFlexCardView cardView  = new QtitansFlexCardView(containerView.getContext());

        // 设置布局参数：宽度匹配父容器，高度根据内容自适应
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        cardView.setLayoutParams(layoutParams);

        // 将卡片视图添加到容器中
        containerView.addView(cardView);

        // 将HashMap数据转换为JSONObject格式，供Flex布局引擎使用
        JSONObject data = new JSONObject(dialogData);

        // 显示视图内容
        cardView.showView(data);

        // 设置点击事件监听器，处理用户的点击交互
        cardView.setClickEventListener(listener::handleClickEvent);

        // 重复设置点击监听器（可能是代码冗余，保留原有逻辑）
        cardView.setClickEventListener(listener::handleClickEvent);
    }

    /**
     * 添加底部对话框浮窗的内容（完整版本）
     *
     * 这是addBottomDialogContent的完整版本，除了处理点击事件外，还支持视图显示状态的监听。
     * 相比简化版本，这个方法增加了对浮窗显示成功、失败状态的监听和回调处理。
     *
     * 主要功能：
     * 1. 初始化崩溃上报器确保稳定性
     * 2. 创建并配置QtitansFlexCardView
     * 3. 设置点击事件监听器
     * 4. 设置显示状态监听器，包括成功和失败的回调
     * 5. 显示动态内容
     *
     * @param containerView 承载浮窗内容的FrameLayout容器
     * @param dialogData 包含浮窗显示数据的HashMap
     * @param clickEventListenerAdapter 处理用户点击事件的监听器适配器
     * @param showStatusListenerAdapter 处理浮窗显示状态变化的监听器适配器
     */
    public static void addBottomDialogContent(FrameLayout containerView, HashMap<String, Object> dialogData,
                                              IFlexViewClickEventListenerAdapter clickEventListenerAdapter,
                                              IFlexViewShowStatusListenerAdapter showStatusListenerAdapter) {
        // 初始化崩溃上报器
        CrashReporter.getInstance().init(containerView.getContext(), new CrashReporterConfig(){});

        // 创建Flex卡片视图
        QtitansFlexCardView cardView  = new QtitansFlexCardView(containerView.getContext());

        // 设置布局参数
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        cardView.setLayoutParams(layoutParams);

        // 添加到容器
        containerView.addView(cardView);

        // 转换数据格式
        JSONObject data = new JSONObject(dialogData);

        // 设置点击事件监听器
        cardView.setClickEventListener(clickEventListenerAdapter::handleClickEvent);

        // 设置显示状态监听器，监听浮窗内容的显示状态变化
        cardView.setShowStatusListener(new IFlexCardShowStatusListener() {
            /**
             * 当浮窗内容成功显示时的回调
             *
             * @param data 模板数据
             * @param viewChanged 视图是否发生了变化
             */
            @Override
            public void onShow(TemplateData data, boolean viewChanged) {
                // 通知外部监听器显示成功
                showStatusListenerAdapter.onShow();
            }

            /**
             * 当浮窗内容显示失败时的回调
             *
             * @param data 模板数据
             */
            @Override
            public void onFailed(TemplateData data) {
                // 通知外部监听器显示失败
                showStatusListenerAdapter.onFailed();
            }
        });

        // 显示视图内容
        cardView.showView(data);
    }
}
